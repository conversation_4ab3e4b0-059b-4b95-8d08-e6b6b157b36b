//const { textModeration } = require('./tencent-text-moderation');
const { getConfig } = require('../config');

class ContentModerationService {
  constructor() {
    this.baseUrl = getConfig().baseUrl;
  }

  // 检查内容是否包含敏感词
  async checkContent(content) {
    try {
      // 1. 先调用后端敏感词检查
      const backendResult = await this.checkWithBackend(content);
      
      // 如果后端检测到敏感词，直接返回结果
      if (backendResult.has_sensitive) {
        return {
          success: false,
          error: `内容包含敏感词: ${backendResult.found_words.join(', ')}`,
          words: backendResult.found_words
        };
      }

      // // 2. 如果需要腾讯云检测
      // if (backendResult.should_check_tencent) {
      //   const tencentResult = await textModeration.checkText(content);
        
      //   // 如果腾讯云检测失败
      //   if (!tencentResult.success) {
      //     return tencentResult;
      //   }

      //   // 如果腾讯云检测到违规内容
      //   if (tencentResult.suggestion === 'Block') {
      //     // 将腾讯云检测到的敏感词添加到后端敏感词库
      //     if (tencentResult.keywords && tencentResult.keywords.length > 0) {
      //       await this.addTencentSensitiveWords(tencentResult.keywords);
      //     }

      //     return {
      //       success: false,
      //       error: `内容包含违规词语: ${tencentResult.keywords.join(', ')}，请修改后重试`,
      //       words: tencentResult.keywords
      //     };
      //   } else if (tencentResult.suggestion === 'Review') {
      //     return {
      //       success: false,
      //       error: '评论内容需要调整，建议：\n1. 避免使用特殊符号或表情\n2. 不要包含联系方式\n3. 使用更简单的表达方式\n4. 避免使用网络用语或谐音字',
      //       suggestion: 'review'
      //     };
      //   }
      // }

      // 所有检查都通过
      return {
        success: true
      };

    } catch (error) {
      console.error('内容检测失败:', error);
      return {
        success: false,
        error: error.message || '内容检测失败，请重试'
      };
    }
  }

  // 调用后端敏感词检查接口
  async checkWithBackend(content) {
    const res = await new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/api/sensitive/check/`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          content: content
        },
        success: res => resolve(res),
        fail: err => reject(err)
      });
    });

    if (!res.data || !res.data.success) {
      throw new Error(res.data?.message || '后端敏感词检查失败');
    }

    return res.data;
  }

  // // 将腾讯云检测到的敏感词添加到后端敏感词库
  // async addTencentSensitiveWords(words) {
  //   try {
  //     const wordsData = words.map(word => ({
  //       word: word,
  //       remark: '腾讯云检测发现'
  //     }));

  //     const res = await new Promise((resolve, reject) => {
  //       wx.request({
  //         url: `${this.baseUrl}/api/sensitive/batch_add/`,
  //         method: 'POST',
  //         header: {
  //           'Content-Type': 'application/json'
  //         },
  //         data: {
  //           words: wordsData
  //         },
  //         success: res => resolve(res),
  //         fail: err => reject(err)
  //       });
  //     });

  //     if (!res.data || !res.data.success) {
  //       console.error('添加敏感词失败:', res.data?.message);
  //     }
  //   } catch (error) {
  //     console.error('添加敏感词到后端失败:', error);
  //   }
  // }
}

// 导出实例
const contentModeration = new ContentModerationService();

module.exports = {
  contentModeration
}; 