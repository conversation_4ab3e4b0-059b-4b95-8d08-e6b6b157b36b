const { getConfig } = require('../../config')

// 获取应用实例
const app = getApp()

Page({
  data: {
    feedbackList: [], // 反馈列表
    currentPage: 1, // 当前页码
    pageSize: 10, // 每页数量
    totalCount: 0, // 总数据条数
    totalPages: 0, // 总页数
    pageNumbers: [], // 显示的页码数组
    isLoading: false, // 是否正在加载
  },

  onLoad() {
    // 页面加载时，不立即请求数据
  },

  onReady() {
    // 页面渲染完成后，再请求数据
    this.loadFeedbackList()
  },

  onShow() {
    // 页面显示
  },

  // 计算要显示的页码
  calculatePageNumbers(current, total) {
    const pages = []
    const maxPages = 5 // 最多显示5个页码
    
    if (total <= maxPages) {
      // 总页数小于等于5，显示所有页码
      for (let i = 1; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 总页数大于5，显示部分页码
      if (current <= 3) {
        // 当前页靠前
        for (let i = 1; i <= 5; i++) {
          pages.push(i)
        }
      } else if (current >= total - 2) {
        // 当前页靠后
        for (let i = total - 4; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // 当前页在中间
        for (let i = current - 2; i <= current + 2; i++) {
          pages.push(i)
        }
      }
    }
    return pages
  },

  // 加载反馈列表
  async loadFeedbackList() {
    if (this.data.isLoading) return
    
    try {
      this.setData({ isLoading: true })
      
      const { currentPage, pageSize } = this.data
      const baseUrl = getConfig().baseUrl
      
      const result = await new Promise((resolve, reject) => {
        setTimeout(() => {
          wx.request({
            url: `${baseUrl}/api/feedback/`,
            method: 'GET',
            data: {
              page: currentPage,
              page_size: pageSize
            },
            success: resolve,
            fail: reject
          })
        }, 100)
      })

      if (result.statusCode === 200) {
        const responseData = result.data.results || []
        const totalCount = result.data.count || 0
        const totalPages = Math.ceil(totalCount / pageSize)
        
        const newList = responseData.map(item => ({
          ...item,
          created_at: this.formatDate(item.created_at),
          updated_at: this.formatDate(item.updated_at)
        }))

        const pageNumbers = this.calculatePageNumbers(currentPage, totalPages)

        this.setData({
          feedbackList: newList,
          totalCount,
          totalPages,
          pageNumbers
        })
      } else {
        wx.showToast({
          title: result.data?.message || '加载失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('加载反馈列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 处理页码点击
  handlePageClick(e) {
    const page = e.currentTarget.dataset.page
    if (page !== this.data.currentPage) {
      this.setData({ currentPage: page }, () => {
        this.loadFeedbackList()
      })
    }
  },

  // 处理上一页
  handlePrevPage() {
    if (this.data.currentPage > 1) {
      this.setData({
        currentPage: this.data.currentPage - 1
      }, () => {
        this.loadFeedbackList()
      })
    }
  },

  // 处理下一页
  handleNextPage() {
    if (this.data.currentPage < this.data.totalPages) {
      this.setData({
        currentPage: this.data.currentPage + 1
      }, () => {
        this.loadFeedbackList()
      })
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      currentPage: 1
    }, () => {
      this.loadFeedbackList()
      wx.stopPullDownRefresh()
    })
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '反馈列表 - 查看用户反馈进度',
      path: '/pages/feedback-list/feedback-list'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '反馈列表 - 实时跟进反馈处理进度',
      query: ''
    };
  }
}) 