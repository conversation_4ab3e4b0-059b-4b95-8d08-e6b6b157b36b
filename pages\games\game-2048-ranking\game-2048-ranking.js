// pages/games/game-2048-ranking/game-2048-ranking.js
const app = getApp();

// 引入API工具类
const api = require('../../../utils/api');

// 页面KEY标识
const PAGE_KEY = 'game-2048-ranking';

// API基础URL
const API_BASE_URL = 'https://pikario.site/api';

Page({
  data: {
    // 排行榜数据
    rankingList: [],
    userRank: null,
    activeTab: 'score', // 当前选中的标签：score(高分榜)、time(时间榜)、moves(步数榜)
    page: 1,
    limit: 20,
    hasMoreData: true,
    isLoading: false,
    listOpacity: 1,
    
    // 背景图片
    backgroundImage: '/images/bg.jpg',
    
    // VIP相关数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,
    
    // 用户信息
    userInfo: null,
    isLoggedIn: false
  },

  onLoad: function() {
    console.log('2048排行榜页面加载');
    
    // 设置背景图片
    this.setData({
      backgroundImage: '/images/bg.jpg'
    });
    
    // 初始化VIP状态
    this.initVipAndFreeCount();
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取排行榜数据
    this.fetchRanking();
  },
  
  onPullDownRefresh: function() {
    // 下拉刷新
    this.refreshRanking();
  },
  
  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays
    });
  },
  
  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    // 从缓存中获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo && token) {
      try {
        // 尝试解析用户信息
        const parsedUserInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;
        
        this.setData({
          userInfo: parsedUserInfo,
          isLoggedIn: true
        });
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }
  },
  
  /**
   * 切换标签
   */
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab !== this.data.activeTab) {
      // 设置透明度为0，实现淡出效果
      this.setData({
        listOpacity: 0
      });
      
      // 延迟切换标签，等待淡出动画完成
      setTimeout(() => {
        this.setData({
          activeTab: tab,
          rankingList: [],
          page: 1,
          hasMoreData: true
        });
        
        // 获取新标签的排行榜数据
        this.fetchRanking();
      }, 300);
    }
  },
  
  /**
   * 获取排行榜数据
   */
  fetchRanking: function() {
    // 如果正在加载或没有更多数据，不再请求
    if (this.data.isLoading || !this.data.hasMoreData) return;
    
    // 设置加载状态
    this.setData({
      isLoading: true
    });
    
    // 获取请求参数
    const { activeTab, page, limit } = this.data;
    
    // 获取排序方式
    let orderType = 'desc';
    if (activeTab === 'time' || activeTab === 'moves') {
      orderType = 'asc'; // 时间榜和步数榜按升序排列
    }
    
    // 发送请求
    wx.request({
      url: `${API_BASE_URL}/games/2048/rankings`,
      method: 'GET',
      data: {
        type: activeTab,
        order: orderType,
        page: page,
        limit: limit,
        openid: this.data.isLoggedIn ? (this.data.userInfo.openid || wx.getStorageSync('openid')) : '',
        timestamp: Date.now() // 添加时间戳避免缓存
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.code === 0) {
          const result = res.data.data || {};
          
          // 处理排行榜数据
          const rankList = result.rankList || [];
          
          // 标记当前用户
          if (this.data.isLoggedIn) {
            const openid = this.data.userInfo.openid || wx.getStorageSync('openid');
            rankList.forEach(item => {
              item.isCurrentUser = item.openid === openid;
            });
          }
          
          // 合并数据
          const newList = page === 1 ? rankList : [...this.data.rankingList, ...rankList];
          
          // 更新用户排名
          const userRank = result.userRank || null;
          
          this.setData({
            rankingList: newList,
            userRank: userRank,
            hasMoreData: rankList.length >= limit,
            page: page + 1,
            listOpacity: 1 // 设置透明度为1，实现淡入效果
          });
        } else {
          wx.showToast({
            title: '获取排行榜失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取排行榜失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isLoading: false
        });
        
        // 停止下拉刷新
        wx.stopPullDownRefresh();
      }
    });
  },
  
  /**
   * 加载更多排行榜数据
   */
  loadMoreRankings: function() {
    this.fetchRanking();
  },
  
  /**
   * 刷新排行榜
   */
  refreshRanking: function() {
    // 防止频繁刷新
    const now = Date.now();
    const lastRefreshTime = this._lastRefreshTime || 0;
    
    if (now - lastRefreshTime < 3000) { // 3秒内不允许重复刷新
      wx.showToast({
        title: '请勿频繁刷新',
        icon: 'none'
      });
      return;
    }
    
    // 记录本次刷新时间
    this._lastRefreshTime = now;
    
    // 重置数据
    this.setData({
      rankingList: [],
      page: 1,
      hasMoreData: true,
      listOpacity: 0 // 设置透明度为0，实现淡出效果
    });
    
    // 获取排行榜数据
    this.fetchRanking();
  },
  
  /**
   * 格式化时间
   * @param {number} seconds 秒数
   * @returns {string} 格式化后的时间
   */
  formatTime: function(seconds) {
    if (!seconds) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  },
  
  /**
   * 格式化日期
   * @param {number|string} timestamp 时间戳
   * @returns {string} 格式化后的日期
   */
  formatDate: function(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  },
  
  /**
   * 返回游戏页面
   */
  goBack: function() {
    wx.navigateBack();
  },
  
  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    this.setData({ showVipDialog: true });
  },
  
  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({ showVipDialog: false });
  },
  
  /**
   * 购买VIP事件
   */
  async onBuyVip() {
    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 获取当前VIP信息
      const currentVipInfo = wx.getStorageSync('vipInfo');
      let expireAt = new Date();

      // 如果已经是VIP，基于原过期时间增加31天
      if (currentVipInfo && currentVipInfo.is_valid_vip && currentVipInfo.vip_expire_at) {
        // 使用原过期时间作为基准
        expireAt = new Date(currentVipInfo.vip_expire_at);
      }

      // 增加31天
      expireAt.setDate(expireAt.getDate() + 31);

      // 调用设置VIP状态接口
      const vipInfo = await app.setVipStatus(true, expireAt.toISOString());

      // 更新页面状态
      this.setData({
        isVip: vipInfo.is_valid_vip || false,
        vipRemainingDays: vipInfo.remainingDays || 0,
        showVipDialog: false
      });

      // 根据是续费还是首次开通显示不同提示
      wx.showToast({
        title: currentVipInfo?.is_valid_vip ? 'VIP续费成功' : 'VIP开通成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('购买VIP失败:', error);
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
