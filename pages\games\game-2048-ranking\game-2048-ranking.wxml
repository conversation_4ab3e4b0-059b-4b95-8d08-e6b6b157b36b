<!--pages/games/game-2048-ranking/game-2048-ranking.wxml-->
<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 排行榜标题 -->
  <view class="header">
    <view class="title">2048排行榜</view>
    <view class="subtitle">看看谁是最强玩家</view>

    <!-- 使用统一的VIP徽章组件 -->
    <view class="vip-badge-container">
      <vip-badge id="vipBadge"
        pageType="common"
        isVip="{{isVip}}"
        freeCount="{{freeCount}}"
        remainingDays="{{vipRemainingDays}}"
        bindtap="onVipBadgeTap"
      />
    </view>
  </view>

  <!-- 排行榜类型切换 -->
  <view class="tabs-container">
    <view class="tab {{activeTab === 'score' ? 'active' : ''}}" bindtap="switchTab" data-tab="score">
      高分榜
    </view>
    <view class="tab {{activeTab === 'time' ? 'active' : ''}}" bindtap="switchTab" data-tab="time">
      时间榜
    </view>
    <view class="tab {{activeTab === 'moves' ? 'active' : ''}}" bindtap="switchTab" data-tab="moves">
      步数榜
    </view>
  </view>

  <!-- 我的排名 -->
  <view class="my-rank-container" wx:if="{{userRank}}">
    <view class="my-rank-title">我的排名</view>
    <view class="my-rank-content">
      <view class="rank-number">{{userRank.rank}}</view>
      <view class="user-info">
        <view class="user-name">{{userRank.nickname || '匿名用户'}}</view>
      </view>
      <view class="score-info">
        <view class="score-value">
          <block wx:if="{{activeTab === 'score'}}">{{userRank.score}}</block>
          <block wx:elif="{{activeTab === 'time'}}">{{formatTime(userRank.gameTime)}}</block>
          <block wx:elif="{{activeTab === 'moves'}}">{{userRank.moves}}步</block>
        </view>
        <view class="score-label">
          <block wx:if="{{activeTab === 'score'}}">分</block>
          <block wx:elif="{{activeTab === 'time'}}">用时</block>
          <block wx:elif="{{activeTab === 'moves'}}">步数</block>
        </view>
      </view>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <scroll-view class="ranking-list" scroll-y="true" bindscrolltolower="loadMoreRankings" style="opacity: {{listOpacity}};">
    <view class="ranking-item {{item.isCurrentUser ? 'current-user' : ''}}"
          wx:for="{{rankingList}}"
          wx:key="id">
      <!-- 排名 -->
      <view class="rank-cell">
        <view class="rank-number {{index < 3 ? 'top-rank' : ''}}">{{item.rank}}</view>
      </view>

      <!-- 用户信息 -->
      <view class="user-cell">
        <text class="user-name">{{item.nickname || '匿名用户'}}</text>
      </view>

      <!-- 分数 -->
      <view class="score-cell">
        <block wx:if="{{activeTab === 'score'}}">
          <text>{{item.score}}</text>
        </block>
        <block wx:elif="{{activeTab === 'time'}}">
          <text>{{formatTime(item.gameTime)}}</text>
        </block>
        <block wx:elif="{{activeTab === 'moves'}}">
          <text>{{item.moves}}步</text>
        </block>
      </view>

      <!-- 时间 -->
      <view class="time-cell">
        <text>{{formatDate(item.timestamp)}}</text>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <view class="loading-indicator"></view>
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view class="no-more-data" wx:if="{{!isLoading && !hasMoreData && rankingList.length > 0}}">
      <text>没有更多数据了</text>
    </view>

    <!-- 空数据提示 -->
    <view class="empty-data" wx:if="{{!isLoading && rankingList.length === 0}}">
      <text>暂无排行数据</text>
    </view>
  </scroll-view>

  <!-- 底部区域 -->
  <view class="footer">
    <!-- 刷新按钮 -->
    <view class="refresh-button" bindtap="refreshRanking">
      刷新排行榜
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" bindtap="goBack">
      返回游戏
    </view>
  </view>
</view>

<!-- VIP对话框组件 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="game-2048-ranking"
  simpleMode="{{true}}"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bindclose="onVipDialogClose"
  bindbuy="onBuyVip">
</vip-dialog>
