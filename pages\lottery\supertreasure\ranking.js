// 排行榜页面逻辑
const app = getApp();
const api = require('../../../utils/api');
const { getConfig } = require('../../../config')

// 接口基础URL
const API_BASE_URL = getConfig().baseUrl+'/api';

Page({
  data: {
    backgroundImage: '/images/bg.jpg',
    activeTab: 'lucky', // 'lucky'(欧皇榜), 'unlucky'(非酋榜) 或 'draws'(抽奖次数)
    rankingList: [],
    userRank: null,
    hasUploaded: false,
    canUpload: true,
    loading: false,
    activityType: 'supertreasure', // 当前活动类型
    activityName: '至尊夺宝', // 活动名称
    isLoggedIn: false, // 用户是否已登录
    userInfo: null, // 用户信息
    statistics: {}, // 统计数据
    rarityScore: 0, // 稀有度分数
    isLoading: false, // 加载状态
    listOpacity: 1, // 列表透明度，用于切换标签时的淡入淡出效果
    // 用户信息相关数据
    showUserInfoModal: false, // 控制是否显示用户信息填写弹窗
    nickName: '', // 用户昵称
    _justSelectedWechatNickname: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('排行榜页面加载，参数:', options);

    // 从页面参数获取活动类型
    if (options && options.activityType) {
      this.setData({ activityType: options.activityType });
      console.log('设置活动类型:', options.activityType);
    }

    // 加载统计数据
    this.loadStatisticsData();

    // 获取排行榜数据
    this.getRankingList();

    // 检查登录状态，但不立即检查记录状态
    // 避免在token未准备好时调用API
    this.checkLoginStatus(false);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('页面显示，当前状态:', {
      isLoggedIn: this.data.isLoggedIn,
      hasUploaded: this.data.hasUploaded,
      activeTab: this.data.activeTab,
      rankingList: this.data.rankingList ? this.data.rankingList.length : 0
    });

    // 每次页面显示时检查登录状态
    this.checkLoginStatus(true);

    // 重新加载统计数据，确保数据为最新
    this.loadStatisticsData();

    // 刷新排行榜数据
    this.getRankingList();
  },

  /**
   * 检查登录状态并尝试恢复登录
   * @param {boolean} checkRecord - 是否检查记录状态
   */
  checkLoginStatus: function(checkRecord = true) {
    // 从本地存储获取用户信息和token
    const userInfoStr = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    if (userInfoStr && token) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        this.setData({
          userInfo: userInfo,
          isLoggedIn: true,
          nickName: userInfo.nickName || ''
        });

        // 仅当传入参数为true且已登录时，检查用户是否已上传过记录
        if (checkRecord) {
          console.log('已登录，开始检查记录状态...');
          this.checkRecordExists();
        }

        return true;
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: null
      });

      console.log('用户未登录，token信息不完整，跳过记录检查');
    }
    return false;
  },

  /**
   * 切换标签
   */
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      // 记录切换标签的时间戳，用于判断是否显示"记录处理中"的提示
      this._lastTabChange = Date.now();
      console.log('切换标签:', tab, '时间戳:', this._lastTabChange);

      // 先设置列表为加载中状态，添加淡出效果
      this.setData({
        isLoading: true,
        listOpacity: 0 // 添加透明度控制
      });

      // 切换标签
      setTimeout(() => {
        this.setData({
          activeTab: tab
        });

        // 获取对应标签的排行榜数据
        this.fetchRanking();

        // 数据加载完成后，添加淡入效果
        setTimeout(() => {
          this.setData({
            listOpacity: 1
          });
        }, 100);
      }, 100);
    }
  },

  /**
   * 获取排行榜列表
   */
  getRankingList: function() {
    // 检查是否是切换榜单导致的刷新
    const now = Date.now();
    const lastTabChange = this._lastTabChange || 0;
    const isTabSwitch = now - lastTabChange < 3000;

    // 只在非切换榜单的情况下显示加载提示，切换榜单时静默加载
    if (!isTabSwitch) {
      wx.showLoading({
        title: '加载排行榜...',
        mask: true
      });
    }

    const self = this;
    self.setData({ isLoading: true });

    // 设置请求超时定时器
    const requestTimeout = setTimeout(() => {
      console.log('排行榜请求超时');
      wx.hideLoading();
      wx.showToast({
        title: '请求超时，请重试',
        icon: 'none'
      });
      self.setData({ isLoading: false });
    }, 15000); // 15秒超时

    // 根据当前标签确定排序方式
    let orderType = 'desc'; // 默认倒序（欧皇榜，得分高的排前面）
    let rankType = 'rarity'; // 默认按稀有度排序

    if (self.data.activeTab === 'unlucky') {
      // 非酋榜，按稀有度正序排列（得分低的排前面）
      orderType = 'asc';
      rankType = 'rarity';
    } else if (self.data.activeTab === 'lucky') {
      // 欧皇榜，按稀有度倒序排列（得分高的排前面）
      orderType = 'desc';
      rankType = 'rarity';
    } else if (self.data.activeTab === 'draws') {
      // 抽奖次数排行，按抽奖次数倒序排列（次数多的排前面）
      orderType = 'desc';
      rankType = 'draws';
    }

    console.log('获取排行榜，类型:', rankType, '排序:', orderType);

    // 使用后端API获取排行榜
    wx.request({
      url: `${API_BASE_URL}/lottery/rankings`,
      method: 'GET',
      data: {
        activityType: self.data.activityType,
        type: rankType,
        order: orderType,
        limit: 50, // 请求更多数据，提供更全面的排行榜
        timestamp: Date.now() // 添加时间戳避免缓存
      },
      header: {
        'Content-Type': 'application/json'
      },
      success: function(res) {
        // 清除超时定时器
        clearTimeout(requestTimeout);

        console.log('获取排行榜数据成功:', res);
        if (res.statusCode === 200 && res.data.code === 0) {
          const result = res.data.data || {};
          console.log('排行榜原始数据:', result);

          // 处理排行榜数据，添加排名序号
          // 注意：后端返回的字段是 rankList 而不是 rankings
          const rankingList = (result.rankList || []).map((item, index) => {
            // 格式化稀有度分数，保留两位小数
            let formattedRarityScore = item.rarityScore;
            if (typeof formattedRarityScore === 'number') {
              formattedRarityScore = formattedRarityScore.toFixed(2);
            } else {
              formattedRarityScore = '0.00';
            }

            return {
              ...item,
              // 使用后端返回的 rank 或者根据索引计算
              rank: item.rank || (index + 1),
              // 格式化时间
              formattedTime: self.formatTimestamp(item.timestamp),
              // 确保 time 字段存在，用于显示
              time: self.formatTimestamp(item.timestamp),
              // 格式化后的稀有度分数
              rarityScore: formattedRarityScore
            };
          });

          // 处理用户排名信息
          let userRankInfo = null;
          if (result.userRank) {
            // 格式化用户排名中的稀有度分数，保留两位小数
            let userRarityScore = result.userRank.rarityScore;
            if (typeof userRarityScore === 'number') {
              userRarityScore = userRarityScore.toFixed(2);
            } else {
              userRarityScore = '0.00';
            }

            userRankInfo = {
              ...result.userRank,
              formattedTime: self.formatTimestamp(result.userRank.timestamp),
              // 确保 time 字段存在，用于显示
              time: self.formatTimestamp(result.userRank.timestamp),
              // 格式化后的稀有度分数
              rarityScore: userRarityScore
            };
          }

          // 更新排行榜数据
          self.setData({
            rankingList: rankingList,
            userRank: userRankInfo,
            isLoading: false,
            listOpacity: 1 // 恢复列表透明度，实现淡入效果
          });

          // 打印用户排名信息，方便调试
          console.log('用户排名信息:', result.userRank);

          // 只在特定情况下提示用户
          // 检查是否是切换榜单导致的刷新
          const now = Date.now();
          const lastTabChange = self._lastTabChange || 0;
          const isTabSwitch = now - lastTabChange < 3000;

          if (rankingList.length === 0) {
            // 无论什么情况，如果没有数据都要提示
            wx.showToast({
              title: '暂无排行数据',
              icon: 'none'
            });
          } else if (!isTabSwitch) {
            // 只有在非切换榜单的情况下（如手动刷新、首次加载）才显示更新提示
            wx.showToast({
              title: '排行榜数据已更新',
              icon: 'success'
            });
          }
          // 切换榜单时不显示任何提示，保持丝滑体验

          // 如果用户已登录但没有看到自己的排名，记录日志但不显示提示
          // 只有在特定条件下才显示提示，避免频繁切换榜单时的干扰
          if (self.data.isLoggedIn && !result.userRank && self.data.hasUploaded) {
            console.log('用户已上传记录但未在排行榜中找到');

            // 检查是否是首次加载或手动刷新，而不是切换榜单
            // _lastTabChange 记录最后一次切换榜单的时间
            const now = Date.now();
            const lastTabChange = self._lastTabChange || 0;

            // 如果不是刚刚切换榜单（超过3秒），才显示提示
            if (now - lastTabChange > 3000) {
              wx.showToast({
                title: '您的记录可能正在处理中',
                icon: 'none',
                duration: 2000
              });
            }
          }
        } else {
          console.error('获取排行榜数据失败:', res.data);
          wx.showToast({
            title: '获取排行榜失败',
            icon: 'none'
          });
          self.setData({ isLoading: false });
        }
      },
      fail: function(err) {
        // 清除超时定时器
        clearTimeout(requestTimeout);

        console.error('获取排行榜数据失败:', err);
        wx.showToast({
          title: '获取排行榜失败',
          icon: 'none'
        });
        self.setData({ isLoading: false });
      },
      complete: function() {
        // 清除超时定时器
        clearTimeout(requestTimeout);

        // 始终隐藏加载提示，但只有在非切换榜单时才显示了加载提示
        // 这样可以确保不会有加载提示一直存在的问题
        wx.hideLoading();
      }
    });
  },

  /**
   * 上传记录到排行榜
   */
  uploadRecord: function() {
    console.log('准备上传记录');

    // 检查是否已登录
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      console.log('用户未登录，显示登录提示');
      wx.showModal({
        title: '提示',
        content: '请先登录后再上传记录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            this.bindLogin();
          }
        }
      });
      return;
    }

    // 直接显示昵称输入弹窗，预填微信昵称
    console.log('显示昵称输入弹窗，引导用户使用微信昵称');
    this.setData({
      showUserInfoModal: true,
      nickName: this.data.userInfo.nickName || '' // 预填当前昵称（如果有）
    });
  },

  /**
   * 准备上传数据
   */
  prepareUploadData: function() {
    // 获取抽奖次数
    let drawCount = this.data.statistics.totalDraws || 0;

    // 如果统计数据中没有抽奖次数，尝试从treasure-hunting.js中保存的状态获取
    if (!drawCount) {
      const activityType = this.data.activityType;
      const stateKey = `${activityType}_state`;
      const stateStr = wx.getStorageSync(stateKey);

      if (stateStr) {
        try {
          const state = JSON.parse(stateStr);
          if (state.totalDraws && typeof state.totalDraws === 'number') {
            drawCount = state.totalDraws;
            console.log(`从状态数据获取抽奖次数: ${drawCount}`);
          }
        } catch (e) {
          console.error('解析状态数据失败:', e);
        }
      }
    }

    // 如果仍未找到，尝试从其他缓存中获取
    if (!drawCount) {
      // 尝试从抽奖次数缓存中获取
      const activityType = this.data.activityType;
      const possibleDrawCountKeys = [
        `drawCount_${activityType}`,
        `${activityType}_drawCount`,
        'treasureHuntingDrawCount',
        'totalDraws'
      ];

      for (const key of possibleDrawCountKeys) {
        const count = wx.getStorageSync(key);
        if (typeof count === 'number' && count > 0) {
          drawCount = count;
          console.log(`找到抽奖次数，使用键: ${key}`, count);
          break;
        }
      }

      // 如果仍未找到，尝试计算物品总数作为抽奖次数
      if (!drawCount) {
        const itemsForCount = this.formatItemsForUpload();
        if (itemsForCount.length > 0) {
          const totalItems = itemsForCount.reduce((sum, item) => sum + (item.count || 0), 0);
          if (totalItems > 0) {
            drawCount = totalItems;
            console.log('使用物品总数作为抽奖次数:', drawCount);
          }
        }
      }
    }

    // 获取物品数据
    const itemsToUpload = this.formatItemsForUpload();

    // 准备上传数据，根据接口文档调整字段名
    const dataToUpload = {
      activityType: this.data.activityType,
      openid: this.data.userInfo.openid || wx.getStorageSync('openid'),
      nickname: this.data.userInfo.nickName.trim(),
      statistics: itemsToUpload, // 使用 statistics 替代 items
      totalDraws: drawCount, // 使用 totalDraws 替代 drawCount
      hasLegendaryItems: this.hasLegendaryItems(),
      hasTotalGoldItems: this.hasGoldItems()
    };

    console.log('准备上传的数据:', dataToUpload);

    // 如果没有找到金色或传说级物品，提示用户无法上传
    if (itemsToUpload.length === 0) {
      console.log('未找到金色或传说级物品，无法上传记录');
      wx.showModal({
        title: '提示',
        content: '尚未抽到金色及以上稀有度的物品，无法上传奖励',
        showCancel: false,
        confirmText: '我知道了'
      });
    } else {
      console.log('找到金色或传说级物品，上传记录');
      this.uploadRecordToServer(dataToUpload);
    }
  },

  /**
   * 昵称输入回调函数
   */
  onInputNickname(e) {
    const value = e.detail.value;
    console.log('昵称输入:', value);
    this.setData({
      nickName: value
    });
  },

  /**
   * 昵称输入框失焦回调函数
   * 当用户从微信昵称选择器中选择昵称时也会触发
   */
  onNicknameBlur(e) {
    // 如果是刚选择微信昵称，则跳过处理
    if (this.data._justSelectedWechatNickname) {
      console.log('跳过blur处理（刚选择微信昵称）');
      return;
    }

    // 原有处理逻辑
    const nickname = e.detail.value;
    if (nickname && nickname.trim() !== '') {
      this.setData({
        nickName: nickname
      });
    }
  },

  /**
   * 表单提交处理函数
   * 用于获取通过选择微信昵称填入的内容
   */
  formSubmit(e) {
    const formNickname = e.detail.value.nickname;
    const finalNickname = this.data.nickName || formNickname; // 优先使用微信昵称

    if (!finalNickname || finalNickname.trim() === '') {
      wx.showToast({ title: '请输入昵称', icon: 'none' });
      return;
    }

    // 保存用户信息
    this.saveUserInfo(finalNickname);
  },

  /**
   * 保存用户信息
   * @param {string} nickname - 可选的直接传入的昵称
   */
  saveUserInfo: function(nickname) {
    const nickName = nickname || this.data.nickName;

    if (!nickName || !nickName.trim()) {
      wx.showToast({ title: '请输入昵称', icon: 'none' });
      return;
    }

    // 确保获取有效的openid
    const openid = this.data.userInfo?.openid || wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({ title: '用户信息不完整，请重新登录', icon: 'none' });
      return;
    }

    // 更新用户信息
    const userInfo = {
      ...this.data.userInfo,
      nickName: nickName.trim(),
      openid: openid
    };

    wx.setStorageSync('userInfo', JSON.stringify(userInfo));

    this.setData({
      userInfo: userInfo,
      isLoggedIn: true,
      showUserInfoModal: false
    });

    // 获取抽奖次数
    let drawCount = this.data.statistics.totalDraws || 0;

    // 如果统计数据中没有抽奖次数，尝试从treasure-hunting.js中保存的状态获取
    if (!drawCount) {
      const activityType = this.data.activityType;
      const stateKey = `${activityType}_state`;
      const stateStr = wx.getStorageSync(stateKey);

      if (stateStr) {
        try {
          const state = JSON.parse(stateStr);
          if (state.totalDraws && typeof state.totalDraws === 'number') {
            drawCount = state.totalDraws;
            console.log(`从状态数据获取抽奖次数: ${drawCount}`);
          }
        } catch (e) {
          console.error('解析状态数据失败:', e);
        }
      }
    }

    // 如果仍未找到，尝试从其他缓存中获取
    if (!drawCount) {
      // 尝试从抽奖次数缓存中获取
      const activityType = this.data.activityType;
      const possibleDrawCountKeys = [
        `drawCount_${activityType}`,
        `${activityType}_drawCount`,
        'treasureHuntingDrawCount',
        'totalDraws'
      ];

      for (const key of possibleDrawCountKeys) {
        const count = wx.getStorageSync(key);
        if (typeof count === 'number' && count > 0) {
          drawCount = count;
          console.log(`找到抽奖次数，使用键: ${key}`, count);
          break;
        }
      }

      // 如果仍未找到，尝试从页面数据中获取
      if (!drawCount) {
        const pageData = wx.getStorageSync('pageData') || {};
        if (typeof pageData.drawCount === 'number' && pageData.drawCount > 0) {
          drawCount = pageData.drawCount;
          console.log('从页面数据获取抽奖次数:', drawCount);
        }
      }

      // 如果仍未找到，尝试计算物品总数作为抽奖次数
      if (!drawCount) {
        const items = this.formatItemsForUpload();
        if (items.length > 0) {
          const totalItems = items.reduce((sum, item) => sum + (item.count || 0), 0);
          if (totalItems > 0) {
            drawCount = totalItems;
            console.log('使用物品总数作为抽奖次数:', drawCount);
          }
        }
      }
    }

    // 获取物品数据
    const items = this.formatItemsForUpload();

    // 准备上传数据，根据接口文档调整字段名
    const uploadData = {
      activityType: this.data.activityType,
      openid: userInfo.openid,
      nickname: nickName.trim(),
      statistics: items, // 使用 statistics 替代 items
      totalDraws: drawCount, // 使用 totalDraws 替代 drawCount
      hasLegendaryItems: this.hasLegendaryItems(),
      hasTotalGoldItems: this.hasGoldItems()
    };

    console.log('准备上传的数据:', uploadData);

    // 如果没有找到金色或传说级物品，提示用户无法上传
    if (items.length === 0) {
      console.log('未找到金色或传说级物品，无法上传记录');
      wx.showModal({
        title: '无法上传',
        content: '只有抽到金色及以上稀有度的物品才能上传到排行榜哦！',
        showCancel: false,
        confirmText: '我知道了'
      });
    } else {
      console.log('找到金色或传说级物品，上传记录');
      wx.showToast({
        title: '正在上传...',
        icon: 'loading',
        duration: 1000
      });
      this.uploadRecordToServer(uploadData);
    }
  },

  /**
   * 关闭用户信息弹窗
   */
  closeUserInfoModal() {
    this.setData({
      showUserInfoModal: false
    });
  },

  /**
   * 检查用户是否已上传过记录 (通过获取记录详情的方式)
   */
  checkRecordExists: function () {
    const that = this;
    if (!that.data.isLoggedIn || !that.data.userInfo?.openid) {
      console.log('用户未登录或openid无效，跳过记录检查');
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未找到用户token，无法获取记录状态');
      return;
    }

    const now = Date.now();
    const lastCheckTime = this._lastRecordCheckTime || 0;
    if (now - lastCheckTime < 5000) {
      console.log('短时间内已检查过记录，跳过重复请求');
      return;
    }
    this._lastRecordCheckTime = now;

    // 显示加载提示
    wx.showLoading({
      title: '检查记录状态...',
      mask: false
    });

    // 设置请求超时定时器
    const requestTimeout = setTimeout(() => {
      console.log('检查记录状态请求超时');
      wx.hideLoading();
    }, 10000); // 10秒超时

    wx.request({
      url: `${API_BASE_URL}/lottery/record-detail`,
      method: 'GET',
      data: {
        activityType: that.data.activityType,
        openid: that.data.userInfo.openid,
        nickname: that.data.userInfo.nickName || that.data.userInfo.nickname,
        timestamp: Date.now() // 添加时间戳避免缓存
      },
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: function (res) {
        console.log('检查记录状态响应:', res);
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            console.log('用户已上传记录');
            that.setData({
              hasUploaded: true,
              // 如果返回了稀有度分数，更新本地存储
              rarityScore: res.data.data?.rarityScore || that.data.rarityScore
            });

            // 如果当前排行榜中没有用户排名，记录日志但不自动刷新
            // 用户可以通过点击刷新按钮手动刷新
            if (!that.data.userRank) {
              console.log('已上传记录但排行榜中未找到用户排名，可能需要手动刷新');
            }
          } else if (res.data.code === 4001) {
            console.log('用户未上传记录');
            that.setData({ hasUploaded: false });
          } else {
            console.warn('检查记录状态返回未知代码:', res.data.code);
          }
        } else {
          console.error('检查记录状态失败:', res.statusCode);
        }
      },
      fail: function (err) {
        console.error('检查记录状态网络请求失败:', err);
      },
      complete: function() {
        // 清除超时定时器
        clearTimeout(requestTimeout);
        wx.hideLoading();
      }
    });
  },

  /**
   * 刷新排行榜 (添加防抖功能)
   */
  refreshRanking: function () {
    // 防止频繁刷新
    const now = Date.now();
    const lastRefreshTime = this._lastRefreshTime || 0;

    if (now - lastRefreshTime < 3000) { // 3秒内不允许重复刷新
      wx.showToast({
        title: '请勿频繁刷新',
        icon: 'none'
      });
      return;
    }

    // 记录本次刷新时间
    this._lastRefreshTime = now;

    // 如果正在加载中，提示用户
    if (this.data.isLoading) {
      wx.showToast({
        title: '正在加载中，请稍候',
        icon: 'none'
      });
      return;
    }

    // 执行刷新操作
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 1000
    });

    this.fetchRanking();
    if (this.data.isLoggedIn) {
      this.checkRecordExists();
    }
  },

  /**
   * 返回上一页
   */
  goBack: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 计算稀有度分数
   */
  calculateRarityScore: function() {
    const { statistics } = this.data;
    let score = 0;

    // 如果没有统计数据则返回0
    if (!statistics || Object.keys(statistics).length === 0) {
      this.setData({
        rarityScore: 0
      });
      return;
    }

    // 根据各稀有度的物品数量计算分数
    // 不同稀有度的物品有不同的权重
    const weights = {
      'ssr': 100, // 极品稀有度
      'sr': 50,   // 高级稀有度
      'r': 20,    // 中级稀有度
      'n': 5     // 普通
    };

    // 计算总分
    Object.entries(statistics).forEach(([itemName, count]) => {
      // 判断物品稀有度
      if (itemName.includes('传说')) {
        // 传说级物品
        score += count * 200;
      } else if (itemName.includes('永久') || itemName.includes('套装')) {
        // SSR物品
        score += count * weights.ssr;
      } else if (itemName.includes('7天') || itemName.includes('稀有')) {
        // SR物品
        score += count * weights.sr;
      } else if (itemName.includes('3天') || itemName.includes('高级')) {
        // R物品
        score += count * weights.r;
      } else {
        // 普通物品
        score += count * weights.n;
      }
    });

    this.setData({
      rarityScore: score
    });

    console.log('计算得出的稀有度分数:', score);
    return score;
  },

  /**
   * 获取排行榜数据
   */
  fetchRanking: function() {
    // 为兼容现有代码，调用getRankingList方法
    this.getRankingList();
  },

  /**
   * 格式化时间戳为可读时间
   * @param {number} timestamp - 时间戳
   * @return {string} 格式化后的时间字符串
   */
  formatTimestamp: function(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');

    // 简化时间格式，只显示月-日 时:分，节省空间
    return `${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 获取用户信息
   * 使用 wx.getUserProfile 获取用户信息
   */
  getUserProfile: function() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res);

          // 保存用户信息到本地存储
          const userInfo = res.userInfo;
          wx.setStorageSync('userInfo', JSON.stringify(userInfo));

          // 更新页面状态
          this.setData({
            userInfo: userInfo,
            isLoggedIn: true,
            nickName: userInfo.nickName || ''
          });

          // 获取登录凭证
          wx.login({
            success: (loginRes) => {
              if (loginRes.code) {
                // 发送 code 到后台换取 openId, sessionKey, unionId
                wx.request({
                  url: `${API_BASE_URL}/login/`,
                  method: 'POST',
                  data: {
                    code: loginRes.code
                  },
                  success: (loginResult) => {
                    if (loginResult.statusCode === 200 && loginResult.data.success) {
                      const data = loginResult.data;
                      // 保存 token 和 openid
                      wx.setStorageSync('token', data.token);
                      wx.setStorageSync('openid', data.openid);

                      // 更新用户信息中的 openid
                      userInfo.openid = data.openid;

                      // 保存VIP相关信息
                      if (data.is_vip !== undefined) {
                        wx.setStorageSync('is_vip', data.is_vip);
                      }
                      if (data.is_valid_vip !== undefined) {
                        wx.setStorageSync('is_valid_vip', data.is_valid_vip);
                      }
                      if (data.vip_expire_at) {
                        wx.setStorageSync('vip_expire_at', data.vip_expire_at);
                      }

                      // 保存用户信息
                      wx.setStorageSync('userInfo', JSON.stringify(userInfo));

                      resolve(userInfo);
                    } else {
                      console.error('登录失败:', loginResult);
                      reject(new Error(loginResult.data.message || '登录失败'));
                    }
                  },
                  fail: (err) => {
                    console.error('登录请求失败:', err);
                    reject(err);
                  }
                });
              } else {
                console.error('获取登录凭证失败:', loginRes);
                reject(new Error('获取登录凭证失败'));
              }
            },
            fail: (err) => {
              console.error('wx.login 失败:', err);
              reject(err);
            }
          });
        },
        fail: (err) => {
          console.log('获取用户信息失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 登录按钮绑定事件
   */
  bindLogin: function() {
    this.getUserProfile().then(result => {
      console.log('获取用户信息成功:', result);
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 登录成功后检查记录状态
      this.checkRecordExists();
    }).catch(err => {
      console.log('用户取消授权', err);
    });
  },

  /**
   * 退出登录
   */
  bindLogout: function() {
    wx.showModal({
      title: '退出确认',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorageSync('userInfo');
          this.setData({
            userInfo: null,
            isLoggedIn: false
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 删除记录
   */
  deleteRecord: function() {
    // 检查是否已登录
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '删除确认',
      content: '确定要删除您在排行榜上的记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteRecord();
        }
      }
    });
  },

  /**
   * 执行删除记录操作
   */
  doDeleteRecord: function() {
    const self = this;
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    // 获取token
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.hideLoading();
      wx.showToast({
        title: '登录信息已失效，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 使用后端API删除记录
    // 打印活动类型，便于调试
    console.log('删除记录，活动类型:', self.data.activityType);

    // 确保活动类型正确
    // 根据API文档，活动类型可选值：treasure-hunting(赛车夺宝)、supertreasure(至尊夺宝)、luckytree(幸运摇钱树)
    const activityType = self.data.activityType === 'treasure-hunting' ? 'treasure-hunting' :
                         self.data.activityType === 'supertreasure' ? 'supertreasure' :
                         self.data.activityType === 'luckytree' ? 'luckytree' : 'treasure-hunting';

    console.log('使用活动类型:', activityType);

    wx.request({
      url: `${API_BASE_URL}/lottery/records?activityType=${encodeURIComponent(activityType)}&openid=${encodeURIComponent(self.data.userInfo.openid)}`,
      method: 'DELETE',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: function(res) {
        console.log('删除记录成功:', res);
        if (res.statusCode === 200 && res.data.code === 0) {
          // 更新状态
          self.setData({
            hasUploaded: false
          });

          // 先显示删除成功的提示
          wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 1500,
            mask: true
          });

          // 删除成功后刷新排行榜，使用较短的延迟立即刷新
          setTimeout(() => {
            console.log('开始刷新排行榜...');
            self.getRankingList();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.message || '删除失败',
            icon: 'none',
            duration: 2000
          });

          // 即使删除失败，也刷新排行榜，确保显示最新数据
          setTimeout(() => {
            console.log('删除失败，仍然刷新排行榜...');
            self.getRankingList();
          }, 2000);
        }
      },
      fail: function(err) {
        console.error('删除记录失败:', err);
        wx.showToast({
          title: '删除记录失败',
          icon: 'none',
          duration: 2000
        });

        // 即使请求失败，也刷新排行榜，确保显示最新数据
        setTimeout(() => {
          console.log('请求失败，仍然刷新排行榜...');
          self.getRankingList();
        }, 2000);
      },
      complete: function() {
        wx.hideLoading();
      }
    });
  },

  /**
   * 绑定删除记录按钮事件
   */
  bindDeleteRecord: function() {
    this.deleteRecord();
  },

  /**
   * 加载统计数据
   */
  loadStatisticsData: function() {
    console.log('重新加载统计数据...');
    // 获取当前活动类型
    const activityType = this.data.activityType;
    console.log('当前活动类型:', activityType);

    // 构建可能的缓存键名数组
    const possibleKeys = [
      `statistics_${activityType}`,
      activityType,
      `${activityType}Statistics`,
      'treasureHuntingStatistics',
      'statistics'
    ];

    // 尝试查找这些键名对应的缓存数据
    let statistics = null;
    for (const key of possibleKeys) {
      const data = wx.getStorageSync(key);
      if (data && Object.keys(data).length > 0) {
        statistics = data;
        console.log(`找到统计数据，使用键: ${key}`, data);
        break;
      }
    }

    // 如果仍未找到，尝试使用其他活动类型的键名
    if (!statistics) {
      const pageKeys = ['supertreasure', 'treasure-hunting', 'luckytree'];
      for (const key of pageKeys) {
        const cacheKey = `statistics_${key}`;
        const data = wx.getStorageSync(cacheKey);
        if (data && Object.keys(data).length > 0) {
          statistics = data;
          console.log(`找到其他活动统计数据，使用键: ${cacheKey}`, data);
          break;
        }
      }
    }

    // 检查统计数据是否为对象
    if (statistics && typeof statistics === 'string') {
      try {
        statistics = JSON.parse(statistics);
        console.log('解析字符串统计数据:', statistics);
      } catch (e) {
        console.error('解析统计数据失败:', e);
        statistics = {};
      }
    }

    // 如果还是没找到，使用空对象
    if (!statistics) {
      statistics = {};
      console.warn('未找到任何统计数据');
    }

    console.log('最终加载的统计数据:', statistics);
    // 更新页面数据
    this.setData({
      statistics: statistics
    });

    // 计算稀有度分数
    this.calculateRarityScore();
  },

  // 更新微信昵称选择器处理方法
  handleChooseNickname: function(e) {
    console.log('微信昵称选择器返回值:', e.detail);
    const nickname = e.detail.value;

    if (nickname && nickname.trim()) {
      this.setData({
        nickName: nickname,
        _justSelectedWechatNickname: true // 设置标记
      });

      // 300ms后清除标记，确保覆盖blur事件
      setTimeout(() => {
        this.setData({ _justSelectedWechatNickname: false });
      }, 300);
    }
  },

  /**
   * 格式化物品数据用于上传
   * @returns {Array} 格式化后的物品数组
   */
  formatItemsForUpload: function() {
    // 尝试从多个可能的缓存键获取抽奖记录
    const activityType = this.data.activityType;
    console.log('格式化物品数据，活动类型:', activityType);

    // 首先尝试从统计数据中获取
    let { statistics } = this.data;
    const items = [];

    // 如果统计数据为空或没有有效内容，尝试从其他缓存中获取
    if (!statistics || Object.keys(statistics).filter(key => typeof statistics[key] === 'number').length === 0) {
      console.log('当前统计数据为空或无效，尝试从其他缓存获取');

      // 首先尝试从treasure-hunting.js中保存的状态获取数据
      // 这是最重要的缓存键，与treasure-hunting.js中的saveDrawState方法保持一致
      const stateKey = `${activityType}_state`;
      const stateStr = wx.getStorageSync(stateKey);

      if (stateStr) {
        try {
          const state = JSON.parse(stateStr);
          console.log(`找到抽奖状态数据，使用键: ${stateKey}`, state);

          if (state.statistics && Array.isArray(state.statistics)) {
            // 转换statistics数组为统计对象
            const statsObj = {};
            state.statistics.forEach(stat => {
              if (stat.item && stat.item.name && stat.count) {
                // 使用物品名称作为键，计数作为值
                const itemName = stat.item.displayName || stat.item.name;
                statsObj[itemName] = stat.count;

                // 只添加金色和传说级物品
                const background = stat.item.background || 'normal';
                if (background === 'gold' || background === 'legendary') {
                  items.push({
                    item: {
                      id: stat.item.id || Math.floor(Math.random() * 10000) + 1, // 使用现有ID或生成随机ID
                      name: itemName,
                      background: background,
                      probability: stat.item.probability || 0.0001
                    },
                    count: stat.count
                  });
                }
              }
            });

            console.log('从状态数据转换的统计对象:', statsObj);
            console.log('直接从状态数据构建的items数组:', items);

            // 如果已经构建了items数组，直接返回
            if (items.length > 0) {
              console.log('已从状态数据构建物品数组，共', items.length, '项');
              return items;
            }
          }
        } catch (e) {
          console.error('解析状态数据失败:', e);
        }
      }

      // 如果没有找到状态数据或解析失败，尝试其他可能的缓存键
      const possibleRecordKeys = [
        `records_${activityType}`,
        `${activityType}_records`,
        'treasureHuntingRecords',
        'drawRecords',
        'lotteryRecords'
      ];

      let recordsData = null;

      // 尝试所有可能的键
      for (const key of possibleRecordKeys) {
        const data = wx.getStorageSync(key);
        if (data && (Array.isArray(data) || (typeof data === 'object' && Object.keys(data).length > 0))) {
          recordsData = data;
          console.log(`找到抽奖记录数据，使用键: ${key}`, data);
          break;
        }
      }

      // 如果找到了记录数据，转换为统计数据格式
      if (recordsData) {
        // 根据数据类型处理
        if (Array.isArray(recordsData)) {
          // 如果是数组格式，统计每种物品的数量
          const tempStats = {};
          recordsData.forEach(record => {
            const itemName = record.displayName || record.name || record.itemName;
            if (itemName) {
              tempStats[itemName] = (tempStats[itemName] || 0) + 1;
            }
          });
          statistics = tempStats;
        } else if (typeof recordsData === 'object') {
          // 如果已经是统计格式
          statistics = recordsData;
        }

        console.log('从记录数据转换的统计数据:', statistics);
      } else {
        console.warn('未找到任何抽奖记录数据');
      }
    }

    // 如果仍然没有有效数据，尝试从页面缓存获取
    if ((!statistics || Object.keys(statistics).filter(key => typeof statistics[key] === 'number').length === 0) && items.length === 0) {
      // 尝试从页面缓存获取
      const pageData = wx.getStorageSync('pageData') || {};
      if (pageData.items && Array.isArray(pageData.items) && pageData.items.length > 0) {
        const tempStats = {};
        pageData.items.forEach(item => {
          const itemName = item.displayName || item.name || item.itemName;
          if (itemName) {
            tempStats[itemName] = (tempStats[itemName] || 0) + 1;
          }
        });
        statistics = tempStats;
        console.log('从页面缓存获取的统计数据:', statistics);
      }
    }

    // 如果已经构建了items数组，直接返回
    if (items.length > 0) {
      console.log('已构建物品数组，共', items.length, '项');
      return items;
    }

    // 如果仍然没有数据，记录警告并返回空数组
    if (!statistics || Object.keys(statistics).filter(key => typeof statistics[key] === 'number').length === 0) {
      console.warn('未能找到任何有效的抽奖记录数据');
      return [];
    }

    // 从统计对象格式化物品数据，只包含金色和传说级物品
    for (const [itemName, count] of Object.entries(statistics)) {
      // 跳过非数字值和特殊字段
      if (typeof count !== 'number' || itemName === 'totalDraws') continue;

      // 确定物品背景（稀有度）
      let background = 'normal';
      let isRare = false;

      if (itemName.includes('传说')) {
        background = 'legendary';
        isRare = true;
      } else if (itemName.includes('永久') || itemName.includes('金色')) {
        background = 'gold';
        isRare = true;
      } else if (itemName.includes('稀有')) {
        background = 'purple';
      }

      // 只添加金色和传说级物品
      if (isRare) {
        items.push({
          item: {
            id: Math.floor(Math.random() * 10000) + 1, // 生成随机ID，实际应该使用真实ID
            name: itemName,
            background: background,
            probability: 0.0001 // 默认概率，需根据实际情况调整
          },
          count: count
        });
      }
    }

    console.log('格式化后的物品数据:', items);
    return items;
  },

  /**
   * 检查是否有传说级物品
   * @returns {boolean} 是否有传说级物品
   */
  hasLegendaryItems: function() {
    // 首先尝试从统计数据中检查
    const { statistics } = this.data;

    // 如果统计数据有效，直接检查
    if (statistics && Object.keys(statistics).length > 0) {
      const hasLegendary = Object.keys(statistics).some(item =>
        item.includes('传说') || item.includes('legendary')
      );

      if (hasLegendary) {
        return true;
      }
    }

    // 如果统计数据中没有，尝试从格式化的物品数据中检查
    const items = this.formatItemsForUpload();
    if (items.length > 0) {
      return items.some(item =>
        item.item.background === 'legendary' ||
        (item.item.name && (item.item.name.includes('传说') || item.item.name.includes('legendary')))
      );
    }

    return false;
  },

  /**
   * 检查是否有金色/永久物品
   * @returns {boolean} 是否有金色/永久物品
   */
  hasGoldItems: function() {
    // 首先尝试从统计数据中检查
    const { statistics } = this.data;

    // 如果统计数据有效，直接检查
    if (statistics && Object.keys(statistics).length > 0) {
      const hasGold = Object.keys(statistics).some(item =>
        item.includes('永久') || item.includes('金色') ||
        item.includes('gold') || item.includes('permanent')
      );

      if (hasGold) {
        return true;
      }
    }

    // 如果统计数据中没有，尝试从格式化的物品数据中检查
    const items = this.formatItemsForUpload();
    if (items.length > 0) {
      return items.some(item =>
        item.item.background === 'gold' ||
        (item.item.name && (
          item.item.name.includes('永久') ||
          item.item.name.includes('金色') ||
          item.item.name.includes('gold') ||
          item.item.name.includes('permanent')
        ))
      );
    }

    return false;
  },

  /**
   * 上传记录到服务器
   * 只上传金色和传说级物品
   */
  uploadRecordToServer: function(data) {
    const self = this;
    wx.showLoading({ title: '上传记录中...', mask: true });

    // 设置请求超时定时器
    const requestTimeout = setTimeout(() => {
      console.log('上传记录请求超时');
      wx.hideLoading();
      wx.showToast({
        title: '上传超时，请重试',
        icon: 'none'
      });
    }, 15000); // 15秒超时

    // 添加时间戳参数，避免缓存问题
    data.timestamp = Date.now();

    // 确保有openid参数
    if (!data.openid) {
      // 尝试从用户信息中获取
      if (this.data.userInfo && this.data.userInfo.openid) {
        data.openid = this.data.userInfo.openid;
      } else {
        // 尝试从缓存中获取
        const openid = wx.getStorageSync('openid');
        if (openid) {
          data.openid = openid;
        } else {
          // 如果仍然没有openid，显示错误并返回
          clearTimeout(requestTimeout);
          wx.hideLoading();
          wx.showToast({
            title: '用户信息不完整，请重新登录',
            icon: 'none'
          });
          return;
        }
      }
    }

    // 确保有token
    const token = wx.getStorageSync('token');
    if (!token) {
      clearTimeout(requestTimeout);
      wx.hideLoading();
      wx.showToast({
        title: '登录信息已失效，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 根据接口文档，将 items 改为 statistics，将 drawCount 改为 totalDraws
    if (data.items && Array.isArray(data.items)) {
      // 将 items 字段重命名为 statistics
      data.statistics = data.items.filter(item => {
        const background = item.item?.background || '';
        return background === 'gold' || background === 'legendary';
      });

      // 删除原来的 items 字段
      delete data.items;

      // 如果过滤后没有物品，提示用户并返回
      if (data.statistics.length === 0) {
        clearTimeout(requestTimeout);
        wx.hideLoading();
        wx.showModal({
          title: '提示',
          content: '尚未抽到永久A车及以上奖励，无法上传奖励',
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }
    }

    // 将 drawCount 改为 totalDraws
    if (data.drawCount !== undefined) {
      data.totalDraws = data.drawCount;
      delete data.drawCount;
    }

    // 记录上传的数据，方便调试
    console.log('上传记录数据:', data);

    wx.request({
      url: `${API_BASE_URL}/lottery/records`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      data: data,
      success: function(res) {
        // 清除超时定时器
        clearTimeout(requestTimeout);

        console.log('上传记录响应:', res);
        if (res.statusCode === 200 && res.data.code === 0) {
          wx.showToast({
            title: '上传成功',
            icon: 'success',
            duration: 2000
          });

          // 保存服务器返回的稀有度分数
          const rarityScore = res.data.data?.rarityScore || 0;
          self.setData({
            hasUploaded: true,
            rarityScore: rarityScore
          });

          // 延迟刷新排行榜，给服务器一些处理时间
          setTimeout(() => {
            console.log('上传成功后刷新排行榜');
            self.getRankingList();

            // 不再进行第二次刷新，避免重复请求
          }, 2000);
        } else {
          wx.showToast({
            title: res.data.message || '上传失败',
            icon: 'none',
            duration: 2000
          });
          console.error('上传记录失败:', res.data);
        }
      },
      fail: function(err) {
        // 清除超时定时器
        clearTimeout(requestTimeout);

        console.error('上传记录网络错误:', err);
        wx.showToast({
          title: '网络错误，请检查网络连接',
          icon: 'none',
          duration: 2000
        });
      },
      complete: function() {
        // 清除超时定时器
        clearTimeout(requestTimeout);
        wx.hideLoading();
      }
    });
  },
})