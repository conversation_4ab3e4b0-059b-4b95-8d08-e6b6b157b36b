const { getConfig } = require('../../config')

Page({
  data: {
    baseUrl: getConfig().baseUrl,
    pets: [],
    filteredPets: [],
    searchKey: '',
    forms: ['全部', '单人骑宠', '双人骑宠', '六人骑宠', '强化暂未开启，敬请期待!'],
    skill_keyword:['全部','引擎宝宝','点火宝宝','进气宝宝','燃料宝宝','经验宝宝','亲密宝宝','情侣宝宝','补偿宝宝','保护宝宝',
      '炫光宝宝','加速宝宝','离线宝宝','炫光宝宝','车队宝宝','工资宝宝','防御宝宝','舞力宝宝','点券宝宝'
    ],
    main_attributes: ['全部', '运气', '防御', '攻击', '速度', '生命', '暂缺'],
    selectedForm: '全部',
    selectedMainAttribute: '全部',
    loading: false,
    expandedPetId: null,
    expandedIndex: -1,
    page: 1,
    hasMore: true,
    isLoading: false,
    pageSize: 24,
    searchParams: {
      pet_id: '',
      name: '',
      form: '',
      combat_power_min: '',
      combat_power_max: '',
      skill_keyword: '',
      main_attribute: ''
    },
    showSearchHistory: false, // 是否显示搜索历史
    selectedSkillKeyword: '全部',
    searchHistory: [],
    // 修改反馈按钮的默认位置
    feedbackBtnLeft: 20,
    feedbackBtnTop: 200,
    feedbackBtnTransform: '', // 新增变量，用于transform定位
    isDragging: false,
    showBackToTop: false,
    detailScrollTop: 0, // 用于控制详情弹窗的滚动位置
    hasPriorityLoading: false, // 添加优先加载权益标志
    hasMoved: false,
    showFullImageModal: false, // 是否显示全屏图片预览
    fullImageUrl: '' // 全屏预览的图片URL
  },

  // 检查优先加载权益
  checkPriorityPrivilege() {
    const privilege = wx.getStorageSync('priorityLoadingPrivilege');
    if (privilege && privilege.hasPrivilege) {
      const now = new Date().getTime();
      if (now < privilege.expireTime) {
        this.setData({ hasPriorityLoading: true });
        return true;
      } else {
        // 权益已过期，清除存储
        wx.removeStorageSync('priorityLoadingPrivilege');
      }
    }
    this.setData({ hasPriorityLoading: false });
    return false;
  },

  onLoad() {
    // 检查优先加载权益
    this.checkPriorityPrivilege();

    this.setData({
      baseUrl: getConfig().baseUrl
    }, () => {
      this.loadPets();
      this.loadSearchHistory();
    });

    // 使用新的 API 获取窗口信息
    const windowInfo = wx.getWindowInfo();
    const left = windowInfo.windowWidth - 70;
    const top = windowInfo.windowHeight - 240;

    this.setData({
      feedbackBtnLeft: left,
      feedbackBtnTop: top,
      feedbackBtnTransform: `translate3d(${left}px, ${top}px, 0)`
    });
  },

  // 加载宠物数据
  async loadPets(isLoadMore = false) {
    // 如果正在加载或者是加载更多但已经没有更多数据，则直接返回
    if (this.data.isLoading || (isLoadMore && !this.data.hasMore)) {
      return;
    }

    try {
      // 只有在有更多数据时才显示加载状态
      this.setData({
        isLoading: true,
        loading: isLoadMore ? false : true // 只在首次加载时显示loading
      });

      // 如果不是加载更多，重置页码为1
      if (!isLoadMore) {
        this.setData({ page: 1 });
      }

      const params = {
        page: this.data.page,
        page_size: this.data.pageSize,
        ...this.data.searchParams
      };

      // 添加优先加载标记
      if (this.data.hasPriorityLoading) {
        params.priority = 1;
      }

      // 过滤掉空值参数
      const queryString = Object.entries(params)
        .filter(([_, value]) => value !== null && value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');

      const url = `${this.data.baseUrl}/api/pets/?${queryString}`;
      console.log('请求URL:', url);

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: url,
          method: 'GET',
          header: this.data.hasPriorityLoading ? {
            'X-Priority-Loading': 'true'
          } : {},
          success: res => resolve(res),
          fail: err => reject(err)
        });
      });

      if (res.data && Array.isArray(res.data.results)) {
        const newPets = res.data.results.map(pet => ({
          ...pet,
          imageLoading: true,
          imageLoaded: false,
          //imageUrl: `${this.data.baseUrl}/media/pet_images/${pet.image_id}.png`,
          imageUrl: `https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/pet_images/${pet.image_id}.png`,
          displaySkills: [
            { name: pet.basic_skill, type: '基础' },
            { name: pet.enhanced_skill, type: '强化' }
          ]
        }));

        // 使用总数和当前页码来判断是否还有更多数据
        const totalCount = res.data.count || 0;
        const currentPage = this.data.page;
        const hasMore = currentPage * this.data.pageSize < totalCount;

        // 如果总数小于每页数量，或者当前加载的数据已经达到总数，则禁用加载状态
        const shouldDisableLoading = totalCount <= this.data.pageSize ||
          (currentPage * this.data.pageSize >= totalCount);

        this.setData({
          pets: isLoadMore ? [...this.data.pets, ...newPets] : newPets,
          filteredPets: isLoadMore ? [...this.data.filteredPets, ...newPets] : newPets,
          page: hasMore ? this.data.page + 1 : this.data.page,
          hasMore: hasMore,
          isLoading: false, // 完成加载后重置加载状态
          loading: false
        });

        // 如果没有更多数据，显示提示
        if (shouldDisableLoading && this.data.pets.length > 0) {
          wx.showToast({
            title: '已加载全部数据',
            icon: 'none',
            duration: 1500
          });
        }
      }
    } catch (error) {
      console.error('加载宠物数据失败:', error);
      this.setData({
        isLoading: false,
        loading: false
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = wx.getStorageSync('petSearchHistory') || [];
    this.setData({ searchHistory: history });
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return;

    let history = wx.getStorageSync('petSearchHistory') || [];
    // 删除已存在的相同关键词
    history = history.filter(item => item !== keyword);
    // 将新关键词添加到开头
    history.unshift(keyword);
    // 只保留最近的10条记录
    history = history.slice(0, 10);

    wx.setStorageSync('petSearchHistory', history);
    this.setData({ searchHistory: history });
  },

  // 清空搜索历史
  clearSearchHistory() {
    wx.removeStorageSync('petSearchHistory');
    this.setData({
      searchHistory: [],
      showSearchHistory: false
    });
  },

  // 点击历史记录项
  onHistoryItemTap(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKey: keyword,
      'searchParams.name': keyword,
      showSearchHistory: false,
      page: 1
    }, () => {
      this.loadPets();
    });
  },

  // 搜索框获得焦点
  onSearchFocus() {
    // 只有当有搜索历史时才显示
    if (this.data.searchHistory && this.data.searchHistory.length > 0) {
      this.setData({ showSearchHistory: true });
    }
  },

  // 搜索框失去焦点
  onSearchBlur() {
    // 延迟隐藏，以便点击历史记录
    if (this.data.showSearchHistory) {
      setTimeout(() => {
        this.setData({ showSearchHistory: false });
      }, 300);
    }
  },

  // 搜索处理
  onSearchInput(e) {
    const searchKey = e.detail.value;
    this.setData({
      searchKey,
      'searchParams.name': searchKey
    });
  },

  // 搜索按钮点击
  onSearch() {
    const keyword = this.data.searchKey.trim();
    if (keyword) {
      this.saveSearchHistory(keyword);
    }
    this.setData({
      showSearchHistory: false,
      page: 1,
      pets: [], // 清空当前列表
      filteredPets: [], // 清空当前列表
      'searchParams.name': keyword
    }, () => {
      this.loadPets();
    });
  },

  // 形态选择处理
  onTypeChange(e) {
    const index = e.detail.value;
    const form = this.data.forms[index];
    this.setData({
      selectedForm: form,
      page: 1,
      pets: [], // 清空当前列表
      filteredPets: [], // 清空当前列表
      'searchParams.form': form === '全部' ? '' : form
    }, () => {
      this.loadPets();
    });
  },

  // 宝宝类型选择处理
  onSkillKeywordChange(e) {
    const index = e.detail.value;
    const keyword = this.data.skill_keyword[index];
    const trimmedKeyword = keyword === '全部' ? '' : keyword.replace('宝宝', '');
    this.setData({
      selectedSkillKeyword: keyword,
      page: 1,
      pets: [], // 清空当前列表
      filteredPets: [], // 清空当前列表
      'searchParams.skill_keyword': trimmedKeyword
    }, () => {
      this.loadPets();
    });
  },

  // 主属性选择变化
  onMainAttributeChange(e) {
    const index = e.detail.value;
    const selectedMainAttribute = this.data.main_attributes[index];

    this.setData({
      selectedMainAttribute,
      'searchParams.main_attribute': selectedMainAttribute === '全部' ? '' : selectedMainAttribute,
      page: 1,
      hasMore: true
    });

    this.loadPets();

    // 收集用户选择数据
    wx.reportAnalytics('filter_main_attribute', {
      attribute: selectedMainAttribute
    });
  },

  // 点击宠物卡片
  onPetCardTap(e) {
    const id = e.currentTarget.dataset.id;
    const index = this.data.filteredPets.findIndex(pet => pet.id === id);

    if (index !== -1) {
      // 将滚动位置重置为0
      this.setData({
        expandedPetId: id,
        expandedIndex: index,
        detailScrollTop: 0
      });

      // 允许一定的延迟后再呈现复杂内容，提高真机渲染性能
      setTimeout(() => {
        // 如果需要执行其他加载操作，可以在这里添加
      }, 100);
    }
  },

  // 关闭详情弹窗
  onOverlayTap() {
    this.setData({
      expandedPetId: null,
      expandedIndex: -1,
      detailScrollTop: 0
    });
  },

  // 阻止冒泡
  onDetailCardTap(e) {
    // 阻止事件冒泡到overlay
    return false;
  },

  // 详情滚动到底部事件处理
  onDetailScrollToLower(e) {
    console.log('详情已滚动到底部');
    // 这里可以添加滚动到底部的处理逻辑，如需要
  },

  // 图片加载完成
  onImageLoad(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`filteredPets[${index}].imageLoading`]: false,
      [`filteredPets[${index}].imageLoaded`]: true
    });
  },

  // 图片加载失败
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`filteredPets[${index}].imageLoading`]: false,
      [`filteredPets[${index}].imageLoaded`]: false
    });
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  },

  // 触底加载更多
  onReachBottom() {
    // 如果总数据量小于等于每页数量，或者已经加载完所有数据，直接返回
    if (!this.data.hasMore || this.data.isLoading) {
      if (!this.data.hasMore) {
        wx.showToast({
          title: '已加载全部数据',
          icon: 'none',
          duration: 1500
        });
      }
      return;
    }

    this.loadPets(true);
  },

  // 修改监听页面滚动
  onPageScroll(e) {
    // 当滚动超过300px时显示返回顶部按钮
    const showBackToTop = e.scrollTop > 300;
    if (showBackToTop !== this.data.showBackToTop) {
      this.setData({ showBackToTop });
    }
  },

  // 返回顶部
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 阻止滚动穿透
  preventTouchMove(e) {
    // 阻止事件传播和默认行为
    if (e && e.preventDefault) {
      e.preventDefault();
    }
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    return false;
  },

  // 显示全屏图片
  showFullImage(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) return;

    this.setData({
      showFullImageModal: true,
      fullImageUrl: url
    });
  },

  // 隐藏全屏图片
  hideFullImage() {
    this.setData({
      showFullImageModal: false
    });
  },

  // 允许卡片内的触摸移动（重要，使内部滚动正常工作）
  allowCardTouchMove() {
    // 允许卡片内的触摸移动，不做任何处理
    return;
  },

  onFeedbackBtnTouchStart(e) {
    // 阻止事件传播
    this.preventTouchMove(e);

    if (e.touches.length !== 1) return;

    const touch = e.touches[0];
    this.startX = touch.clientX;
    this.startY = touch.clientY;
    this.offsetX = this.data.feedbackBtnLeft;
    this.offsetY = this.data.feedbackBtnTop;
    this.hasMoved = false;

    // 激活遮罩层，用于阻止背景滚动
    this.setData({ isDragging: true });

    // 临时禁用页面滚动 (额外措施)
    try {
      wx.disableScrolling && wx.disableScrolling({
        disable: true
      });
    } catch (err) {
      console.log('禁用滚动API不存在', err);
    }
  },

  onFeedbackBtnTouchMove(e) {
    // 阻止事件传播
    this.preventTouchMove(e);

    if (e.touches.length !== 1) return;

    // 确保遮罩层激活
    if (!this.data.isDragging) {
      this.setData({ isDragging: true });
    }

    const touch = e.touches[0];

    // 计算与起始位置的偏移量
    const deltaX = touch.clientX - this.startX;
    const deltaY = touch.clientY - this.startY;

    const windowInfo = wx.getWindowInfo();

    // 计算新位置
    let newLeft = this.offsetX + deltaX;
    let newTop = this.offsetY + deltaY;

    // 限制按钮在屏幕内
    newLeft = Math.max(0, Math.min(newLeft, windowInfo.windowWidth - 60));
    newTop = Math.max(0, Math.min(newTop, windowInfo.windowHeight - 60));

    this.setData({
      feedbackBtnLeft: newLeft,
      feedbackBtnTop: newTop
    });

    // 移动距离超过5px视为拖动
    if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
      this.hasMoved = true;
    }
  },

  onFeedbackBtnTouchEnd(e) {
    // 阻止事件传播
    this.preventTouchMove(e);

    // 延迟关闭遮罩层，确保所有触摸事件都处理完毕
    setTimeout(() => {
      this.setData({ isDragging: false });
    }, 50);

    // 重新启用页面滚动 (额外措施)
    try {
      wx.disableScrolling && wx.disableScrolling({
        disable: false
      });
    } catch (err) {
      console.log('启用滚动API不存在', err);
    }

    // 如果是点击而非拖动，则触发反馈按钮点击事件
    if (!this.hasMoved) {
      setTimeout(() => {
        this.onFeedbackBtnTap();
      }, 100);
    }

    this.hasMoved = false;
  },

  onFeedbackBtnTap() {
    if (!this.hasMoved) {
      // 先移除权益标记，避免影响页面跳转性能
      const currentPrivilege = this.data.hasPriorityLoading;
      this.setData({ hasPriorityLoading: false }, () => {
        wx.navigateTo({
          url: '/pages/feedback/feedback',
          success: () => {
            // 跳转成功后恢复权益标记
            this.setData({ hasPriorityLoading: currentPrivilege });
          },
          fail: (err) => {
            console.error('导航到反馈页面失败:', err);
            // 恢复权益标记
            this.setData({ hasPriorityLoading: currentPrivilege });
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      });
    }
  },

  copyGroupNumber() {
    wx.setClipboardData({
      data: '2156036977',
      success: () => {
        wx.showToast({
          title: '群号已复制',
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 分享给好友
  onShareAppMessage() {
    const title = this.data.expandedPetId !== null ?
      `【${this.data.filteredPets[this.data.expandedIndex].name}】宠物详情` :
      '宠物图鉴';

    return {
      title: title,
      path: '/pages/pet/pet'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '宠物图鉴 - 查看所有宠物详细信息',
      query: ''
    };
  },

  // 确保页面卸载时清理定时器
  onUnload() {
    // 不再需要清理长按定时器
  }
});