<!--pages/car-calculation/car-calculation.wxml-->
<view class="container">
  <!-- 背景图片 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- VIP徽章 -->
  <view class="vip-badge-container">
    <vip-badge id="vipBadge"
      pageType="common"
      pageKey="{{pageKey}}"
      isVip="{{isVip}}"
      freeCount="{{freeCount}}"
      remainingDays="{{vipRemainingDays}}"
      bindtap="handleVipBadgeClick" />
  </view>

  <!-- 页面标题 -->
  <view class="header">
    <view class="title">赛车推进计算与绘图</view>
    <view class="subtitle">根据原装数据计算满改装推进40</view>
  </view>

  <!-- 搜索栏 - 参考首页设计 -->
  <view class="search-bar" wx:if="{{!selectedCar}}">
    <view class="search-input-wrap">
      <image class="search-icon" src="/images/search.png" />
      <input
        type="text"
        placeholder="搜索赛车名称或编号"
        placeholder-class="placeholder"
        value="{{searchKey}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <picker
        class="custom-picker"
        mode="selector"
        range="{{levels}}"
        bindchange="onLevelChange"
        header-text="选择赛车级别"
      >
        <view class="level-picker {{selectedLevel !== '全部' ? 'active' : ''}}">
          <text>{{selectedLevel === '全部' ? '级别' : selectedLevel}}</text>
          <image class="picker-arrow" src="/images/arrow-down.png" />
        </view>
      </picker>
      <view class="search-btn" bindtap="onSearchConfirm">查询</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !filteredCars.length}}">
    <view class="loading"></view>
  </view>

  <!-- 无数据提示 -->
  <view class="empty-tip" wx:elif="{{filteredCars.length === 0 && !loading}}">
    暂无符合条件的赛车数据
  </view>

  <!-- 赛车列表 -->
  <view class="car-list" wx:elif="{{!selectedCar}}">
    <view class="car-card"
          wx:for="{{filteredCars}}"
          wx:key="id"
          bindtap="onCarSelect"
          data-car="{{item}}">
      <view class="car-card-content">
        <!-- 左侧图片 -->
        <view class="car-image-container">
          <image
            class="car-image {{item.imageLoaded ? 'loaded' : ''}}"
            src="{{item.cachedImageUrl}}"
            mode="aspectFit"
            bindload="onImageLoad"
            binderror="onImageError"
            data-index="{{index}}"
            lazy-load
          />
          <view class="image-loading" wx:if="{{!item.imageLoaded}}">
            <view class="loading"></view>
          </view>
        </view>

        <!-- 右侧数据 -->
        <view class="car-info">
          <view class="car-header">
            <text class="car-name">{{item.name}}</text>
            <text class="car-level" data-level="{{item.level}}">{{item.level}}</text>
          </view>

          <!-- 引擎档位数据 -->
          <view class="engine-section">
            <view class="engine-title">引擎档位</view>
            <view class="engine-gears">
              <view class="gear-row">
                <view class="gear-item">1档：{{item.engine_levels[0] || '暂无'}}</view>
                <view class="gear-item">2档：{{item.engine_levels[1] || '暂无'}}</view>
                <view class="gear-item">3档：{{item.engine_levels[2] || '暂无'}}</view>
              </view>
              <view class="gear-row">
                <view class="gear-item">4档：{{item.engine_levels[3] || '暂无'}}</view>
                <view class="gear-item">5档：{{item.engine_levels[4] || '暂无'}}</view>
                <view class="gear-item">6档：{{item.engine_levels[5] || '暂无'}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部加载提示 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <view class="loading"></view>
      <text>加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && filteredCars.length > 0}}">
      已加载全部数据
    </view>
  </view>

  <!-- 选中赛车信息卡片 -->
  <view class="car-info-section" wx:if="{{selectedCar}}">
    <view class="car-info-card">
      <view class="car-header">
        <view class="car-title">
          <image class="selected-car-image" src="{{selectedCar.cachedImageUrl}}" mode="aspectFit"/>
          <view class="car-title-text">
            <text class="car-name">{{selectedCar.name}}</text>
            <text class="car-level" data-level="{{selectedCar.level}}">{{selectedCar.level}}</text>
          </view>
        </view>
        <button class="change-car-btn" bindtap="onChangeCar">更换</button>
      </view>

      <view class="car-data-grid">
        <!-- 原装推进1-7档 -->
        <view class="data-section">
          <view class="data-title">原装推进档位（1-7档）</view>
          <view class="data-levels">
            <view class="level-item" wx:for="{{selectedCar.propulsion_levels}}" wx:key="index">
              <text class="level-label">推进{{index + 1}}</text>
              <text class="level-value">{{item || '暂无'}}</text>
            </view>
          </view>
        </view>

        <!-- 引擎1-6档 -->
        <view class="data-section">
          <view class="data-title">引擎档位（1-6档）</view>
          <view class="data-levels">
            <view class="level-item" wx:for="{{selectedCar.engine_levels}}" wx:key="index">
              <text class="level-label">引擎{{index + 1}}</text>
              <text class="level-value">{{item || '暂无'}}</text>
            </view>
          </view>
        </view>

        <!-- 燃料强度 -->
        <view class="data-section">
          <view class="data-title">燃料强度</view>
          <view class="fuel-intensity">{{selectedCar.formatted_fuel_intensity_display || '6.29'}}</view>
        </view>

        <!-- 点火强度 -->
        <view class="data-section">
          <view class="data-title">点火强度</view>
          <view class="ignition-intensity">{{selectedCar.formatted_ignition_intensity_display || '6.29'}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-section" wx:if="{{selectedCar}}">
    <view class="button-group">


      <!-- 曲线类型选择 -->
      <view class="curve-type-section">
        <view class="curve-type-title">选择曲线类型</view>
        <view class="curve-type-options">
          <view
            class="curve-type-option {{selectedCurveType === 'power_speed' ? 'active' : ''}}"
            bindtap="onCurveTypeSelect"
            data-type="power_speed"
          >
            动力-速度曲线
          </view>
          <view
            class="curve-type-option {{selectedCurveType === 'speed_time' ? 'active' : ''}}"
            bindtap="onCurveTypeSelect"
            data-type="speed_time"
          >
            � 速度-时间曲线
          </view>
        </view>
      </view>

      <!-- 改装次数设置 -->
      <view class="upgrade-section" wx:if="{{selectedCurveType}}">
        <view class="upgrade-title">推进改装次数 (0-40)</view>
        <view class="upgrade-input-container">
          <input
            class="upgrade-input"
            type="number"
            value="{{propulsionUpgrades}}"
            bindinput="onUpgradeInput"
            placeholder="请输入0-40"
            maxlength="2"
          />
          <text class="upgrade-unit">次</text>
        </view>
      </view>

      <!-- 绘制曲线按钮 -->
      <button
        class="chart-button primary-button"
        bindtap="generateCurve"
        disabled="{{!selectedCurveType || generatingChart}}"
      >
        {{generatingChart ? '绘制中...' : '绘制曲线'}}
      </button>
    </view>
  </view>



  <!-- 图表显示区域 -->
  <view class="chart-section" wx:if="{{showChart}}">
    <view class="chart-card">
      <view class="chart-header">
        <view class="chart-title">{{chartTitle}}</view>
        <view class="chart-subtitle">{{chartSubtitle}}</view>
      </view>
      <view class="chart-container">
        <canvas class="chart-canvas" canvas-id="carChart"></canvas>
      </view>
      <view class="chart-legend" wx:if="{{chartLegend.length > 0}}">
        <view class="legend-item" wx:for="{{chartLegend}}" wx:key="index">
          <view class="legend-color" style="background-color: {{item.color}}"></view>
          <text class="legend-text">{{item.name}}</text>
        </view>
      </view>

      <!-- 曲线数据说明 -->
      <view class="chart-description" wx:if="{{chartDescription}}">
        <text class="description-text">{{chartDescription}}</text>
      </view>
    </view>
  </view>

  <!-- 底部区域 -->
  <view class="footer">
    <view class="disclaimer-card">
      <view class="disclaimer">* 计算结果仅供参考，实际数据以游戏内为准</view>
    </view>

    <!-- 反馈按钮 -->
    <view class="feedback-section">
      <navigator url="/pages/feedback/feedback" class="feedback-link">
        💬 功能建议
      </navigator>
    </view>
  </view>
</view>

<!-- 底部导航栏 -->
<custom-tabbar selected="2"></custom-tabbar>

<!-- VIP对话框 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="{{pageKey}}"
  isVip="{{isVip}}"
  freeCount="{{freeCount}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bindclose="onVipDialogClose"
  bindbuy="onBuyVip">
</vip-dialog>
