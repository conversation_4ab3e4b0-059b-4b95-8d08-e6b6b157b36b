/* components/custom-tabbar/custom-tabbar.wxss */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 48px;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.03);
  z-index: 999;
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 2px;
}

.tab-bar-item image {
  width: 27px;
  height: 27px;
}

.tab-bar-text {
  font-size: 10px;
  color: #999999;
  margin-top: 3px;
}

.tab-bar-item.active .tab-bar-text {
  color: #4a90e2;
} 