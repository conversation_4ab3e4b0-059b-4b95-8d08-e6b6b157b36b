<!-- components/ad-button/ad-button.wxml -->
<!-- 遮罩层 - 使用catch而非catchtouchmove，并添加多个事件捕获 -->
<view 
  class="mask-layer {{isTouching ? 'active' : ''}}"
  catch:touchstart="preventTouchEvent" 
  catch:touchmove="preventTouchEvent" 
  catch:touchend="preventTouchEvent"
  catch:touchcancel="preventTouchEvent"
></view>

<view 
  class="ad-button-container {{isTouching ? 'dragging' : ''}}"
  style="left: {{buttonLeft}}px; top: {{buttonTop}}px;"
  catch:touchstart="buttonTouchStart"
  catch:touchmove="buttonTouchMove"
  catch:touchend="buttonTouchEnd"
  catch:touchcancel="buttonTouchEnd">
  <view class="ad-button">
    <image class="ad-icon" src="/images/ad-icon.png" mode="aspectFit"></image>
    <text class="ad-text">助力</text>
  </view>
</view> 