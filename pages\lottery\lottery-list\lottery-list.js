// 抽奖活动列表页面逻辑
const app = getApp();

// 引入API工具类
const api = require('../../../utils/api');

// 页面KEY标识
const PAGE_KEY = 'lottery-list';

import PageWithVIP from '../../../utils/page-with-vip';

PageWithVIP({
  data: {
    backgroundImage: '/images/bg.jpg', // 确保使用绝对路径
    activities: [
      {
        id: 'treasurehunting',
        name: '赛车夺宝',
        icon: 'cloud://cloud1-1gpha037e961200e.636c-cloud1-1gpha037e961200e-1334033817/treasure-hunting/logo.png',
        description: '“赛车夺宝”，抽奖概率模拟！',
        items: ['永久227顶级S车','永久普通S车', '永久雷诺系列赛车', '永久T1机甲', '永久A车']
      },
      {
        id: 'supertreasure',
        name: '至尊夺宝',
        icon: 'cloud://cloud1-1gpha037e961200e.636c-cloud1-1gpha037e961200e-1334033817/supertreasure/logo.png',
        description: '“至尊夺宝”，抽奖概率模拟！',
        items: ['永久227顶级S车','永久T2机甲', '永久T1机甲',  '永久A车']
      },
      {
        id: 'luckytree',
        name: '幸运摇钱树',
        icon: 'cloud://cloud1-1gpha037e961200e.636c-cloud1-1gpha037e961200e-1334033817/luckytree/logo.png',
        description: '“幸运摇钱树”，抽奖概率模拟！',
        items: ['龙胆逆鳞', '炼狱使者', '月影', '利刃针尖', '稀有服饰', '改装部件', '点券']
      },
    ],
    // VIP相关数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 导航状态控制
    isNavigating: false,
    loadingActivityId: ''
  },

  onLoad: function() {
    console.log('抽奖活动列表页面加载');

    // 重置页面卸载标记
    this.isUnloaded = false;

    // 确保背景图片路径正确
    this.setData({
      backgroundImage: '/images/bg.jpg'
    });

    // 初始化VIP和免费次数
    this.initVipAndFreeCount();
  },

  onUnload: function() {
    // 设置页面卸载标记
    this.isUnloaded = true;
    console.log('抽奖活动列表页面卸载');
  },

  onShow: function() {
    // 每次显示页面时更新VIP状态和免费次数
    this.updateVipAndFreeCount();
  },

  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays
    });
  },

  /**
   * 更新VIP和免费次数
   */
  updateVipAndFreeCount: function() {
    this.initVipAndFreeCount();
  },

  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    this.setData({
      showVipDialog: true
    });
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({
      showVipDialog: false
    });
  },

  /**
   * 添加免费次数事件
   */
  onAddFreeAttempts: function() {
    // 添加免费次数
    const newFreeCount = api.updateFreeCount(PAGE_KEY, 100);

    this.setData({
      freeCount: newFreeCount,
      showVipDialog: false
    });

    // 显示添加成功提示
    wx.showToast({
      title: '已添加100次免费机会',
      icon: 'success'
    });
  },

  // 活动点击事件处理
  onActivityTap: function(e) {
    const activityId = e.currentTarget.dataset.id;
    console.log('活动点击:', activityId);

    // 防止频繁点击
    if (this.data.isNavigating) {
      console.log('正在导航中，忽略点击');
      return;
    }

    // 设置导航状态
    this.setData({
      isNavigating: true,
      loadingActivityId: activityId
    });

    // 保存页面实例的引用
    const self = this;

    // 延迟一帧再跳转，让UI有时间响应
    setTimeout(() => {
      // 检查页面是否仍然存在（防止页面已卸载）
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 如果当前页面不是抽奖列表页面，则不执行后续操作
      if (currentPage.route !== 'pages/lottery/lottery-list/lottery-list') {
        console.log('页面已切换，取消导航');
        return;
      }

      let url = '';

      if (activityId === 'luckytree') {
        url = `/pages/lottery/luckytree/luckytree`;
      } else if (activityId === 'treasurehunting') {
        url = `/pages/lottery/treasure-hunting/treasure-hunting`;
      } else if (activityId === 'supertreasure') {
        url = `/pages/lottery/supertreasure/supertreasure`;
      }

      if (url) {
        wx.navigateTo({
          url: url,
          animationType: 'none',
          success: () => {
            console.log(`跳转到${activityId}成功`);
          },
          fail: (err) => {
            console.error(`跳转到${activityId}失败`, err);
          },
          complete: () => {
            // 检查页面是否仍然存在
            try {
              // 使用setTimeout确保在下一个事件循环中执行，避免视图更新冲突
              setTimeout(() => {
                // 再次检查页面是否仍然存在
                if (!self.isUnloaded) {
                  // 重置状态
                  self.setData({
                    isNavigating: false,
                    loadingActivityId: ''
                  });
                }
              }, 300);
            } catch (e) {
              console.log('页面可能已卸载，忽略状态更新');
            }
          }
        });
      } else {
        // 重置状态
        if (!self.isUnloaded) {
          self.setData({
            isNavigating: false,
            loadingActivityId: ''
          });
        }
      }
    }, 50);
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: 'QQ飞车抽奖模拟器 - 体验游戏抽奖不花钱',
      path: '/pages/lottery/lottery-list/lottery-list'
    };
  },

  // 返回工具箱（优化版本）
  goBack: function() {
    // 防止频繁点击
    if (this.data.isNavigating) {
      return;
    }

    this.setData({
      isNavigating: true
    });

    wx.navigateBack({
      delta: 1,
      animationType: 'none',
      success: () => {
        console.log('返回工具箱成功');
      },
      fail: (err) => {
        console.error('返回工具箱失败', err);
        // 重置状态
        this.setData({
          isNavigating: false
        });
      }
    });
  }
});