<!--pages/games/game-list/game-list.wxml-->
<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 小游戏标题 -->
  <view class="header">
    <view class="title">小游戏</view>
    <view class="subtitle">休闲娱乐</view>

    <!-- 使用统一的VIP徽章组件 -->
    <view class="vip-badge-container">
      <vip-badge id="vipBadge"
        pageType="common"
        isVip="{{isVip}}"
        freeCount="{{freeCount}}"
        remainingDays="{{vipRemainingDays}}"
        bindtap="onVipBadgeTap"
      />
    </view>
  </view>

  <view class="games-grid">
    <view class="game-item {{loadingNextPage && item.id === 'game-2048' ? 'loading' : ''}}"
          wx:for="{{games}}"
          wx:key="id"
          bindtap="onGameTap"
          data-id="{{item.id}}">
      <view class="game-icon">
        <image src="{{item.icon}}" mode="aspectFit"></image>
        <view class="loading-indicator" wx:if="{{loadingNextPage && item.id === 'game-2048'}}"></view>
      </view>
      <view class="game-info">
        <view class="game-name">{{item.name}}</view>
        <view class="game-desc">{{item.description}}</view>
      </view>
    </view>
  </view>

  <view class="footer">
    <!-- 免责声明卡片 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 更多游戏持续开发中</view>
    </view>
    <view class="feedback-button" bindtap="onFeedback">
      <navigator url="/pages/feedback/feedback">功能建议</navigator>
    </view>
  </view>
</view>

<!-- VIP对话框组件 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="games"
  simpleMode="{{true}}"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bindclose="onVipDialogClose"
  bindbuy="onBuyVip">
</vip-dialog>
