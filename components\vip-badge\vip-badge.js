Component({
  /**
   * 组件的属性列表
   */
  properties: {
    pageKey: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        // 当页面标识变化时，刷新免费次数
        if (newVal !== oldVal && newVal && this.data.pageType === 'lottery') {
          console.log(`VIP徽章 - 页面标识变化: ${oldVal} -> ${newVal}，刷新免费次数`);
          // 使用setTimeout确保在下一个事件循环中执行，避免在属性初始化期间调用
          setTimeout(() => {
            this.refreshFreeCount();
          }, 0);
        }
      }
    },
    pageType: {
      type: String,
      value: 'common', // common:通用页面, lottery:抽奖页面
      observer: function(newVal, oldVal) {
        // 当页面类型变化时，如果变为lottery类型，刷新免费次数
        if (newVal !== oldVal && newVal === 'lottery' && this.data.pageKey) {
          console.log(`VIP徽章 - 页面类型变化: ${oldVal} -> ${newVal}，刷新免费次数`);
          // 使用setTimeout确保在下一个事件循环中执行，避免在属性初始化期间调用
          setTimeout(() => {
            this.refreshFreeCount();
          }, 0);
        }
      }
    },
    isVip: {
      type: Boolean,
      value: false
    },
    freeCount: {
      type: Number,
      value: 0
    },
    remainingDays: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 移除初始数据，使用properties中的值
    vipInfoFailed: false // 添加标记，表示VIP信息是否获取失败过
  },

  lifetimes: {
    attached() {
      // 组件加载时自动获取VIP信息
      this.initVipStatus();

      // 监听全局VIP状态变化
      const app = getApp();
      if (app.globalEventEmitter) {
        app.globalEventEmitter.on('vipStatusChanged', this.handleVipStatusChanged.bind(this));
      }

      // 移除定时刷新VIP状态的定时器，避免频繁API调用
      // VIP状态变化通常不会那么频繁，可以依赖全局事件系统通知
    },

    detached() {
      // 清理事件监听
      const app = getApp();
      if (app.globalEventEmitter) {
        app.globalEventEmitter.off('vipStatusChanged', this.handleVipStatusChanged.bind(this));
      }

      // 不再需要清理定时器，因为已经移除了定时刷新逻辑
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化VIP状态
     */
    initVipStatus() {
      try {
        // 先尝试从全局app获取最新VIP状态
        const app = getApp();

        // 如果是抽奖页面，同时初始化免费次数
        if (this.data.pageType === 'lottery' && this.data.pageKey) {
          try {
            // 尝试获取免费次数
            let freeCount = null;

            // 1. 尝试从app.api获取
            if (app && app.api && app.api.getFreeCount) {
              freeCount = app.api.getFreeCount(this.data.pageKey);

              // 只有奖品查询页面需要检查是否为旧的默认值
              if (freeCount === 100 && app.api.initFreeCount && (this.data.pageKey === 'prize-list' || this.data.pageKey === 'prize-query')) {
                console.log(`VIP徽章初始化 - 检测到奖品查询页面的旧默认值(100)，重新初始化为3次`);
                freeCount = app.api.initFreeCount(this.data.pageKey, null);
              }
            }

            // 2. 尝试使用全局API
            if (freeCount === null) {
              const api = require('../../utils/api');
              if (api && api.getFreeCount) {
                freeCount = api.getFreeCount(this.data.pageKey);

                // 只有奖品查询页面需要检查是否为旧的默认值
                if (freeCount === 100 && api.initFreeCount && (this.data.pageKey === 'prize-list' || this.data.pageKey === 'prize-query')) {
                  console.log(`VIP徽章初始化 - 检测到奖品查询页面的旧默认值(100)，重新初始化为3次`);
                  freeCount = api.initFreeCount(this.data.pageKey, null);
                }
              }
            }

            // 3. 尝试从缓存获取
            if (freeCount === null) {
              const key = `freeCount_${this.data.pageKey}`;
              freeCount = wx.getStorageSync(key) || 0;

              // 只有奖品查询页面需要检查是否为旧的默认值
              if (freeCount === 100 && (this.data.pageKey === 'prize-list' || this.data.pageKey === 'prize-query')) {
                console.log(`VIP徽章初始化 - 从缓存检测到奖品查询页面的旧默认值(100)，重置为3`);
                freeCount = 3;
                wx.setStorageSync(key, freeCount);
              }
            }

            // 更新数据
            if (freeCount !== null) {
              this.setData({ freeCount: freeCount });
              console.log(`VIP徽章初始化 - 页面[${this.data.pageKey}]剩余次数:`, freeCount);
            }
          } catch (error) {
            console.error('初始化免费次数失败:', error);
          }
        }

        // 更新VIP状态 - 先从app获取，失败则从缓存获取
        if (app && app.getVipInfo) {
          app.getVipInfo().then(vipInfo => {
            if (vipInfo) {
              this.updateVipStatus(vipInfo);
              // 重置失败标记
              if (this.data.vipInfoFailed) {
                this.setData({ vipInfoFailed: false });
              }
            } else {
              // 尝试从缓存获取
              const cachedVipInfo = wx.getStorageSync('vipInfo');
              if (cachedVipInfo) {
                this.updateVipStatus(cachedVipInfo);
              } else {
                // 缓存也没有，标记为失败
                this.setData({ vipInfoFailed: true });
              }
            }
          }).catch(error => {
            console.error('从app获取VIP信息失败:', error);
            // 设置失败标记
            this.setData({ vipInfoFailed: true });

            // 尝试从缓存获取
            const cachedVipInfo = wx.getStorageSync('vipInfo');
            if (cachedVipInfo) {
              this.updateVipStatus(cachedVipInfo);
            }
          });
        } else {
          // 尝试从缓存获取
          const cachedVipInfo = wx.getStorageSync('vipInfo');
          if (cachedVipInfo) {
            this.updateVipStatus(cachedVipInfo);
          } else {
            // 无法获取VIP信息，标记为失败
            this.setData({ vipInfoFailed: true });
          }
        }
      } catch (error) {
        console.error('VIP徽章初始化失败:', error);
        // 设置失败标记
        this.setData({ vipInfoFailed: true });
      }
    },

    /**
     * 更新VIP状态
     */
    updateVipStatus(vipInfo) {
      if (!vipInfo) return;

      // 重新计算剩余天数
      if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
        const expireDate = new Date(vipInfo.vip_expire_at);
        const now = new Date();
        const diffTime = expireDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        vipInfo.remainingDays = Math.max(0, diffDays);

        console.log('VIP徽章 - 过期时间:', vipInfo.vip_expire_at);
        console.log('VIP徽章 - 当前时间:', now.toISOString());
        console.log('VIP徽章 - 剩余天数:', vipInfo.remainingDays);
      }

      // 设置VIP信息
      this.setData({
        isVip: vipInfo.is_valid_vip,
        remainingDays: vipInfo.remainingDays || 0
      });

      // 触发状态更新事件
      this.triggerEvent('vipStatusUpdate', {
        isVip: vipInfo.is_valid_vip,
        remainingDays: vipInfo.remainingDays || 0,
        freeCount: this.data.freeCount
      });
    },

    /**
     * 处理VIP状态变化事件
     */
    handleVipStatusChanged(vipInfo) {
      console.log('VIP徽章收到全局VIP状态变更通知:', vipInfo);

      // 检查是否需要刷新
      const currentIsVip = this.data.isVip;
      const newIsVip = vipInfo && vipInfo.is_valid_vip;

      const currentRemainingDays = this.data.remainingDays;
      const newRemainingDays = vipInfo && vipInfo.remainingDays ? vipInfo.remainingDays : 0;

      console.log('VIP徽章状态对比 - 当前:', {isVip: currentIsVip, remainingDays: currentRemainingDays});
      console.log('VIP徽章状态对比 - 新:', {isVip: newIsVip, remainingDays: newRemainingDays});

      // 更新VIP状态
      this.updateVipStatus(vipInfo);

      // 如果是抽奖页面，刷新免费次数
      if (this.data.pageType === 'lottery' && this.data.pageKey) {
        this.refreshFreeCount();
      }

      // 主动通知所有父页面刷新VIP状态
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        console.log('通知当前页面刷新VIP状态:', currentPage.route);

        // 检查页面是否有刷新VIP状态的方法
        if (currentPage.refreshVipStatus) {
          currentPage.refreshVipStatus();
        } else if (currentPage.updateVipStatus) {
          currentPage.updateVipStatus(vipInfo);
        } else if (currentPage.initVipStatus) {
          currentPage.initVipStatus();
        }
      }
    },

    /**
     * 处理点击事件
     */
    handleTap() {
      // 如果之前VIP信息获取失败过，尝试重新登录和获取VIP信息
      if (this.data.vipInfoFailed) {
        console.log('检测到VIP信息获取失败，尝试重新登录');
        const app = getApp();

        // 输出当前登录信息进行调试
        console.log('当前登录信息:', {
          token: wx.getStorageSync('token') ? '存在token' : '无token',
          tokenPreview: wx.getStorageSync('token') ?
            wx.getStorageSync('token').substring(0, 10) + '...' : '无token',
          openid: wx.getStorageSync('openid') || '无openid',
          时间戳: new Date().toISOString()
        });

        // 显示加载提示
        wx.showLoading({
          title: '加载中...',
          mask: true
        });

        // 清除旧的token和openid，强制重新登录
        wx.removeStorageSync('token');
        wx.removeStorageSync('openid');

        // 尝试重新登录
        if (app && app.login) {
          app.login().then((loginResult) => {
            console.log('重新登录成功，获取到新凭证:', {
              hasToken: !!loginResult.token,
              hasOpenid: !!loginResult.openid,
              tokenPreview: loginResult.token ?
                loginResult.token.substring(0, 10) + '...' : '无'
            });

            console.log('尝试获取VIP信息');
            // 手动发起VIP查询请求，绕过app.getVipInfo
            this.manualRequestVipInfo()
              .then(vipInfo => {
                if (vipInfo) {
                  console.log('手动获取VIP信息成功:', vipInfo);
                  this.updateVipStatus(vipInfo);
                  this.setData({ vipInfoFailed: false });
                }
                this.triggerEvent('click');
                wx.hideLoading();
              })
              .catch(error => {
                console.error('手动获取VIP信息失败:', error);
                // 仍然触发点击事件，让用户体验不受影响
                this.triggerEvent('click');
                wx.hideLoading();

                wx.showToast({
                  title: 'VIP信息获取失败',
                  icon: 'none',
                  duration: 2000
                });
              });
          }).catch(error => {
            console.error('重新登录失败:', error);
            this.triggerEvent('click');
            wx.hideLoading();

            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none',
              duration: 2000
            });
          });
        } else {
          // 如果没有登录方法，直接触发点击事件
          this.triggerEvent('click');
          wx.hideLoading();
        }
      } else {
        // 正常情况下直接触发点击事件
        this.triggerEvent('click');
      }
    },

    /**
     * 手动请求VIP信息
     * 通过直接调用请求，绕过app.getVipInfo可能存在的问题
     */
    manualRequestVipInfo() {
      return new Promise((resolve, reject) => {
        const openid = wx.getStorageSync('openid');
        const token = wx.getStorageSync('token');

        if (!openid || !token) {
          return reject(new Error('缺少认证信息'));
        }

        // 获取baseUrl - 根据实际情况调整
        const app = getApp();
        const baseUrl = (app.getConfig && app.getConfig().baseUrl) ||
                      'https://api.example.com'; // 替换为你的实际API地址

        console.log('发起手动VIP信息请求:', {
          url: `${baseUrl}/api/user/vip/info/`,
          method: 'GET',
          openid: openid,
          token: token ? token.substring(0, 10) + '...' : '无'
        });

        wx.request({
          url: `${baseUrl}/api/user/vip/info/`,
          method: 'GET',
          data: { openid: openid },
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('VIP信息响应:', {
              statusCode: res.statusCode,
              headers: JSON.stringify(res.header),
              data: JSON.stringify(res.data)
            });

            if (res.statusCode === 200 && res.data && res.data.success) {
              // 计算剩余天数
              const vipInfo = res.data.data || {};

              if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
                const expireDate = new Date(vipInfo.vip_expire_at);
                const now = new Date();
                vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
              }

              // 保存到缓存
              wx.setStorageSync('vipInfo', vipInfo);
              resolve(vipInfo);
            } else {
              console.error('VIP信息请求失败:', {
                statusCode: res.statusCode,
                message: res.data.message || '未知错误',
                data: res.data
              });
              reject(new Error(`VIP请求失败: ${res.statusCode}`));
            }
          },
          fail: (err) => {
            console.error('VIP信息请求发送失败:', err);
            reject(err);
          }
        });
      });
    },

    /**
     * 刷新VIP状态
     */
    refreshVipStatus() {
      try {
        // 主动从app获取最新VIP状态
        const app = getApp();
        if (app && app.getVipInfo) {
          app.getVipInfo().then(vipInfo => {
            if (vipInfo) {
              console.log('VIP徽章主动刷新VIP状态:', vipInfo);
              this.updateVipStatus(vipInfo);
              // 重置失败标记
              if (this.data.vipInfoFailed) {
                this.setData({ vipInfoFailed: false });
              }
            }
          }).catch(error => {
            console.error('VIP徽章刷新VIP状态失败:', error);
            // 设置失败标记
            this.setData({ vipInfoFailed: true });
          });
        } else {
          // 如果无法调用app方法，退回到本地存储
          this.initVipStatus();
        }
      } catch (error) {
        console.error('刷新VIP状态失败:', error);
        // 设置失败标记
        this.setData({ vipInfoFailed: true });
      }
    },

    /**
     * 刷新免费次数
     */
    refreshFreeCount() {
      if (!this.data.pageKey || this.data.pageType !== 'lottery') return;

      try {
        // 尝试从API工具获取最新的免费次数
        let freeCount = null;

        // 1. 尝试从app.api获取
        const app = getApp();
        if (app && app.api && app.api.getFreeCount) {
          freeCount = app.api.getFreeCount(this.data.pageKey);
        }

        // 2. 尝试从全局api获取
        if (freeCount === null) {
          try {
            const api = require('../../utils/api');
            if (api && api.getFreeCount) {
              freeCount = api.getFreeCount(this.data.pageKey);

              // 只有奖品查询页面需要检查是否为旧的默认值
              if (freeCount === 100 && (this.data.pageKey === 'prize-list' || this.data.pageKey === 'prize-query')) {
                console.log(`VIP徽章 - 检测到奖品查询页面的旧默认值(100)，重新初始化为3次`);
                // 使用null作为第二个参数，让initFreeCount根据页面类型自动设置初始值
                freeCount = api.initFreeCount(this.data.pageKey, null);
              }
            }
          } catch (error) {
            console.error('从全局API获取免费次数失败:', error);
          }
        }

        // 3. 尝试从缓存获取
        if (freeCount === null) {
          const key = `freeCount_${this.data.pageKey}`;
          freeCount = wx.getStorageSync(key) || 0;

          // 只有奖品查询页面需要检查是否为旧的默认值
          if (freeCount === 100 && (this.data.pageKey === 'prize-list' || this.data.pageKey === 'prize-query')) {
            console.log(`VIP徽章 - 从缓存检测到奖品查询页面的旧默认值(100)，重置为3`);
            freeCount = 3;
            wx.setStorageSync(key, freeCount);
          }
        }

        // 如果获取到了免费次数且与当前不同，则更新
        if (freeCount !== null && freeCount !== this.data.freeCount) {
          console.log(`VIP徽章 - 免费次数已更新: ${this.data.freeCount} -> ${freeCount}`);
          this.setData({ freeCount: freeCount });

          // 触发次数更新事件
          this.triggerEvent('freeCountUpdate', { freeCount: freeCount });
        }
      } catch (error) {
        console.error('刷新免费次数失败:', error);
      }
    }
  }
})