/**
 * 微信支付回调通知处理云函数
 */
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

exports.main = async (event, context) => {
  const db = cloud.database();
  
  try {
    console.log('收到微信支付回调:', event);
    
    // 检查是否为支付成功通知
    if (event.event_type !== "TRANSACTION.SUCCESS") {
      console.log('非支付成功通知，忽略处理');
      return { code: 0, message: '非支付成功通知' };
    }
    
    // 获取支付信息
    const transactionInfo = event.resource || {};
    
    // 提取商户订单号
    const orderNo = transactionInfo.out_trade_no;
    const transactionId = transactionInfo.transaction_id;
    const openid = transactionInfo.payer?.openid;
    
    if (!orderNo || !openid) {
      console.error('订单信息不完整:', { orderNo, openid });
      return { code: -1, message: '订单信息不完整' };
    }
    
    console.log(`处理支付成功通知: 订单号=${orderNo}, 微信交易号=${transactionId}, 用户=${openid}`);
    
    // 检查并处理vip_orders集合
    try {
      // 查找订单信息
      const orderRes = await db.collection('vip_orders').where({
        orderNo: orderNo
      }).get();
      
      if (!orderRes.data || orderRes.data.length === 0) {
        console.error('未找到对应的订单记录:', orderNo);
        return { code: -1, message: '未找到订单记录' };
      }
      
      const orderInfo = orderRes.data[0];
      
      // 检查订单是否已处理
      if (orderInfo.status === 'SUCCESS') {
        console.log('订单已处理，忽略重复通知');
        return { code: 0, message: '订单已处理' };
      }
      
      // 更新订单状态
      await db.collection('vip_orders').doc(orderInfo._id).update({
        data: {
          status: 'SUCCESS',
          paidTime: db.serverDate(),
          transactionId: transactionId,
          updateTime: db.serverDate()
        }
      });
      
      console.log('订单状态已更新为支付成功');
      
      // 计算VIP到期时间
      let expireAt = new Date();
      
      // 尝试更新用户VIP信息
      try {
        // 查询用户当前VIP信息
        const userRes = await db.collection('user_vip').where({
          openid: openid
        }).get();
        
        let user = null;
        if (userRes.data && userRes.data.length > 0) {
          user = userRes.data[0];
        } else {
          // 创建用户记录
          const addRes = await db.collection('user_vip').add({
            data: {
              openid: openid,
              createTime: db.serverDate()
            }
          });
          
          const newUserRes = await db.collection('user_vip').doc(addRes._id).get();
          user = newUserRes.data;
        }
        
        // 如果用户已经是VIP，基于原过期时间计算新的过期时间
        if (user.is_vip && user.vip_expire_at) {
          const currentExpireDate = new Date(user.vip_expire_at);
          // 确保基准时间不早于当前时间
          expireAt = currentExpireDate > new Date() ? currentExpireDate : new Date();
        }
        
        // 增加VIP天数
        expireAt.setDate(expireAt.getDate() + orderInfo.days);
        
        // 更新用户VIP状态
        await db.collection('user_vip').where({
          openid: openid
        }).update({
          data: {
            is_vip: true,
            is_valid_vip: true,
            vip_expire_at: expireAt,
            updateTime: db.serverDate()
          }
        });
        
        console.log(`用户VIP状态已更新: 过期时间=${expireAt.toISOString()}`);
      } catch (userError) {
        console.error('更新用户VIP信息失败，但订单已处理:', userError);
        // 即使用户VIP状态更新失败，也认为流程基本完成，返回部分成功
        return {
          code: 1,
          message: '订单处理成功，但用户VIP状态更新失败',
          data: {
            orderNo: orderNo,
            transactionId: transactionId,
            error: userError.message
          }
        };
      }
      
      // 尝试记录VIP购买日志，但即使失败也不影响主流程
      try {
        // 记录VIP购买日志
        await db.collection('vip_purchase_logs').add({
          data: {
            openid: openid,
            orderNo: orderNo,
            transactionId: transactionId,
            amount: orderInfo.price,
            days: orderInfo.days,
            planType: orderInfo.planType,
            expireAt: expireAt,
            createTime: db.serverDate(),
            isRenewal: orderInfo.isRenewal || false
          }
        });
        console.log('VIP购买日志已记录');
      } catch (logError) {
        console.warn('记录VIP购买日志失败，但不影响主流程:', logError);
        // 日志记录失败不影响主流程
      }
      
      return {
        code: 0,
        message: '处理成功',
        data: {
          openid: openid,
          orderNo: orderNo,
          transactionId: transactionId,
          expireAt: expireAt
        }
      };
    } catch (orderError) {
      console.error('订单处理失败:', orderError);
      return {
        code: -1,
        message: `订单处理失败: ${orderError.message}`,
        error: orderError
      };
    }
  } catch (error) {
    console.error('处理支付通知失败:', error);
    return {
      code: -1,
      message: `处理失败: ${error.message}`,
      error: error
    };
  }
}; 