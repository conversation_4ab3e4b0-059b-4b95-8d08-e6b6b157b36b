<!--pages/toolbox/toolbox.wxml-->
<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 工具箱标题 -->
  <view class="header">
    <view class="title">工具箱</view>
    <view class="subtitle">实用工具集</view>

    <!-- 使用统一的VIP徽章组件 -->
    <view class="vip-badge-container">
      <vip-badge id="vipBadge"
        pageType="common"
        isVip="{{isVip}}"
        freeCount="{{freeCount}}"
        remainingDays="{{vipRemainingDays}}"
        bindtap="onVipBadgeTap"
      />
    </view>
  </view>

  <view class="tools-grid">
    <view class="tool-item {{loadingNextPage && (item.id === 'lottery' || item.id === 'car-calculation') ? 'loading' : ''}}"
          wx:for="{{tools}}"
          wx:key="id"
          bindtap="onToolTap"
          data-id="{{item.id}}">
      <view class="tool-icon">
        <image src="{{item.icon}}" mode="aspectFit"></image>
        <view class="loading-indicator" wx:if="{{loadingNextPage && (item.id === 'lottery' || item.id === 'car-calculation')}}"></view>
      </view>
      <view class="tool-info">
        <view class="tool-name">{{item.name}}</view>
        <view class="tool-desc">{{item.description}}</view>
      </view>
    </view>
  </view>

  <view class="footer">
    <!-- 免责声明卡片 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 更多功能持续开发中</view>
    </view>
    <view class="feedback-button" bindtap="onFeedback">
      <navigator url="/pages/feedback/feedback">功能建议</navigator>
    </view>

    <!-- 广告组件 -->
    <!--<view class="ad-container">
      <ad-button text="获取更多功能" />
    </view>-->
  </view>
</view>

<!-- VIP对话框组件 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="toolbox"
  simpleMode="{{true}}"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bindclose="onVipDialogClose"
  bindbuy="onBuyVip">
</vip-dialog>