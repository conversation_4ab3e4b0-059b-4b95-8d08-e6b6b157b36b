/* 添加响应式配置，适配不同屏幕尺寸 */
page {
  --content-width: 94%;
  --grid-columns-large: 5;
  --grid-columns-medium: 4;
  --grid-columns-small: 3;
  --item-gap-large: 10rpx;
  --item-gap-small: 6rpx;
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --content-width: 96%;
    --grid-columns-large: 4;
    --grid-columns-medium: 3;
    --grid-columns-small: 2;
    --item-gap-large: 6rpx;
    --item-gap-small: 4rpx;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --content-width: 94%;
    --grid-columns-large: 5;
    --grid-columns-medium: 4;
    --grid-columns-small: 3;
    --item-gap-large: 12rpx;
    --item-gap-small: 8rpx;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

/* 抽奖模拟器页面样式 */
.container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20rpx;
  position: relative;
  box-sizing: border-box;
  padding-bottom: calc(48px + env(safe-area-inset-bottom) + 30rpx); /* 为自定义tabBar预留空间 */
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加一个半透明的遮罩层 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: -1;
}

/* 活动信息区域 */
.activity-info {
  text-align: center;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
  width: var(--content-width);
  max-width: 700rpx;
}

.activity-name {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10rpx;
}

.activity-description {
  font-size: var(--font-size-small);
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.cost-info {
  font-size: 24rpx;
  color: #ffd700;
}

.real-cost-info {
  font-size: 22rpx;
  color: #ffb700;
  display: block;
  margin-top: 6rpx;
}

/* 抽奖按钮区域 */
.draw-area {
  margin: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.buttons {
  display: flex;
  gap: 40rpx;
}

.draw-button {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.draw-button.single {
  background-color: #9966cc;
  color: #ffffff;
}

.draw-button.multi {
  background-color: #ff9500;
  color: #ffffff;
}

.draw-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.draw-button.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 抽奖动画 */
.draw-animation {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading {
  display: flex;
  gap: 20rpx;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
  margin: 0 10rpx;
  display: inline-block;
  animation: loading 0.4s infinite ease-in-out both;
}

@keyframes loading {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
}

/* 抽奖结果面板 */
.result-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-height: 80vh;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
  padding: 30rpx;
  z-index: 100;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.result-panel.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  pointer-events: auto;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eeeeee;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.close-button {
  width: 60rpx;
  height: 60rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 48rpx;
  color: #999999;
  border-radius: 50%;
}

.result-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.result-item {
  width: calc(50% - 10rpx);
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.result-item.gold-bg {
  background: linear-gradient(135deg, #fffceb, #fff6d6);
  border: 1px solid #ffd700;
}

.result-item.purple-bg {
  background: linear-gradient(135deg, #f0e6ff, #e6d9ff);
  border: 1px solid #9966cc;
}

.result-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.result-icon image {
  width: 100%;
  height: 100%;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.result-rarity {
  font-size: 24rpx;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  display: inline-block;
}

/* 统计面板 */
.stats-panel {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  width: var(--content-width);
  max-width: 700rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1px solid #eeeeee;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.stats-summary-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 10px;
}

.stats-summary-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.stats-summary {
  font-size: 14px;
  color: #666;
  margin-right: 15px;
}

.stats-summary.fragments {
  color: #ff9800;
  font-weight: bold;
}

.real-cost-summary {
  font-size: 22rpx;
  color: #ff9500;
  margin-right: 10rpx;
}

.reset-button {
  font-size: 24rpx;
  color: #4a90e2;
  padding: 6rpx 16rpx;
  border: 1px solid #4a90e2;
  border-radius: 20rpx;
}

.stats-list {
  width: 100%;
}

.stats-item {
  display: flex;
  align-items: center;
  padding: 16rpx 10rpx;
  border-bottom: 1px solid #f5f5f5;
}

.stats-item.gold-bg {
  background: linear-gradient(135deg, #fffceb, #fff6d6);
  border-radius: 8rpx;
  margin: 4rpx 0;
}

.stats-item.purple-bg {
  background: linear-gradient(135deg, #f0e6ff, #e6d9ff);
  border-radius: 8rpx;
  margin: 4rpx 0;
}

.stats-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.stats-icon image {
  width: 100%;
  height: 100%;
}

.stats-name {
  flex: 3;
  font-size: 26rpx;
  color: #333333;
}

.stats-probability {
  flex: 1;
  font-size: 22rpx;
  color: #999999;
  text-align: center;
}

.stats-count {
  flex: 1;
  font-size: 22rpx;
  color: #333333;
  text-align: center;
}

.stats-percentage {
  flex: 1;
  font-size: 22rpx;
  color: #ff9500;
  text-align: center;
  font-weight: bold;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin: 20rpx 0;
}

.action-button {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background-color: #4a90e2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.action-button.reset-selection {
  background-color: #ff9500;
}

.action-button:active {
  opacity: 0.8;
  transform: scale(0.96);
}

/* 底部区域 */
.footer {
  margin-top: 40rpx;
  padding: 20rpx 0;
  padding-bottom: env(safe-area-inset-bottom);
  text-align: center;
}

/* 免责声明卡片 */
.disclaimer-card {
  width: var(--content-width);
  max-width: 700rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 auto 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.disclaimer {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.back-button {
  display: inline-block;
  background-color: #9966cc;
  color: #ffffff;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid #ffffff;
  transition: all 0.3s;
}

.back-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.ad-container {
  margin-top: 20rpx;
}

/* 稀有度说明 */
.rarity-info {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}

.rarity-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.rarity-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.rarity-item {
  display: flex;
  align-items: center;
  width: calc(50% - 10rpx);
}

.rarity-desc {
  font-size: 22rpx;
  color: #666666;
}

/* 中奖结果展示区域样式 */
.results-display {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 20rpx auto;
  width: var(--content-width);
  max-width: 700rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: var(--font-size-title);
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
  color: #333;
}

/* 新的五列网格样式 - 响应式调整 */
.results-grid.five-column {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns-large), 1fr);
  gap: var(--item-gap-large);
  padding: 10rpx;
}

.result-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-grid-item.gold-bg {
  background: linear-gradient(135deg, #fffae6, #fff5cc);
  border: 1px solid #ffcc00;
}

.result-grid-item.purple-bg {
  background: linear-gradient(135deg, #f0e6ff, #e6d9ff);
  border: 1px solid #9966cc;
}

.result-grid-item image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 6rpx;
}

.result-grid-item text {
  font-size: 20rpx;
  text-align: center;
  color: #666;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 添加活动规则样式 */
.activity-rules {
  margin-top: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 10rpx;
  padding: 15rpx;
  width: var(--content-width);
  max-width: 700rpx;
}

.rules-title {
  color: #f8d384;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.rules-content text {
  color: #ffffff;
  font-size: 24rpx;
  line-height: 1.4;
}

.guarantee-note {
  color: #ff9f43 !important;
  font-weight: bold;
}

/* 概率公示链接样式 */
.probability-link {
  color: #ffffff;
  font-size: 24rpx;
  line-height: 1.4;
  margin-top: 10rpx;
}

.highlight-text {
  color: #00ccff;
  font-weight: bold;
  text-decoration: underline;
}

/* 添加自选按钮样式 */
.select-button {
  margin-top: 15rpx;
  background-color: #ff9900;
  color: #ffffff;
  font-size: 26rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  display: inline-block;
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s;
}

.select-button:active {
  transform: scale(0.96);
  opacity: 0.9;
}

/* 自选面板相关样式 */
.select-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  overflow: hidden; /* 防止内容溢出 */
}

.select-panel.show {
  opacity: 1;
  visibility: visible;
}

.select-panel-content {
  width: var(--content-width);
  max-height: 90vh;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  overflow-y: auto; /* 只允许面板内容滚动 */
  overflow-x: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: fadeInUp 0.3s;
  -webkit-overflow-scrolling: touch; /* 使iOS滚动更流畅 */
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 12px;
}

.select-title {
  font-size: 18px;
  font-weight: bold;
}

.close-button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f2f2f2;
  font-size: 20px;
  color: #666;
}

.load-previous-button {
  background-color: #f5f5f5;
  color: #666;
  text-align: center;
  padding: 8px 0;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.step {
  width: 70px;
  padding: 6px 0;
  text-align: center;
  font-size: 14px;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 12px;
}

.step.active {
  background-color: #ffecec;
  color: #ff6b6b;
  font-weight: bold;
}

.step-line {
  height: 2px;
  flex: 1;
  background-color: #eee;
  margin: 0 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.selection-hint {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}

/* 新UI：已选项区域 */
.selected-items-area {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 16px;
}

.selected-items-title {
  font-size: 14px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.selected-items-container {
  display: flex;
  flex-wrap: wrap;
  min-height: 80px;
}

.selected-item-placeholder {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 14px;
  border: 1px dashed #ddd;
  border-radius: 6px;
}

.selected-item {
  width: calc(20% - 8px);
  margin: 0 4px 8px;
  background-color: #fff;
  border-radius: 6px;
  padding: 6px;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.selected-item-image {
  width: 100%;
  height: 45px;
  object-fit: contain;
  margin-bottom: 4px;
}

.selected-item-name {
  font-size: 10px;
  color: #333;
  text-align: center;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-button {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 新UI：选择网格 - 响应式调整 */
.selection-grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns-medium), 1fr);
  gap: var(--item-gap-large);
  margin-bottom: 16px;
  justify-content: center;
}

.grid-item {
  width: auto;
  margin: 0;
  background-color: #fff;
  border-radius: 6px;
  padding: 6px;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  border: 1px solid #eee;
}

.grid-item-image {
  width: 100%;
  height: 50px;
  object-fit: contain;
  margin-bottom: 4px;
}

.grid-item-name {
  font-size: var(--font-size-mini);
  color: #333;
  text-align: center;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-item-selected {
  background-color: #ffecec;
  border-color: #ff6b6b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.15);
}

.grid-item-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.selected-mark {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ff6b6b;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 步骤按钮 */
.step-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

/* 顶部步骤按钮 */
.top-buttons {
  margin-top: 10px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eee;
}

.step-button {
  flex: 1;
  margin: 0 5px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 20px;
  font-size: 14px;
  transition: all 0.2s;
}

.step-button.prev {
  background-color: #f5f5f5;
  color: #666;
}

.step-button.next, .step-button.complete {
  background-color: #ff6b6b;
  color: white;
}

.step-button.disabled {
  background-color: #f5f5f5;
  color: #bbb;
  pointer-events: none;
}

/* 预览部分样式 */
.preview-section {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
}

.preview-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.preview-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  min-height: 120rpx;
}

.preview-item {
  width: calc(33.333% - 7rpx);
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.preview-item image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 8rpx;
}

.preview-item text {
  font-size: 22rpx;
  color: #333333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.preview-empty {
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 26rpx;
  border: 1px dashed #cccccc;
  border-radius: 8rpx;
}

/* 选择步骤 */
.select-step {
  margin-bottom: 30rpx;
}

/* 激活态动画效果 */
.custom-checkbox-item:active {
  transform: scale(0.97);
  opacity: 0.9;
}

/* 新统计面板和概率公示样式 */
.panels-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-top: 40rpx;
}

/* 概率公示面板 */
.probability-panel {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #eee;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.probability-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.probability-category {
  margin-bottom: 16rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.probability-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx;
  border-bottom: 1px solid #f0f0f0;
}

.item-name {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.item-probability {
  font-size: 26rpx;
  color: #ff9900;
  font-weight: bold;
}

/* 简易统计面板 */
.simple-stats-panel {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
}

.simple-stats-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 20rpx;
}

.stats-category {
  margin-bottom: 20rpx;
}

.simple-stats-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.simple-stats-item.gold-bg {
  background: linear-gradient(135deg, #fffceb, #fff6d6);
  border: 1px solid #ffd700;
}

.simple-stats-item.purple-bg {
  background: linear-gradient(135deg, #f0e6ff, #e6d9ff);
  border: 1px solid #9966cc;
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}

.item-count {
  margin-left: auto;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.no-stats {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 20rpx 0;
}

/* 概率公示面板显示/隐藏样式 */
.probability-panel.show-panel {
  display: block;
}

.probability-panel.hide-panel {
  display: none;
}

/* 概率公示表格样式 */
.probability-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10rpx;
}

.table-header {
  display: flex;
  background-color: #f4f4f4;
  font-weight: bold;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #ddd;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eee;
  padding: 14rpx 0;
}

.table-row:nth-child(even) {
  background-color: #f9f9f9;
}

.table-cell-left {
  flex: 3;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #333;
}

.table-cell-right {
  flex: 1;
  text-align: right;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #ff9900;
  font-weight: bold;
}

.table-header .table-cell-left,
.table-header .table-cell-right {
  color: #333;
  font-size: 28rpx;
}

/* 复选框列表样式 */
.checkbox-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: center;
  margin-top: 20rpx;
}

.checkbox-item {
  width: calc(33.33% - 16rpx);
  background-color: #fff;
  border-radius: 10rpx;
  padding: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid #f5f5f5;
  transition: all 0.3s;
}

.car-checkbox {
  margin-top: 8rpx;
  transform: scale(0.8);
}

.car-image-container {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
}

.car-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 选中状态的复选框项目 */
.checkbox-item-checked {
  background-color: #ffedcc;
  border: 2rpx solid #ff9900;
}

/* 禁用状态的复选框项目 */
.checkbox-item-disabled {
  opacity: 0.5;
}

/* 加载之前保存的设置按钮 */
.load-previous-button {
  background-color: #4a90e2;
  color: #ffffff;
  text-align: center;
  font-size: 28rpx;
  padding: 12rpx 0;
  border-radius: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.load-previous-button:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 自定义复选框列表样式 - 响应式调整 */
.custom-checkbox-list {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns-small), 1fr);
  gap: var(--item-gap-large);
  justify-content: center;
  margin-top: 20rpx;
}

.custom-checkbox-item {
  width: auto;
  margin: 0;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid #f5f5f5;
  transition: all 0.3s;
  position: relative;
}

.custom-checkbox-item-checked {
  background-color: #ffedcc;
  border: 2rpx solid #ff9900;
}

.custom-checkbox-item-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #cccccc;
  border-radius: 6rpx;
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.custom-checkbox-checked {
  border-color: #ff9900;
  background-color: #ff9900;
  color: #ffffff;
}

.custom-checkbox-inner {
  font-size: 24rpx;
  line-height: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 选中标记 */
.selection-mark {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff9900;
  border-radius: 50%;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 游戏规则宽度与下方卡片对齐 */
.card-width {
  width: var(--content-width);
  max-width: 700rpx;
  margin: 20rpx auto;
}

/* 抽卡区域展示优化，一行5个图片 */
.result-list.five-column {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10rpx;
  max-height: 60vh;
  overflow-y: auto;
  padding: 10rpx;
}

.result-list.five-column .result-item {
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  padding: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 8rpx;
}

.result-list.five-column .result-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 0;
  margin-bottom: 6rpx;
}

.result-list.five-column .result-info {
  width: 100%;
  text-align: center;
}

.result-list.five-column .result-name {
  font-size: 20rpx;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 提示完成自选设置样式 */
.setup-reminder {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx auto;
  width: var(--content-width);
  max-width: 680rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.reminder-text {
  font-size: var(--font-size-normal);
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.setup-button {
  display: inline-block;
  background-color: #ff6b6b;
  color: white;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  font-size: var(--font-size-normal);
  margin-top: 10rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.setup-button:active {
  opacity: 0.85;
  transform: scale(0.98);
}

/* 添加横竖屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding-left: 40rpx;
    padding-right: 40rpx;
  }
  
  .results-grid.five-column {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .selection-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .custom-checkbox-list {
    grid-template-columns: repeat(4, 1fr);
  }
}

.vip-badge-container {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 10;
} 

.probability-link {
  margin-top: 16rpx;
}

.highlight-text {
  color: #ffcc00;
  font-size: 26rpx;
}

/* 页面上方排行榜按钮样式 */
.ranking-button-top {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  display: flex;
  align-items: center;
  background-color: #e08400;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.ranking-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
  color: #fff;
}

.ranking-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
}

/* 排行榜按钮点击效果 */
.ranking-button-top:active {
  transform: scale(0.95);
  background-color: #bb6e00;
}

/* 添加彩虹品阶背景 */
.legendary-bg {
  background: linear-gradient(135deg, 
    rgba(255, 0, 0, 0.3) 0%, 
    rgba(255, 165, 0, 0.3) 15%, 
    rgba(255, 255, 0, 0.3) 30%, 
    rgba(0, 255, 0, 0.3) 45%, 
    rgba(0, 0, 255, 0.3) 60%, 
    rgba(75, 0, 130, 0.3) 75%, 
    rgba(148, 0, 211, 0.3) 90%);
  border: 3rpx solid transparent;
  border-image: linear-gradient(to right, 
    red, orange, yellow, green, blue, indigo, violet);
  border-image-slice: 1;
  box-shadow: 0 0 10rpx gold, inset 0 0 10rpx gold;
  animation: glowing 2s infinite;
  position: relative;
  overflow: hidden;
}

@keyframes glowing {
  0% { box-shadow: 0 0 10rpx gold, inset 0 0 10rpx gold; }
  50% { box-shadow: 0 0 20rpx gold, inset 0 0 15rpx gold; }
  100% { box-shadow: 0 0 10rpx gold, inset 0 0 10rpx gold; }
}