<view class="container">
  <image class="bg-image" src="/images/bg.jpg" mode="aspectFill"></image>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <!-- 搜索框和按钮组合 -->
    <view class="search-input-wrap">
      <input
        type="text"
        placeholder="搜索宠物名称"
        placeholder-class="placeholder"
        value="{{searchKey}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        bindfocus="onSearchFocus"
        bindblur="onSearchBlur"
      />
      <view class="search-btn" bindtap="onSearch">查询</view>
    </view>

    <!-- 搜索历史部分 -->
    <view class="search-history {{showSearchHistory ? 'show' : ''}}">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="clear-history" bindtap="clearSearchHistory">清空</text>
      </view>
      <view class="history-list">
        <view class="history-item"
              wx:for="{{searchHistory}}"
              wx:key="*this"
              bindtap="onHistoryItemTap"
              data-keyword="{{item}}">
          <text>{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 筛选条件区域 -->
    <view class="filter-section">
      <picker
        class="filter-item"
        mode="selector"
        range="{{forms}}"
        bindchange="onTypeChange"
        header-text="选择宠物形态"
      >
        <view class="filter-picker">
          <text class="filter-label">
            <text class="filter-icon">🐾</text>宠物形态
          </text>
          <view class="filter-value {{selectedForm !== '全部' ? 'active' : ''}}">
            <text>{{selectedForm || '全部'}}</text>
          </view>
        </view>
      </picker>

      <picker
        class="filter-item"
        mode="selector"
        range="{{skill_keyword}}"
        bindchange="onSkillKeywordChange"
        header-text="选择宝宝类型"
      >
        <view class="filter-picker">
          <text class="filter-label">
            <text class="filter-icon">🔖</text>宝宝类型
          </text>
          <view class="filter-value {{selectedSkillKeyword !== '全部' ? 'active' : ''}}">
            <text>{{selectedSkillKeyword || '全部'}}</text>
          </view>
        </view>
      </picker>

      <picker
        class="filter-item"
        mode="selector"
        range="{{main_attributes}}"
        bindchange="onMainAttributeChange"
        header-text="选择主属性"
      >
        <view class="filter-picker">
          <text class="filter-label">
            <text class="filter-icon">⚡</text>主属性
          </text>
          <view class="filter-value {{selectedMainAttribute !== '全部' ? 'active' : ''}}">
            <text>{{selectedMainAttribute || '全部'}}</text>
          </view>
        </view>
      </picker>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
  </view>

  <!-- 无数据提示 -->
  <view class="empty-tip" wx:elif="{{filteredPets.length === 0}}">
    暂无符合条件的宠物数据
  </view>

  <!-- 宠物列表 -->
  <view class="pet-list" wx:else>
    <view class="pet-card" wx:for="{{filteredPets}}" wx:key="id" bindtap="onPetCardTap" data-id="{{item.id}}">
      <view class="pet-image-container">
        <image
          class="pet-image {{item.imageLoaded ? 'loaded' : ''}}"
          src="{{item.imageUrl}}"
          mode="aspectFit"
          bindload="onImageLoad"
          binderror="onImageError"
          data-index="{{index}}"
          lazy-load
        />
        <view class="image-loading {{!item.imageLoading ? 'hide' : ''}}">
          <view class="loading"></view>
        </view>
      </view>
      <view class="pet-info">
        <view class="pet-header">
          <text class="pet-name">{{item.name}}</text>
          <text class="pet-id">{{item.pet_id}}</text>
        </view>
        <view class="pet-attributes">
          <text class="form-tag">{{item.form === '强化暂未开启，敬请期待!' ? '非骑宠' : item.form}}</text>
          <text class="power-tag">战力 {{item.combat_power}}</text>
        </view>
        <view class="pet-skills">
          <view class="skill-item basic" wx:if="{{item.basic_skill}}">
            <text class="skill-name">{{item.basic_skill}}</text>
            <text class="skill-type">基础</text>
          </view>
          <view class="skill-item enhanced" wx:if="{{item.enhanced_skill}}">
            <text class="skill-name">{{item.enhanced_skill}}</text>
            <text class="skill-type">强化</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部加载提示 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <view class="loading"></view>
      <text>加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && pets.length > 0}}">
      已加载全部数据
    </view>
  </view>

  <!-- 详情弹窗 -->
  <view class="pet-detail-overlay {{expandedPetId !== null ? 'show' : ''}}"
    catchtap="onOverlayTap"
    catchmove="preventTouchMove">
    <view class="pet-detail-card {{expandedPetId !== null ? 'show' : ''}}"
          catchtap="onDetailCardTap"
          catchmove="allowCardTouchMove">
      <view class="close-detail-btn" catchtap="onOverlayTap">×</view>
      <view class="detail-image-container">
        <image
          class="detail-image"
          src="{{expandedPetId !== null ? filteredPets[expandedIndex].imageUrl : ''}}"
          mode="aspectFit"
          bindtap="showFullImage"
          data-url="{{expandedPetId !== null ? filteredPets[expandedIndex].imageUrl : ''}}"
        />
      </view>
      <view class="group-info">
        <text>欢迎加入"飞车图鉴"交流群，QQ群号</text>
        <text class="group-number" bindtap="copyGroupNumber">2156036977</text>
        <text>(点击群号复制)</text>
      </view>
      <view class="detail-header">
        <view class="detail-title">
          <text class="detail-name">{{expandedPetId !== null ? filteredPets[expandedIndex].name : ''}}</text>
          <text class="detail-id">{{expandedPetId !== null ? filteredPets[expandedIndex].pet_id : ''}}</text>
        </view>
        <view class="detail-tags">
          <text class="detail-form">{{expandedPetId !== null ? (filteredPets[expandedIndex].form === '强化暂未开启，敬请期待!' ? '非骑宠' : filteredPets[expandedIndex].form) : ''}}</text>
          <text class="detail-power">战力 {{expandedPetId !== null ? filteredPets[expandedIndex].combat_power : ''}}</text>
        </view>
      </view>

      <scroll-view
        class="detail-content"
        scroll-y
        enable-back-to-top
        scroll-anchoring
        scroll-with-animation
      >
        <view class="detail-sections-container">
          <view class="detail-section">
            <view class="section-title">技能列表</view>
            <view class="skill-list">
              <view class="skill-detail">
                <view class="skill-header">
                  <text class="skill-title">基本技能（满级数值）</text>
                </view>
                <text class="skill-description">{{expandedPetId !== null ? filteredPets[expandedIndex].basic_skill : ''}}</text>
              </view>
              <view class="skill-detail">
                <view class="skill-header">
                  <text class="skill-title">强化技能（强化+10）</text>
                </view>
                <text class="skill-description">{{expandedPetId !== null ? filteredPets[expandedIndex].enhanced_skill : ''}}</text>
              </view>
            </view>
          </view>
          <view class="detail-section">
            <view class="section-title">宠物对战</view>
            <view class="skill-list">
              <view class="attribute-item">
                <text class="label">资质：</text>
                <text class="value">{{expandedPetId !== null ? filteredPets[expandedIndex].aptitude : ''}}</text>
              </view>
              <view class="attribute-item">
                <text class="label">主属性：</text>
                <text class="value">{{expandedPetId !== null ? filteredPets[expandedIndex].main_attribute : ''}}</text>
              </view>
              <view class="skill-detail">
                <view class="skill-header">
                  <text class="skill-title">普通技能</text>
                </view>
                <text class="skill-description">{{expandedPetId !== null ? filteredPets[expandedIndex].normal_skill : ''}}</text>
              </view>
              <view class="skill-detail">
                <view class="skill-header">
                  <text class="skill-title">怒气技能</text>
                </view>
                <text class="skill-description">{{expandedPetId !== null ? filteredPets[expandedIndex].rage_skill : ''}}</text>
              </view>
            </view>
          </view>
          <!-- 底部安全区域 -->
          <view class="safe-area-bottom">
            <view class="thanks-text">感谢语段提供的宠物数据支持！</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 拖动时的遮罩层 - 放在反馈按钮前面，确保按钮在遮罩层之上 -->
  <view class="drag-mask {{isDragging ? 'active' : ''}}"
    catch:touchstart="preventTouchMove"
    catch:touchmove="preventTouchMove"
    catch:touchend="preventTouchMove"
    catch:touchcancel="preventTouchMove">
  </view>

  <!-- 悬浮反馈按钮 -->
  <view class="feedback-btn {{ isDragging ? 'dragging' : '' }}"
    catch:touchstart="onFeedbackBtnTouchStart"
    catch:touchmove="onFeedbackBtnTouchMove"
    catch:touchend="onFeedbackBtnTouchEnd"
    catch:touchcancel="onFeedbackBtnTouchEnd"
    style="left: {{feedbackBtnLeft}}px; top: {{feedbackBtnTop}}px;">
    <image class="feedback-icon" src="/images/feedback.png" mode="aspectFit" />
    <text>反馈</text>
  </view>

  <!-- 返回顶部按钮 -->
  <view class="back-to-top {{showBackToTop ? 'show' : ''}}" bindtap="scrollToTop">
    <text class="back-to-top-icon">↑</text>
    <text>顶部</text>
  </view>

  <!-- 全屏图片预览弹窗 -->
  <view class="full-image-overlay {{showFullImageModal ? 'show' : ''}}" bindtap="hideFullImage">
    <view class="full-image-container" catchtap>
      <image
        class="full-image"
        src="{{fullImageUrl}}"
        mode="aspectFit"
        show-menu-by-longpress="true"
      />
      <view class="full-image-tips">长按图片可保存到手机</view>
      <view class="close-full-image-btn" catchtap="hideFullImage">×</view>
    </view>
  </view>

  <!-- 广告按钮 -->
  <!--<ad-button left="20" top="300"></ad-button>-->
</view>