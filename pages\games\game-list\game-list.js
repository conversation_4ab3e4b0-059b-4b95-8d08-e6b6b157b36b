// pages/games/game-list/game-list.js
const app = getApp();

// 引入API工具类
const api = require('../../../utils/api');

// 页面KEY标识
const PAGE_KEY = 'games';

Page({
  data: {
    games: [
      {
        id: 'game-2048',
        name: '2048',
        icon: '/images/2048/2048-icon.png',
        description: '经典数字方块游戏，合并相同数字获得高分'
      }
      // 未来可以添加更多游戏
    ],
    backgroundImage: '/images/bg.jpg', // 确保使用绝对路径

    // VIP相关数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 导航状态控制
    isNavigating: false,
    loadingNextPage: false
  },

  onLoad: function() {
    // 页面加载时的逻辑
    console.log('小游戏列表页面加载');

    // 重置页面卸载标记
    this.isUnloaded = false;

    // 设置页面级错误处理
    this.setupPageErrorHandlers();

    // 确保背景图片路径正确
    this.setData({
      backgroundImage: '/images/bg.jpg'
    });

    // 初始化VIP和免费次数
    this.initVipAndFreeCount();

    // 初始化VIP状态
    this.initVipStatus();

    // 监听VIP状态变化
    if (app.globalEventEmitter) {
      app.globalEventEmitter.on('vipStatusChanged', this.handleVipStatusChanged);
    }
  },

  onShow: function() {
    // 从缓存读取VIP信息，避免重复请求
    const cachedVipInfo = wx.getStorageSync('vipInfo');
    const cachedToken = wx.getStorageSync('token');

    if (cachedVipInfo && cachedToken) {
      // 有缓存信息，直接更新UI
      this.updateVipStatus(cachedVipInfo);

      // 后台悄悄刷新，但不影响用户体验
      setTimeout(() => {
        this.refreshVipInfoInBackground();
      }, 3000); // 延迟3秒后再刷新，避免页面刚显示就发请求
    } else {
      // 没有缓存，初始化VIP状态
      this.initVipStatus();
    }
  },

  onUnload: function() {
    // 设置页面卸载标记
    this.isUnloaded = true;
    console.log('小游戏列表页面卸载');

    // 清理事件监听
    const app = getApp();
    if (app && app.globalEventEmitter) {
      app.globalEventEmitter.off('vipStatusChanged', this.handleVipStatusChanged);
    }

    // 清理错误处理器
    this._handlePageError = null;

    // 移除未捕获Promise错误处理
    wx.offUnhandledRejection();
  },

  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays
    });
  },

  /**
   * 更新VIP和免费次数
   */
  updateVipAndFreeCount: function() {
    this.initVipAndFreeCount();
  },

  /**
   * 初始化VIP状态
   */
  initVipStatus: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      vipRemainingDays: vipRemainingDays
    });
  },

  /**
   * 更新VIP状态
   */
  updateVipStatus: function(vipInfo) {
    if (!vipInfo) return;

    this.setData({
      isVip: vipInfo.is_valid_vip || false,
      vipRemainingDays: vipInfo.remainingDays || 0
    });
  },

  /**
   * 后台刷新VIP信息
   */
  refreshVipInfoInBackground: function() {
    // 如果页面已卸载，不执行刷新
    if (this.isUnloaded) return;

    // 尝试刷新VIP信息
    app.getVipInfo(true, false)
      .then(vipInfo => {
        // 如果页面已卸载，不更新UI
        if (this.isUnloaded) return;
        this.updateVipStatus(vipInfo);
      })
      .catch(err => {
        console.error('后台刷新VIP信息失败:', err);
      });
  },

  /**
   * 处理VIP状态变化事件
   */
  handleVipStatusChanged: function(vipInfo) {
    // 如果页面已卸载，不更新UI
    if (this.isUnloaded) return;
    this.updateVipStatus(vipInfo);
  },

  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    this.setData({ showVipDialog: true });
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({ showVipDialog: false });
  },

  /**
   * 购买VIP事件
   */
  async onBuyVip() {
    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 获取当前VIP信息
      const currentVipInfo = wx.getStorageSync('vipInfo');
      let expireAt = new Date();

      // 如果已经是VIP，基于原过期时间增加31天
      if (currentVipInfo && currentVipInfo.is_valid_vip && currentVipInfo.vip_expire_at) {
        // 使用原过期时间作为基准
        expireAt = new Date(currentVipInfo.vip_expire_at);
      }

      // 增加31天
      expireAt.setDate(expireAt.getDate() + 31);

      // 调用设置VIP状态接口
      const vipInfo = await app.setVipStatus(true, expireAt.toISOString());

      // 更新页面状态
      this.updateVipStatus(vipInfo);

      // 关闭VIP对话框
      this.setData({
        showVipDialog: false
      });

      // 根据是续费还是首次开通显示不同提示
      wx.showToast({
        title: currentVipInfo?.is_valid_vip ? 'VIP续费成功' : 'VIP开通成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('购买VIP失败:', error);
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 添加免费次数事件
   */
  onAddFreeAttempts: function() {
    // 添加免费次数
    const newFreeCount = api.updateFreeCount(PAGE_KEY, 20);

    this.setData({
      freeCount: newFreeCount,
      showVipDialog: false
    });

    // 显示添加成功提示
    wx.showToast({
      title: '已添加20次免费机会',
      icon: 'success'
    });
  },

  /**
   * 设置页面级错误处理
   */
  setupPageErrorHandlers: function() {
    // 创建页面级错误处理函数
    this._handlePageError = (error) => {
      console.error('小游戏列表页面错误:', error);
      
      // 显示友好的错误提示
      wx.showToast({
        title: '出现了一些问题，请重试',
        icon: 'none',
        duration: 2000
      });
      
      return true; // 表示错误已处理
    };
    
    // 添加页面级的未捕获Promise错误处理
    wx.onUnhandledRejection(res => {
      if (this._handlePageError(res.reason)) {
        // 错误已处理，不需要进一步操作
        return;
      }
      // 否则，让默认处理程序处理
      console.error('小游戏列表页面: 未处理的Promise错误', res.reason);
    });
  },

  // 游戏点击事件处理
  onGameTap: function(e) {
    const gameId = e.currentTarget.dataset.id;
    console.log('游戏点击:', gameId);

    // 防止频繁点击
    if (this.data.isNavigating) {
      console.log('正在导航中，忽略点击');
      return;
    }

    // 设置导航状态
    this.setData({
      isNavigating: true
    });

    // 根据游戏ID执行相应操作
    switch(gameId) {
      case 'game-2048':
        // 预加载下一页面，减少跳转卡顿
        this.setData({
          loadingNextPage: true
        });

        // 保存页面实例的引用
        const self = this;

        // 延迟一帧再跳转，让UI有时间响应
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/games/game-2048/game-2048',
            success: () => {
              console.log('跳转到2048游戏成功');
            },
            fail: (err) => {
              console.error('跳转到2048游戏失败', err);
            },
            complete: () => {
              // 检查页面是否仍然存在
              try {
                // 使用setTimeout确保在下一个事件循环中执行，避免视图更新冲突
                setTimeout(() => {
                  // 再次检查页面是否仍然存在
                  const currentPages = getCurrentPages();
                  const isPageStillValid = currentPages.some(p => p.route === 'pages/games/game-list/game-list');

                  if (isPageStillValid && !self.isUnloaded) {
                    // 重置状态
                    self.setData({
                      isNavigating: false,
                      loadingNextPage: false
                    });
                  }
                }, 100);
              } catch (e) {
                console.log('页面可能已卸载，忽略状态更新');
              }
            }
          });
        }, 50);
        break;
      default:
        // 重置状态
        this.setData({
          isNavigating: false
        });
        break;
    }
  },

  // 反馈功能
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: 'QQ飞车小游戏 - 休闲娱乐',
      path: '/pages/games/game-list/game-list'
    };
  }
});
