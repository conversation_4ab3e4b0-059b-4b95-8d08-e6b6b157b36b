<view class="container">
  <image class="bg-image" src="/images/bg.jpg" mode="aspectFill"></image>
  
  <view class="feedback-list">
    <!-- 反馈列表 -->
    <block wx:for="{{feedbackList}}" wx:key="id">
      <view class="feedback-item">
        <view class="feedback-header">
          <view class="feedback-type">{{item.type_display}}</view>
          <view class="feedback-status {{item.status}}">{{item.status_display}}</view>
        </view>
        <view class="feedback-content">{{item.content}}</view>
        <view class="feedback-footer">
          <text class="feedback-time">提交时间：{{item.created_at}}</text>
        </view>
        <!-- 如果有回复则显示 -->
        <view class="feedback-reply" wx:if="{{item.reply}}">
          <text class="reply-label">作者回复：</text>
          <text class="reply-content">{{item.reply}}</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 分页控制器 -->
  <view class="pagination" wx:if="{{totalPages > 0}}">
    <view class="page-info">
      <text>共 {{totalCount}} 条</text>
      <text class="separator">|</text>
      <text>第 {{currentPage}}/{{totalPages}} 页</text>
    </view>
    <view class="page-controls">
      <view 
        class="page-btn {{currentPage <= 1 ? 'disabled' : ''}}" 
        bindtap="handlePrevPage"
      >上一页</view>
      <view class="page-numbers">
        <view 
          wx:for="{{pageNumbers}}" 
          wx:key="*this"
          class="page-number {{item === currentPage ? 'active' : ''}}"
          bindtap="handlePageClick"
          data-page="{{item}}"
        >{{item}}</view>
      </view>
      <view 
        class="page-btn {{currentPage >= totalPages ? 'disabled' : ''}}" 
        bindtap="handleNextPage"
      >下一页</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-more" wx:if="{{isLoading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态显示 -->
  <view class="empty-state" wx:if="{{!feedbackList.length && !isLoading}}">
    <text>暂无反馈记录</text>
  </view>

  <!-- 广告按钮 -->
  <!--<ad-button left="20" top="300"></ad-button>-->
</view> 