const app = getApp();
const PAGE_KEY = 'lottery';

Page({
  data: {
    isVip: false,
    vipRemainingDays: 0,
    freeCount: 0,
    showVipDialog: false,
    pageKey: PAGE_KEY
    // 其他数据...
  },
  
  onLoad() {
    // 初始化VIP状态
    this.initVipStatus();
    
    // 监听VIP状态变化
    if (app.globalEventEmitter) {
      app.globalEventEmitter.on('vipStatusChanged', this.handleVipStatusChanged);
    }
  },
  
  onShow() {
    // 从缓存读取VIP信息
    const cachedVipInfo = wx.getStorageSync('vipInfo');
    if (cachedVipInfo) {
      this.updateVipStatus(cachedVipInfo);
    } else {
      // 如果没有缓存才重新获取
      this.initVipStatus();
    }
  },
  
  onUnload() {
    // 清理事件监听
    if (app.globalEventEmitter) {
      app.globalEventEmitter.off('vipStatusChanged', this.handleVipStatusChanged);
    }
  },
  
  /**
   * 初始化VIP状态
   */
  async initVipStatus() {
    try {
      const vipInfo = await app.getVipInfo();
      
      if (vipInfo) {
        this.updateVipStatus(vipInfo);
      } else {
        // 如果获取失败，尝试从缓存读取
        const cachedVipInfo = wx.getStorageSync('vipInfo');
        if (cachedVipInfo) {
          this.updateVipStatus(cachedVipInfo);
        }
      }
    } catch (error) {
      console.error('初始化VIP状态失败:', error);
      
      // 尝试从缓存恢复
      const cachedVipInfo = wx.getStorageSync('vipInfo');
      if (cachedVipInfo) {
        this.updateVipStatus(cachedVipInfo);
      }
    }
  },
  
  /**
   * 更新VIP状态显示
   */
  updateVipStatus(vipInfo) {
    if (!vipInfo) return;
    
    // 重新计算剩余天数
    if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
      const expireDate = new Date(vipInfo.vip_expire_at);
      const now = new Date();
      vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
    }
    
    this.setData({
      isVip: vipInfo.is_valid_vip,
      vipRemainingDays: vipInfo.remainingDays || 0
    });
    
    // 更新VIP徽章组件
    const vipBadge = this.selectComponent('#vipBadge');
    if (vipBadge) {
      vipBadge.setData({
        isVip: vipInfo.is_valid_vip,
        remainingDays: vipInfo.remainingDays || 0
      });
    }
  },
  
  /**
   * 处理VIP状态变化事件
   */
  handleVipStatusChanged(vipInfo) {
    this.updateVipStatus(vipInfo);
  },
  
  // 初始化免费次数 - 使用页面特定键名
  initFreeCount() {
    try {
      // 使用页面特定的键名
      const storageKey = `freeCount_${this.data.pageKey}`;
      let freeCount = wx.getStorageSync(storageKey);
      
      if (freeCount === '' || freeCount === undefined) {
        // 兼容旧数据
        freeCount = wx.getStorageSync('freeCount') || 0;
      }
      
      this.setData({ freeCount });
      console.log(`初始化[${this.data.pageKey}]抽奖免费次数:`, freeCount);
      
      // 更新VIP徽章显示
      this.updateVipBadge(freeCount);
    } catch (e) {
      console.error('获取免费次数失败', e);
    }
  },
  
  // 更新VIP徽章显示
  updateVipBadge(freeCount) {
    // 查找页面中的VIP徽章组件
    const vipBadgeComponent = this.selectComponent('#vipBadge');
    if (vipBadgeComponent) {
      vipBadgeComponent.setData({ freeCount });
    }
  },
  
  // 提供给VIP弹窗组件调用的更新方法
  updateLotteryCount(newCount, pageKey) {
    if (!pageKey || pageKey === this.data.pageKey) {
      console.log(`更新[${this.data.pageKey}]抽奖免费次数:`, newCount);
      this.setData({ freeCount: newCount });
      
      // 更新VIP徽章显示
      this.updateVipBadge(newCount);
    }
  },
  
  // VIP弹窗更新事件处理
  onFreeCountUpdated(e) {
    console.log('收到免费次数更新事件:', e.detail);
    // 只有当更新的是当前页面的免费次数时才处理
    if (!e.detail.pageKey || e.detail.pageKey === this.data.pageKey) {
      this.updateLotteryCount(e.detail.freeCount);
    }
  },
  
  // 执行抽奖前检查免费次数
  checkBeforeDraw() {
    // 从本地存储重新读取最新免费次数
    this.initFreeCount();
    
    if (this.data.freeCount <= 0) {
      // 免费次数不足，显示VIP弹窗
      this.setData({ showVipDialog: true });
      return false;
    }
    
    return true;
  },
  
  // 十连抽处理
  onTenDrawTap() {
    if (this.checkBeforeDraw()) {
      // 有足够免费次数，执行抽奖
      const cost = 10; // 十连抽消耗10次
      const newFreeCount = this.data.freeCount - cost;
      
      // 更新免费次数
      this.setData({ freeCount: newFreeCount });
      
      // 使用页面特定键名保存
      const storageKey = `freeCount_${this.data.pageKey}`;
      try {
        wx.setStorageSync(storageKey, newFreeCount);
        
        // 同时更新通用键，用于VIP徽章显示
        const currentCounts = wx.getStorageSync('allFreeCounts') || {};
        currentCounts[this.data.pageKey] = newFreeCount;
        wx.setStorageSync('allFreeCounts', currentCounts);
      } catch (e) {
        console.error('保存免费次数失败', e);
      }
      
      // 更新VIP徽章显示
      this.updateVipBadge(newFreeCount);
      
      // 执行抽奖逻辑
      this.executeDraw(10);
    } else {
      // 显示VIP对话框
      this.setData({ showVipDialog: true });
    }
  }
  
  // 其他方法...
}); 