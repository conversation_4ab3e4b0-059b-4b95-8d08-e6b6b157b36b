/* pages/games/game-list/game-list.wxss */
/* 添加响应式配置，适配不同屏幕尺寸 */
page {
  --content-width: 94%;
  --grid-columns-large: 5;
  --grid-columns-medium: 4;
  --grid-columns-small: 3;
  --item-gap-large: 10rpx;
  --item-gap-small: 6rpx;
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --content-width: 96%;
    --grid-columns-large: 4;
    --grid-columns-medium: 3;
    --grid-columns-small: 2;
    --item-gap-large: 6rpx;
    --item-gap-small: 4rpx;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --content-width: 92%;
    --grid-columns-large: 5;
    --grid-columns-medium: 4;
    --grid-columns-small: 3;
    --item-gap-large: 12rpx;
    --item-gap-small: 8rpx;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

.container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加一个半透明的遮罩层，使背景变暗，提高内容可读性 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.header {
  text-align: center;
  padding: 40rpx 0;
  margin-bottom: 30rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.games-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  width: var(--content-width);
  max-width: 700rpx;
  margin: 0 auto;
}

.game-item {
  display: flex;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
  position: relative;
}

.game-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 加载状态样式 */
.game-item.loading {
  opacity: 0.8;
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(74, 144, 226, 0.3);
  border-top: 4rpx solid rgba(74, 144, 226, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.game-icon {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-icon image {
  width: 80rpx;
  height: 80rpx;
}

.game-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.game-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.game-desc {
  font-size: 24rpx;
  color: #666666;
}

.footer {
  margin-top: 40rpx;
  padding: 30rpx 0;
  text-align: center;
}

.footer-text {
  font-size: 26rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.feedback-button {
  display: inline-block;
  background-color: rgba(74, 144, 226, 0.9);
  color: #ffffff;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.feedback-button navigator {
  color: #ffffff;
}

/* 免责声明卡片 */
.disclaimer-card {
  width: var(--content-width);
  max-width: 700rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 auto 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.disclaimer {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

/* 添加横竖屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding-left: 40rpx;
    padding-right: 40rpx;
  }

  .games-grid {
    width: 85%;
    max-width: 900rpx;
  }
}

/* VIP徽章容器样式 */
.vip-badge-container {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 10;
}
