const { getConfig } = require('../../config')

/**
 * 反馈页面逻辑
 */
Page({
  data: {
    baseUrl: getConfig().baseUrl,
    feedbackTypes: [
      { label: '功能建议', value: 'feature', checked: true },
      { label: 'Bug反馈', value: 'bug' },
      { label: '其他', value: 'other' }
    ],
    type: 'feature',
    content: '',
    contact: '',
    deviceInfo: '',
    groupNumber: '2156036977'
  },

  onLoad() {
    // 获取设备信息
    const deviceInfo = wx.getDeviceInfo();
    const appBaseInfo = wx.getAppBaseInfo();
    const baseDeviceInfo = `系统：${deviceInfo.system}\n机型：${deviceInfo.model}\n微信版本：${appBaseInfo.version}\n平台：${deviceInfo.platform}`;
    this.setData({
      deviceInfo: baseDeviceInfo
    });
  },

  /**
   * 处理反馈类型变化
   * @param {Object} e - 事件对象
   */
  handleTypeChange(e) {
    this.setData({
      type: e.detail.value
    });
  },

  /**
   * 处理反馈内容输入
   * @param {Object} e - 事件对象
   */
  handleContentInput(e) {
    this.setData({
      content: e.detail.value
    });
  },

  /**
   * 处理联系方式输入
   * @param {Object} e - 事件对象
   */
  handleContactInput(e) {
    this.setData({
      contact: e.detail.value
    });
  },

  /**
   * 处理设备信息输入
   * @param {Object} e - 事件对象
   */
  handleDeviceInfoInput(e) {
    this.setData({
      deviceInfo: e.detail.value
    });
  },

  /**
   * 提交反馈
   */
  handleSubmit() {
    const { type, content, contact, deviceInfo } = this.data;
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    // 当类型为bug时，检查设备信息
    if (type === 'bug' && !deviceInfo.trim()) {
      wx.showToast({
        title: '请填写设备信息',
        icon: 'none'
      });
      return;
    }

    const data = {
      type,
      content,
      contact,
      device_info: type === 'bug' ? deviceInfo : ''
    };

    wx.showLoading({
      title: '提交中...'
    });

    // 发送请求
    wx.request({
      url: `${this.data.baseUrl}/api/feedback/`,
      method: 'POST',
      data,
      success: (res) => {
        wx.hideLoading();
        if (res.statusCode === 201 || res.statusCode === 200) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });
          // 清空表单
          this.setData({
            content: '',
            contact: '',
            type: 'feature',
            'feedbackTypes[0].checked': true,
            'feedbackTypes[1].checked': false,
            'feedbackTypes[2].checked': false
          });
        } else {
          wx.showToast({
            title: res.data.message || '提交失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
        console.error('提交反馈失败:', error);
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '意见反馈 - 帮助我们变得更好',
      path: '/pages/feedback/feedback'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '意见反馈 - 一起完善飞车图鉴',
      query: ''
    };
  },

  // 导航到反馈列表页面
  navigateToFeedbackList() {
    wx.navigateTo({
      url: '/pages/feedback-list/feedback-list',
      animationType: 'none'
    })
  },

  // 复制群号到剪贴板
  copyGroupNumber() {
    wx.setClipboardData({
      data: this.data.groupNumber,
      success: () => {
        wx.showToast({
          title: '群号已复制',
          icon: 'success',
          duration: 1500
        });
      }
    });
  }
});