// 工具箱页面逻辑
const app = getApp();

// 引入API工具类
const api = require('../../utils/api');

// 页面KEY标识
const PAGE_KEY = 'toolbox';

Page({
  data: {
    tools: [
      {
        id: 'lottery',
        name: '抽奖模拟器',
        icon: '/images/lottery.png',
        description: '模拟游戏内各种抽奖活动的概率和结果'
      },
      {
        id: 'prize',
        name: '道具出货查询',
        icon: '/images/prize-icon.png',
        description: '查询游戏内各种道具的出货概率和详情'
      }
      ,
      {
        id: 'car-calculation',
        name: '赛车推进计算与绘图',
        icon: '/images/car-icon.png',
        description: '根据原装数据计算满改装推进40并生成动力曲线图'
      }
      // 小游戏入口暂时注释，后续再开放
      /*,{
        id: 'games',
        name: '小游戏',
        icon: '/images/2048/2048-icon.png',
        description: '休闲小游戏集合，包含2048等多种游戏'
      }     */
    ],
    backgroundImage: '/images/bg.jpg', // 确保使用绝对路径

    // VIP相关数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 导航状态控制
    isNavigating: false,
    loadingNextPage: false
  },

  onLoad: function() {
    // 页面加载时的逻辑
    console.log('工具箱页面加载');

    // 重置页面卸载标记
    this.isUnloaded = false;

    // 设置页面级错误处理
    this.setupPageErrorHandlers();

    // 确保背景图片路径正确
    this.setData({
      backgroundImage: '/images/bg.jpg'
    });

    // 初始化VIP和免费次数
    this.initVipAndFreeCount();

    // 初始化VIP状态
    this.initVipStatus();

    // 监听VIP状态变化
    if (app.globalEventEmitter) {
      app.globalEventEmitter.on('vipStatusChanged', this.handleVipStatusChanged);
    }
  },

  /**
   * 设置页面级错误处理器
   */
  setupPageErrorHandlers: function() {
    // 使用应用全局的错误处理函数
    const app = getApp();
    if (app && app.globalHandlePageError) {
      this._handlePageError = app.globalHandlePageError;
    } else {
      // 如果全局处理函数不可用，使用简单的本地处理函数
      this._handlePageError = (error) => {
        if (error) {
          const errMsg = error.errMsg || error.message || String(error);
          if (typeof errMsg === 'string' && (
              errMsg.includes('private_getBackgroundFetchData') ||
              errMsg.includes('miniprogramLog') ||
              errMsg.includes('wxfile://usr/miniprogramLog')
          )) {
            return true; // 表示错误已处理
          }
        }
        return false; // 表示错误未处理
      };
    }

    // 添加页面级的未捕获Promise错误处理
    wx.onUnhandledRejection(res => {
      if (this._handlePageError(res.reason)) {
        // 错误已处理，不需要进一步操作
        return;
      }
      // 否则，让默认处理程序处理
      console.error('工具箱页面: 未处理的Promise错误', res.reason);
    });

    // wx.showToast({
    //   title: '功能开发中',
    //   icon: 'none',
    //   duration: 2000
    // });
  },

  onShow: function() {
    // 从缓存读取VIP信息，避免重复请求
    const cachedVipInfo = wx.getStorageSync('vipInfo');
    const cachedToken = wx.getStorageSync('token');

    if (cachedVipInfo && cachedToken) {
      // 有缓存信息，直接更新UI
      this.updateVipStatus(cachedVipInfo);

      // 后台悄悄刷新，但不影响用户体验
      setTimeout(() => {
        this.refreshVipInfoInBackground();
      }, 3000); // 延迟3秒后再刷新，避免页面刚显示就发请求
    } else {
      // 没有缓存，初始化VIP状态
      this.initVipStatus();
    }
  },

  onUnload: function() {
    // 设置页面卸载标记
    this.isUnloaded = true;
    console.log('工具箱页面卸载');

    // 清理事件监听
    const app = getApp();
    if (app && app.globalEventEmitter) {
      app.globalEventEmitter.off('vipStatusChanged', this.handleVipStatusChanged);
    }

    // 清理错误处理器
    this._handlePageError = null;

    // 移除未捕获Promise错误处理
    wx.offUnhandledRejection();
  },

  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays
    });
  },

  /**
   * 更新VIP和免费次数
   */
  updateVipAndFreeCount: function() {
    this.initVipAndFreeCount();
  },

  /**
   * 初始化VIP状态
   */
  async initVipStatus() {
    try {
      // 先检查缓存中是否有登录信息
      const cachedToken = wx.getStorageSync('token');
      const cachedOpenid = wx.getStorageSync('openid');
      // const cachedUserInfo = wx.getStorageSync('userInfo'); // 暂时不需要用户信息
      const cachedVipInfo = wx.getStorageSync('vipInfo');

      // 如果缓存中有完整的登录信息，直接使用
      if (cachedToken && cachedVipInfo) {
        console.log('使用缓存的登录信息和VIP状态');
        this.updateVipStatus(cachedVipInfo);

        // 在后台刷新VIP信息，不阻塞UI
        this.refreshVipInfoInBackground();
        return;
      }

      // 如果只有登录信息但没有VIP信息，只获取VIP信息
      if (cachedToken && cachedOpenid && !cachedVipInfo) {
        console.log('有登录信息但无VIP信息，只获取VIP状态');
        this.getVipInfoOnly();
        return;
      }

      // 缓存中没有完整登录信息，需要重新获取
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      const vipInfo = await app.getVipInfo();

      if (vipInfo) {
        // 确保计算剩余天数
        if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
          const expireDate = new Date(vipInfo.vip_expire_at);
          const now = new Date();
          vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
        }

        // 更新页面状态
        this.updateVipStatus(vipInfo);

        // 保存到缓存
        wx.setStorageSync('vipInfo', vipInfo);
      } else {
        // 如果获取失败，尝试从缓存读取
        if (cachedVipInfo) {
          this.updateVipStatus(cachedVipInfo);
        } else {
          // 没有缓存，设置默认状态
          this.setData({
            isVip: false,
            vipRemainingDays: 0
          });
        }
      }
    } catch (error) {
      console.error('初始化VIP状态失败:', error);

      // 尝试从缓存恢复
      const cachedVipInfo = wx.getStorageSync('vipInfo');
      if (cachedVipInfo) {
        this.updateVipStatus(cachedVipInfo);
      } else {
        this.setData({
          isVip: false,
          vipRemainingDays: 0
        });
      }
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 在后台刷新VIP信息（不阻塞UI）
   */
  refreshVipInfoInBackground() {
    // 仅在后台刷新VIP信息，不显示加载框，不阻塞UI
    app.getVipInfo().then(vipInfo => {
      if (vipInfo) {
        // 计算剩余天数
        if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
          const expireDate = new Date(vipInfo.vip_expire_at);
          const now = new Date();
          vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
        }

        // 静默更新VIP状态和缓存
        this.updateVipStatus(vipInfo);
        wx.setStorageSync('vipInfo', vipInfo);
        console.log('后台刷新VIP信息成功');
      }
    }).catch(err => {
      console.error('后台刷新VIP信息失败:', err);
    });
  },

  /**
   * 只获取VIP信息（已有登录态的情况）
   */
  getVipInfoOnly() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 使用已有token获取VIP信息
    const token = wx.getStorageSync('token');
    const openid = wx.getStorageSync('openid');

    // 构建请求参数
    const url = `${app.getConfig().baseUrl}/api/user/vip/info/?openid=${openid}`;

    // 发起请求获取VIP信息
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();

        if (res.statusCode === 200 && res.data.success) {
          const vipInfo = res.data.data;

          // 计算剩余天数
          if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
            const expireDate = new Date(vipInfo.vip_expire_at);
            const now = new Date();
            vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
          }

          // 更新页面状态
          this.updateVipStatus(vipInfo);

          // 保存到缓存
          wx.setStorageSync('vipInfo', vipInfo);
          console.log('获取VIP信息成功');
        } else {
          console.error('获取VIP信息失败:', res.data);
          // 尝试从缓存恢复
          const cachedVipInfo = wx.getStorageSync('vipInfo');
          if (cachedVipInfo) {
            this.updateVipStatus(cachedVipInfo);
          }
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取VIP信息请求失败:', err);

        // 尝试从缓存恢复
        const cachedVipInfo = wx.getStorageSync('vipInfo');
        if (cachedVipInfo) {
          this.updateVipStatus(cachedVipInfo);
        }
      }
    });
  },

  /**
   * 更新VIP状态显示
   */
  updateVipStatus(vipInfo) {
    if (!vipInfo) return;

    // 如果没有剩余天数，重新计算
    if (vipInfo.is_valid_vip && vipInfo.vip_expire_at && !vipInfo.remainingDays) {
      const expireDate = new Date(vipInfo.vip_expire_at);
      const now = new Date();
      vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
    }

    this.setData({
      isVip: vipInfo.is_valid_vip,
      vipRemainingDays: vipInfo.remainingDays || 0
    });
  },

  /**
   * 处理VIP状态变化事件
   */
  handleVipStatusChanged(vipInfo) {
    this.updateVipStatus(vipInfo);
  },

  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    this.setData({
      showVipDialog: true
    });
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({
      showVipDialog: false
    });
  },

  /**
   * 购买VIP事件
   */
  async onBuyVip() {
    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 获取当前VIP信息
      const currentVipInfo = wx.getStorageSync('vipInfo');
      let expireAt = new Date();

      // 如果已经是VIP，基于原过期时间增加31天
      if (currentVipInfo && currentVipInfo.is_valid_vip && currentVipInfo.vip_expire_at) {
        // 使用原过期时间作为基准
        expireAt = new Date(currentVipInfo.vip_expire_at);
      }

      // 增加31天
      expireAt.setDate(expireAt.getDate() + 31);

      // 调用设置VIP状态接口
      const vipInfo = await app.setVipStatus(true, expireAt.toISOString());

      // 更新页面状态
      this.updateVipStatus(vipInfo);

      // 关闭VIP对话框
      this.setData({
        showVipDialog: false
      });

      // 根据是续费还是首次开通显示不同提示
      wx.showToast({
        title: currentVipInfo?.is_valid_vip ? 'VIP续费成功' : 'VIP开通成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('购买VIP失败:', error);
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 添加免费次数事件
   */
  onAddFreeAttempts: function() {
    // 添加免费次数
    const newFreeCount = api.updateFreeCount(PAGE_KEY, 20);

    this.setData({
      freeCount: newFreeCount,
      showVipDialog: false
    });

    // 显示添加成功提示
    wx.showToast({
      title: '已添加20次免费机会',
      icon: 'success'
    });
  },

  // 工具点击事件处理
  onToolTap: function(e) {
    const toolId = e.currentTarget.dataset.id;
    console.log('工具点击:', toolId);

    // 防止频繁点击
    if (this.data.isNavigating) {
      console.log('正在导航中，忽略点击');
      return;
    }

    // 设置导航状态
    this.setData({
      isNavigating: true
    });

    // 根据工具ID执行相应操作
    switch(toolId) {
      case 'lottery':
        // 预加载下一页面，减少跳转卡顿
        this.setData({
          loadingNextPage: true
        });

        // 保存页面实例的引用
        const self = this;

        // 显示加载提示，提升用户体验
        wx.showLoading({
          title: '正在加载...',
          mask: false
        });

        // 延迟一帧再跳转，让UI有时间响应
        setTimeout(() => {
          // 检查页面是否仍然存在（防止页面已卸载）
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];

          // 如果当前页面不是工具箱页面，则不执行后续操作
          if (currentPage.route !== 'pages/toolbox/toolbox') {
            console.log('页面已切换，取消导航');
            wx.hideLoading();
            return;
          }

          wx.navigateTo({
            url: '/pages/lottery/lottery-list/lottery-list',
            animationType: 'none',
            success: () => {
              console.log('跳转到抽奖模拟器成功');
              wx.hideLoading();
            },
            fail: (err) => {
              console.error('跳转到抽奖模拟器失败', err);
              wx.hideLoading();
            },
            complete: () => {
              // 检查页面是否仍然存在
              try {
                // 使用setTimeout确保在下一个事件循环中执行，避免视图更新冲突
                setTimeout(() => {
                  // 再次检查页面是否仍然存在
                  const currentPages = getCurrentPages();
                  const isPageStillValid = currentPages.some(p => p.route === 'pages/toolbox/toolbox');

                  if (isPageStillValid && !self.isUnloaded) {
                    // 重置状态
                    self.setData({
                      isNavigating: false,
                      loadingNextPage: false
                    });
                  }
                }, 100);
              } catch (e) {
                console.log('页面可能已卸载，忽略状态更新');
              }
            }
          });
        }, 50);
        break;

      case 'prize':
        // 预加载下一页面，减少跳转卡顿
        this.setData({
          loadingNextPage: true
        });

        // 保存页面实例的引用
        const selfPrize = this;

        // 显示加载提示，提升用户体验
        wx.showLoading({
          title: '正在加载...',
          mask: false
        });

        // 延迟一帧再跳转，让UI有时间响应
        setTimeout(() => {
          // 检查页面是否仍然存在（防止页面已卸载）
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];

          // 如果当前页面不是工具箱页面，则不执行后续操作
          if (currentPage.route !== 'pages/toolbox/toolbox') {
            console.log('页面已切换，取消导航');
            wx.hideLoading();
            return;
          }

          wx.navigateTo({
            url: '/pages/prize/prize-list/prize-list',
            animationType: 'none',
            success: () => {
              console.log('跳转到道具出货查询成功');
              wx.hideLoading();
            },
            fail: (err) => {
              console.error('跳转到道具出货查询失败', err);
              wx.hideLoading();
            },
            complete: () => {
              // 检查页面是否仍然存在
              try {
                // 使用setTimeout确保在下一个事件循环中执行，避免视图更新冲突
                setTimeout(() => {
                  // 再次检查页面是否仍然存在
                  const currentPages = getCurrentPages();
                  const isPageStillValid = currentPages.some(p => p.route === 'pages/toolbox/toolbox');

                  if (isPageStillValid && !selfPrize.isUnloaded) {
                    // 重置状态
                    selfPrize.setData({
                      isNavigating: false,
                      loadingNextPage: false
                    });
                  }
                }, 100);
              } catch (e) {
                console.log('页面可能已卸载，忽略状态更新');
              }
            }
          });
        }, 50);
        break;

      case 'car-calculation':
        // 预加载下一页面，减少跳转卡顿
        this.setData({
          loadingNextPage: true
        });

        // 保存页面实例的引用
        const selfCarCalculation = this;

        // 显示加载提示，提升用户体验
        wx.showLoading({
          title: '正在加载...',
          mask: false
        });

        // 延迟一帧再跳转，让UI有时间响应
        setTimeout(() => {
          // 检查页面是否仍然存在（防止页面已卸载）
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];

          // 如果当前页面不是工具箱页面，则不执行后续操作
          if (currentPage.route !== 'pages/toolbox/toolbox') {
            console.log('页面已切换，取消导航');
            wx.hideLoading();
            return;
          }

          wx.navigateTo({
            url: '/pages/car-calculation/car-calculation',
            animationType: 'none',
            success: () => {
              console.log('跳转到赛车推进计算与绘图成功');
              wx.hideLoading();
            },
            fail: (err) => {
              console.error('跳转到赛车推进计算与绘图失败', err);
              wx.hideLoading();
            },
            complete: () => {
              // 检查页面是否仍然存在
              try {
                // 使用setTimeout确保在下一个事件循环中执行，避免视图更新冲突
                setTimeout(() => {
                  // 再次检查页面是否仍然存在
                  const currentPages = getCurrentPages();
                  const isPageStillValid = currentPages.some(p => p.route === 'pages/toolbox/toolbox');

                  if (isPageStillValid && !selfCarCalculation.isUnloaded) {
                    // 重置状态
                    selfCarCalculation.setData({
                      isNavigating: false,
                      loadingNextPage: false
                    });
                  }
                }, 100);
              } catch (e) {
                console.log('页面可能已卸载，忽略状态更新');
              }
            }
          });
        }, 50);
        break;

      // 小游戏入口暂时注释，后续再开放
      case 'games':
        // 预加载下一页面，减少跳转卡顿
        this.setData({
          loadingNextPage: true
        });

        // 保存页面实例的引用
        const selfGames = this;

        // 延迟一帧再跳转，让UI有时间响应
        setTimeout(() => {
          // 检查页面是否仍然存在（防止页面已卸载）
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];

          // 如果当前页面不是工具箱页面，则不执行后续操作
          if (currentPage.route !== 'pages/toolbox/toolbox') {
            console.log('页面已切换，取消导航');
            return;
          }

          wx.navigateTo({
            url: '/pages/games/game-list/game-list',
            animationType: 'none',
            success: () => {
              console.log('跳转到小游戏列表成功');
            },
            fail: (err) => {
              console.error('跳转到小游戏列表失败', err);
            },
            complete: () => {
              // 检查页面是否仍然存在
              try {
                // 使用setTimeout确保在下一个事件循环中执行，避免视图更新冲突
                setTimeout(() => {
                  // 再次检查页面是否仍然存在
                  const currentPages = getCurrentPages();
                  const isPageStillValid = currentPages.some(p => p.route === 'pages/toolbox/toolbox');

                  if (isPageStillValid && !selfGames.isUnloaded) {
                    // 重置状态
                    selfGames.setData({
                      isNavigating: false,
                      loadingNextPage: false
                    });
                  }
                }, 100);
              } catch (e) {
                console.log('页面可能已卸载，忽略状态更新');
              }
            }
          });
        }, 50);
        break;
      case 'calculator':
      case 'compare':
      case 'petAnalyzer':
      case 'timer':
      case 'guide':
        wx.showModal({
          title: '提示',
          content: '该功能正在开发中，敬请期待！',
          showCancel: false,
          complete: () => {
            // 重置状态
            this.setData({
              isNavigating: false
            });
          }
        });
        break;
      default:
        // 重置状态
        this.setData({
          isNavigating: false
        });
        break;
    }
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: 'QQ飞车工具箱 - 全面提升游戏体验',
      path: '/pages/toolbox/toolbox'
    };
  }
});