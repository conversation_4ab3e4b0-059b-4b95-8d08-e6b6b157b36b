{"compileType": "miniprogram", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": false, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "urlCheck": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.7.4", "packOptions": {"ignore": [], "include": []}, "appid": "wx5f17c4ba385491af", "cloudfunctionRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}