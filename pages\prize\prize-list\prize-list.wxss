/* pages/prize/prize-list/prize-list.wxss */
/* 添加响应式配置，适配不同屏幕尺寸 */
page {
  /* 内容宽度 */
  --content-width: 94%;

  /* 字体大小 - 遵循设计规范 */
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;

  /* 颜色系统 - 遵循设计规范 */
  --primary-color: #4a90e2;
  --primary-gradient: linear-gradient(135deg, #4a90e2, #3670b2);
  --purple-color: #9c27b0;
  --gold-color: #f9d776;
  --bg-color: #f5f7fa;
  --text-main: #333333;
  --text-secondary: #666666;
  --text-hint: #999999;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;

  /* 卡片样式 */
  --card-bg-color: rgba(255, 255, 255, 0.95);
  --card-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  --card-border: 1px solid rgba(74, 144, 226, 0.1);
  --card-radius: 16rpx;

  /* 稀有度颜色 */
  --common-color: #e0e0e0;
  --rare-color: #4a90e2;
  --epic-color: #9c27b0;
  --legendary-color: #f9d776;

  /* 字体 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --content-width: 96%;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --content-width: 92%;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

.container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  overflow-x: hidden; /* 防止水平溢出 */
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加一个半透明的遮罩层，使背景变暗，提高内容可读性 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.header {
  text-align: center;
  padding: 30rpx 0 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  width: var(--content-width);
  max-width: 700rpx;
}

.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 12rpx;
  letter-spacing: 2rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  opacity: 0.9;
}

/* 删除VIP徽章容器样式，避免样式冲突 */

/* 标签页样式 - 按照设计规范调整 */
.tabs-container {
  display: flex;
  justify-content: center;
  width: 96%;
  max-width: 720rpx;
  margin: 0 auto 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 6rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-normal);
  position: relative;
  transition: all 0.3s ease;
  border-radius: 24rpx;
}

.tab.active {
  color: #ffffff;
  background-color: rgba(74, 144, 226, 0.6);
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #ffffff;
  border-radius: 2rpx;
}

/* 搜索区域 - 按照设计规范调整 */
.search-container {
  width: 96%;
  max-width: 720rpx;
  margin: 0 auto 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx;
  padding: 10rpx 16rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.search-box:focus-within {
  box-shadow: 0 4rpx 8rpx rgba(74, 144, 226, 0.25);
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: var(--font-size-normal);
  color: #333;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  color: #fff;
  padding: 0 20rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
  font-size: var(--font-size-normal);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.search-button:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
}

.search-icon image {
  width: 100%;
  height: 100%;
}

/* 筛选区域 - 按照设计规范调整 */
.filter-container {
  width: 96%;
  max-width: 720rpx;
  margin: 0 auto 20rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(74, 144, 226, 0.1);
  box-sizing: border-box;
}

.filter-scroll {
  white-space: nowrap;
  width: 100%;
}

.filter-group {
  display: inline-block;
  margin-right: 20rpx;
  padding-right: 20rpx;
  border-right: 1px solid #eee;
}

.filter-group-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  border-right: none;
}

.filter-group:last-child {
  border-right: none;
  margin-right: 0;
  padding-right: 0;
}

.filter-label {
  font-size: var(--font-size-normal);
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.filter-options {
  display: flex;
  flex-wrap: nowrap;
}

.filter-options-centered {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
}

.filter-option {
  font-size: var(--font-size-normal);
  color: #333;
  background-color: #f5f5f5;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
  white-space: nowrap;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.filter-option:active {
  transform: scale(0.95);
}

.filter-option.active {
  background: var(--primary-gradient);
  color: #fff;
  box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.3);
  font-weight: 500;
  transform: translateY(-2rpx);
}

/* 下拉框样式 */
.filter-dropdown {
  position: relative;
  width: 240rpx;
  display: flex;
  align-items: center;
}

/* 筛选相关样式已移除 */

/* 道具列表 */
.prize-list {
  width: 96%;
  max-width: 720rpx;
  margin: 0 auto;
}

.prize-source-card, .prize-card {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(74, 144, 226, 0.1);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  transform: translateY(0);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.prize-source-card::before, .prize-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: var(--primary-gradient);
  border-radius: 16rpx 16rpx 0 0;
}

/* 悬浮效果 - 使用hover-class实现 */
.card-hover {
  transform: translateY(-4rpx) !important;
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.16), 0 4rpx 12rpx rgba(0, 0, 0, 0.12) !important;
  border-color: rgba(74, 144, 226, 0.2) !important;
}

.prize-source-card:active, .prize-card:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.14), 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  border-color: rgba(74, 144, 226, 0.3);
}

.source-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  padding-bottom: 10rpx;
}

.source-image {
  width: 90rpx;
  height: 90rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f7fa;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.2);
  object-fit: contain;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.source-info {
  flex: 1;
}

.source-name {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.source-type {
  font-size: var(--font-size-small);
  color: #666;
  margin-bottom: 8rpx;
}

/* 奖励标签样式 */
.prize-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8rpx;
}

.prize-tag {
  font-size: var(--font-size-mini);
  color: #333;
  background-color: #f0f0f0;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  margin-bottom: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: inline-block;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.prize-tag.legendary {
  background-color: var(--legendary-color);
  color: #333;
  font-weight: 500;
  border-color: rgba(249, 215, 118, 0.5);
  box-shadow: 0 3rpx 6rpx rgba(249, 215, 118, 0.3), 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.prize-tag.epic {
  background-color: var(--epic-color);
  color: #fff;
  font-weight: 500;
  border-color: rgba(156, 39, 176, 0.5);
  box-shadow: 0 3rpx 6rpx rgba(156, 39, 176, 0.3), 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.prize-tag.rare {
  background-color: var(--rare-color);
  color: #fff;
  font-weight: 500;
  border-color: rgba(74, 144, 226, 0.5);
  box-shadow: 0 3rpx 6rpx rgba(74, 144, 226, 0.3), 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.prize-tag-more {
  font-size: var(--font-size-mini);
  color: #666;
  padding: 4rpx 10rpx;
  margin-bottom: 6rpx;
}

.prize-count {
  font-size: var(--font-size-small);
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.15);
  padding: 6rpx 14rpx;
  border-radius: 20rpx;
  font-weight: 500;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.2);
}

.prize-preview {
  display: flex;
  flex-wrap: wrap;
}

.prize-item {
  width: 30%;
  margin-right: 3%;
  margin-bottom: 10rpx;
  text-align: center;
}

.prize-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-bottom: 8rpx;
  background-color: #f5f7fa;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.prize-image:hover {
  transform: scale(1.05);
}

.prize-name {
  font-size: var(--font-size-mini);
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2rpx;
}

.prize-rarity {
  font-size: var(--font-size-mini);
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  display: inline-block;
}

.prize-rarity.common, .prize-rarity-tag.common {
  background-color: var(--common-color);
  color: #333;
}

.prize-rarity.rare, .prize-rarity-tag.rare {
  background-color: var(--rare-color);
  color: #fff;
}

.prize-rarity.epic, .prize-rarity-tag.epic {
  background-color: var(--epic-color);
  color: #fff;
}

.prize-rarity.legendary, .prize-rarity-tag.legendary {
  background-color: var(--legendary-color);
  color: #333;
}

.more-prizes {
  width: 30%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}

.empty-state text {
  font-size: var(--font-size-normal);
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state text {
  font-size: var(--font-size-normal);
  color: #fff;
}

/* 底部加载动画 */
.bottom-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  opacity: 0.8;
  margin-top: 10rpx;
}

.bottom-loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(74, 144, 226, 0.3);
  border-top: 3rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-right: 10rpx;
}

.bottom-loading text {
  font-size: var(--font-size-small);
  color: #ffffff;
}

/* 加载更多和没有更多 */
.load-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  margin-top: 10rpx;
}

.load-more text, .no-more text {
  font-size: var(--font-size-small);
  color: rgba(255, 255, 255, 0.7);
  background-color: rgba(0, 0, 0, 0.2);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}

/* 广告按钮 */
.ad-button-container {
  width: 96%;
  max-width: 720rpx;
  margin: 20rpx auto;
}

.ad-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #3670b2);
  color: #fff;
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  font-size: var(--font-size-normal);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.ad-button image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 底部区域 - 按照设计规范调整 */
.footer {
  margin-top: 30rpx;
  padding: 20rpx 0;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 免责声明卡片 - 按照设计规范调整 */
.disclaimer-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(74, 144, 226, 0.1);
  position: relative;
  overflow: hidden;
  width: 92%;
}

.disclaimer-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, #4a90e2, #3670b2);
}

.disclaimer {
  font-size: var(--font-size-small);
  color: #555555;
  text-align: center;
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
}

.back-button {
  display: inline-block;
  background: var(--primary-gradient);
  color: #ffffff;
  padding: 16rpx 40rpx;
  border-radius: 12rpx;
  font-size: var(--font-size-normal);
  margin: 20rpx 0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  width: auto;
}

.back-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.back-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.back-button:active::after {
  opacity: 1;
}

.back-button navigator {
  color: #ffffff;
}

/* 新增样式 - 奖品卡片样式 */
.prize-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  position: relative;
  padding-top: 2rpx;
}

.prize-image-large {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  background-color: #f5f7fa;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.2);
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.prize-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0; /* 确保文本可以正确截断 */
  overflow: hidden;
  position: relative;
  padding-right: 10rpx;
}

.prize-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  margin-top: 2rpx;
  padding-right: 10rpx;
}

.prize-name-large {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 100rpx);
  line-height: 1.3;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.prize-quantity {
  font-size: var(--font-size-normal);
  color: #4a90e2;
  margin-left: 10rpx;
  white-space: nowrap;
  font-weight: 600;
  background-color: rgba(74, 144, 226, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

.prize-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow: hidden;
}

.prize-type-tag {
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  font-size: var(--font-size-small);
  color: #fff;
  background-color: #4a90e2;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200rpx;
  line-height: 1.3;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  font-weight: 500;
  z-index: 2;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.prize-rarity-tag {
  display: inline-block;
  font-size: var(--font-size-mini);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.prize-description {
  font-size: var(--font-size-small);
  color: #666;
  margin: 6rpx 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sources-preview {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  background-color: rgba(245, 247, 250, 0.6);
  border-radius: 8rpx;
  padding: 6rpx 10rpx;
}

.sources-title {
  font-size: var(--font-size-small);
  color: #555;
  margin-right: 6rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.sources-list {
  display: flex;
  flex-wrap: wrap;
}

.source-tag {
  font-size: var(--font-size-small);
  color: #333;
  background-color: #fff;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  height: 32rpx;
  max-width: 200rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border: 1px solid rgba(74, 144, 226, 0.2);
}

.source-probability {
  font-size: var(--font-size-small);
  color: var(--primary-color);
  margin-left: 4rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.more-sources {
  font-size: var(--font-size-small);
  color: #4a90e2;
  padding: 0 6rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.source-description {
  font-size: var(--font-size-normal);
  color: #333;
  margin: 10rpx 0;
  padding: 12rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  line-height: 1.5;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  margin-top: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--font-size-mini);
  color: #999;
  opacity: 0.8;
  border-radius: 8rpx;
  background-color: rgba(74, 144, 226, 0.02);
  border: 1px solid rgba(74, 144, 226, 0.1);
}

.expand-button:active {
  background-color: rgba(74, 144, 226, 0.08);
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.expand-button text {
  font-size: var(--font-size-small);
  color: var(--primary-color);
  font-weight: 500;
}

.expand-button image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

.prize-source-card:active .expand-button image,
.prize-card:active .expand-button image {
  transform: translateX(4rpx) rotate(-90deg);
}

/* 弹窗样式 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.detail-modal.show {
  opacity: 1;
  pointer-events: auto;
}

/* 遮罩层样式 */
.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 998;
  overflow: hidden;
}

.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.modal-content {
  width: 90%;
  max-width: 650rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.25);
  position: relative;
  overflow: hidden;
  transform: scale(0.9);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 1000; /* 确保内容在遮罩层之上 */
}

.detail-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  position: relative;
  padding: 30rpx 24rpx;
  background: var(--primary-gradient);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-right: 60rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 1rpx;
}

.close-button {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:active {
  transform: scale(0.9);
  background-color: rgba(255, 255, 255, 0.4);
}

.close-button::before,
.close-button::after {
  content: '';
  position: absolute;
  width: 22rpx;
  height: 2rpx;
  background-color: #fff;
}

.close-button::before {
  transform: rotate(45deg);
}

.close-button::after {
  transform: rotate(-45deg);
}

.modal-body {
  padding: 30rpx;
  max-height: calc(80vh - 180rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 使用scroll-view作为滚动容器 */
scroll-view.modal-body {
  height: calc(80vh - 180rpx);
  box-sizing: border-box;
}

.detail-section {
  margin-bottom: 30rpx;
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.detail-section-title {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.detail-section-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 30rpx;
  background: var(--primary-gradient);
  margin-right: 12rpx;
  border-radius: 4rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.03);
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-label {
  width: 160rpx;
  font-size: var(--font-size-normal);
  color: #666;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  font-size: var(--font-size-normal);
  color: #333;
}

.prize-list-title {
  font-size: var(--font-size-normal);
  font-weight: bold;
  color: #333;
  margin: 20rpx 0 10rpx;
}

.prize-list-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}

.prize-list-item:hover {
  background-color: rgba(74, 144, 226, 0.05);
}

.prize-list-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.prize-list-image {
  width: 70rpx;
  height: 70rpx;
  border-radius: 10rpx;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #fff;
}

.prize-list-info {
  flex: 1;
}

.prize-list-name {
  font-size: var(--font-size-normal);
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.prize-list-meta {
  display: flex;
  align-items: center;
  font-size: var(--font-size-small);
  color: #666;
}

.prize-list-rarity {
  margin-right: 12rpx;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  background-color: #f0f0f0;
}

.prize-list-rarity.legendary {
  background-color: var(--legendary-color);
  color: #333;
}

.prize-list-rarity.epic {
  background-color: var(--epic-color);
  color: #fff;
}

.prize-list-rarity.rare {
  background-color: var(--rare-color);
  color: #fff;
}

.prize-list-probability {
  color: var(--primary-color);
  font-weight: bold;
}
