/**
 * 微信支付 - VIP会员订单下单功能
 */
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 定义VIP套餐价格（单位：分）
const VIP_PLANS = {
  month: {
    price: 1, // 9.9元
    days: 31,
    description: 'VIP月卡会员服务'
  },
  year: {
    price: 1, // 59.9元
    days: 365,
    description: 'VIP年卡会员服务'
  }
};

/**
 * 生成唯一订单号
 * @returns {string} 订单号
 */
function generateOrderNo() {
  // 时间戳 + 随机数
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `VIP${timestamp}${random}`;
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const db = cloud.database();
  
  try {
    // 从事件中获取商品信息
    const { goodsInfo, isRenewal = false } = event;
    
    if (!goodsInfo) {
      return {
        code: -1,
        message: '缺少商品信息参数'
      };
    }
    
    // 获取套餐类型和价格
    const planType = goodsInfo.plan || 'month';
    const plan = VIP_PLANS[planType] || VIP_PLANS.month;
    
    // 使用前端传过来的价格，如果没有则使用默认价格
    const price = goodsInfo.price || plan.price;
    const days = goodsInfo.days || plan.days;
    
    // 商品描述
    const description = isRenewal ? 
      `VIP会员续费(${days}天)` : 
      `VIP会员开通(${days}天)`;
    
    // 生成商户订单号
    const outTradeNo = generateOrderNo();
    
    // 记录订单到数据库
    await db.collection('vip_orders').add({
      data: {
        orderNo: outTradeNo,
        openid: wxContext.OPENID,
        price,
        days,
        planType,
        createTime: db.serverDate(),
        status: 'PENDING', // 待支付
        description,
        isRenewal
      }
    });
    
    console.log(`创建VIP订单成功: ${outTradeNo}, 用户: ${wxContext.OPENID}, 价格: ${price}分`);
    
    // 记录环境信息，帮助排查问题
    console.log('环境上下文信息:', {
      OPENID: wxContext.OPENID,
      APPID: wxContext.APPID,
      ENV: wxContext.ENV,
      SOURCE: wxContext.SOURCE
    });
    
    // 准备请求参数
    const requestData = {
      name: 'wxpay_order',
      data: {
        description,
        amount: {
          total: price, // 订单金额，单位：分
          currency: 'CNY',
        },
        // 商户订单号
        out_trade_no: outTradeNo,
        payer: {
          // 服务端云函数中直接获取当前用户openId
          openid: wxContext.OPENID,
        },
      },
    };
    
    // 记录完整请求参数
    console.log('发送给支付模块的完整参数:', JSON.stringify(requestData));
    
    // 调用微信支付模板下单
    const res = await cloud.callFunction({
      name: 'cloudbase_module',
      data: requestData
    });
    
    console.log('微信支付下单返回结果:', JSON.stringify(res.result));
    
    // 如果返回了错误信息
    if (res.result && res.result.errcode) {
      // 尝试解析错误信息
      let errorDetails = res.result.errmsg;
      try {
        // 尝试解析JSON格式的错误信息
        const errorJson = JSON.parse(res.result.errmsg);
        errorDetails = errorJson.message || errorJson;
        
        // 特别处理签名错误
        if (errorDetails.includes('SIGN_ERROR') || errorDetails.includes('签名错误')) {
          console.error('检测到签名错误，请检查以下可能的原因:');
          console.error('1. 商户号与APPID是否匹配');
          console.error('2. 商户API v3密钥是否正确配置');
          console.error('3. 商户证书是否正确配置');
          console.error('4. 是否使用了正确的环境ID');
        }
      } catch (parseError) {
        // 解析错误时保留原始信息
        console.error('解析错误信息失败:', parseError);
      }
      
      return {
        code: -1,
        message: `微信支付下单失败: ${errorDetails}`,
        error: res.result
      };
    }
    
    // 检查返回结果格式
    if (!res.result || !res.result.data) {
      console.error('微信支付返回结果格式错误:', res.result);
      return {
        code: -1,
        message: '微信支付返回格式错误',
        error: res.result
      };
    }
    
    // 确保支付参数完整
    const payParams = res.result.data;
    if (!payParams.timeStamp || !payParams.nonceStr || 
        !payParams.packageVal || !payParams.paySign) {
      console.error('微信支付返回参数不完整:', payParams);
      return {
        code: -1,
        message: '微信支付返回参数不完整',
        error: payParams
      };
    }
    
    // 确保timeStamp是字符串类型
    if (typeof payParams.timeStamp !== 'string') {
      payParams.timeStamp = String(payParams.timeStamp);
    }
    
    // 返回订单号和支付参数
    return {
      code: 0,
      message: '订单创建成功',
      data: {
        orderNo: outTradeNo,
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        packageVal: payParams.packageVal,
        package: payParams.packageVal, // 同时返回package字段作为备用
        signType: payParams.signType || 'RSA',
        paySign: payParams.paySign
      }
    };
  } catch (error) {
    console.error('创建VIP订单失败:', error);
    return {
      code: -1,
      message: `创建订单失败: ${error.message}`,
      error
    };
  }
};