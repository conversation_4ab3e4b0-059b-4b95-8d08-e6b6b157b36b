.vip-dialog-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.vip-dialog-mask.show {
  opacity: 1;
  visibility: visible;
}

.vip-dialog-container {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(20rpx); opacity: 0.5; }
  to { transform: translateY(0); opacity: 1; }
}

.vip-dialog-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.vip-dialog-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.vip-dialog-close {
  font-size: 46rpx;
  color: #999;
  line-height: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -20rpx;
}

.vip-dialog-content {
  padding: 30rpx;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.option-item {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.option-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.option-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}

.watch-ad-button {
  background-color: #4a90e2;
}

.vip-button {
  background: linear-gradient(135deg, #8B5A00, #FFC700);
  box-shadow: 0 4rpx 8rpx rgba(255, 199, 0, 0.4);
}

.option-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.option-benefits {
  margin: 16rpx 0;
  padding-left: 20rpx;
}

.benefit-item {
  font-size: 26rpx;
  color: #555;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.vip-info {
  padding: 10rpx 0;
}

.vip-status {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.vip-crown {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.vip-text {
  flex: 1;
}

.vip-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  background: linear-gradient(135deg, #8B5A00, #FFC700);
  -webkit-background-clip: text;
  color: transparent;
}

.vip-expiry {
  font-size: 26rpx;
  color: #666;
  margin-top: 5rpx;
}

.vip-days {
  color: #FF6B00;
  font-weight: bold;
  font-size: 30rpx;
}

.vip-benefits-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin: 20rpx 0 10rpx;
}

.vip-plans-container {
  margin-top: 30rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #FFFBF0;
  border: 1rpx solid rgba(255, 199, 0, 0.3);
}

.vip-plans-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.vip-plans {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.vip-plan-item {
  flex: 1;
  margin: 0 10rpx;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.vip-plan-item:first-child {
  margin-left: 0;
}

.vip-plan-item:last-child {
  margin-right: 0;
}

.vip-plan-item.selected {
  border-color: #FFC700;
  background-color: #FFFBF0;
  transform: scale(1.05);
  box-shadow: 0 4rpx 16rpx rgba(255, 199, 0, 0.2);
}

.plan-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.plan-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B00;
  margin: 8rpx 0;
}

.plan-desc {
  font-size: 24rpx;
  color: #666;
}

.plan-tag {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: linear-gradient(135deg, #FF6B00, #FF9500);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  transform: scale(0.8);
}

.renew-container {
  margin-top: 30rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #FFFBF0;
  border: 1rpx solid rgba(255, 199, 0, 0.3);
}

.renew-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.renew-desc {
  font-size: 26rpx;
  color: #666;
  margin: 8rpx 0 16rpx;
}

.free-count-info {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 10rpx;
}

.free-count-number {
  color: #ff6b00;
  font-size: 34rpx;
  font-weight: bold;
}

.free-count-hint {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 20rpx;
} 