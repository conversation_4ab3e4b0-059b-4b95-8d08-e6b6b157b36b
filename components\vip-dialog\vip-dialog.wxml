<view class="vip-dialog-mask {{show ? 'show' : ''}}" wx:if="{{show}}" bindtap="closeDialog">
  <view class="vip-dialog-container" catchtap="prevent">
    <view class="vip-dialog-header">
      <text class="vip-dialog-title">{{isVip ? 'VIP会员信息' : (simpleMode ? 'VIP会员特权' : (freeCount > 0 ? '免费次数' : '次数已用完'))}}</text>
      <view class="vip-dialog-close" bindtap="closeDialog">×</view>
    </view>

    <view class="vip-dialog-content">
      <!-- 已是VIP会员的内容 -->
      <block wx:if="{{isVip}}">
        <view class="vip-info">
          <view class="vip-status">
            <image class="vip-crown" src="/images/vip-icon.png" mode="aspectFit"></image>
            <view class="vip-text">
              <view class="vip-label">VIP会员</view>
              <view class="vip-expiry">还剩 <text class="vip-days">{{vipRemainingDays}}</text> 天到期</view>
            </view>
          </view>

          <view class="vip-benefits-title">您的VIP特权：</view>
          <view class="option-benefits">
            <view class="benefit-item">✅ 无限制使用所有工具箱工具</view>
            <view class="benefit-item">✅ 无广告体验</view>
            <view class="benefit-item">✅ 专属VIP标识</view>
            <view class="benefit-item">✅ 优先体验新功能</view>
          </view>

          <view class="vip-plans-container">
            <view class="vip-plans-title">续费VIP会员</view>

            <view class="vip-plans">
              <view class="vip-plan-item {{selectedPlan === 'month' ? 'selected' : ''}}" bindtap="selectPlan" data-plan="month">
                <view class="plan-name">月卡</view>
                <view class="plan-price">¥9.9</view>
                <view class="plan-desc">31天VIP特权</view>
              </view>

              <view class="vip-plan-item {{selectedPlan === 'year' ? 'selected' : ''}}" bindtap="selectPlan" data-plan="year">
                <view class="plan-name">年卡</view>
                <view class="plan-price">¥59.9</view>
                <view class="plan-desc">365天VIP特权</view>
                <view class="plan-tag">超值优惠</view>
              </view>
            </view>

            <view class="option-button vip-button" bindtap="buyVip">立即续费</view>
          </view>
        </view>
      </block>

      <!-- 非VIP会员的内容 -->
      <block wx:else>
        <view class="content-text">
          <block wx:if="{{simpleMode}}">
            开通VIP会员，享受更多特权：
          </block>
          <block wx:else>
            <block wx:if="{{freeCount > 0}}">
              <view class="free-count-info">您还有 <text class="free-count-number">{{freeCount}}</text> 次免费使用机会</view>
              <view class="free-count-hint">用完后可通过以下方式获取更多次数：</view>
            </block>
            <block wx:else>
              您的免费次数已用完，可以选择以下方式继续使用：
            </block>
          </block>
        </view>

        <view class="option-item" wx:if="{{!simpleMode}}">
          <view class="option-title">
            <text class="option-icon">📺</text>
            <text>观看广告获取免费次数</text>
          </view>
          <view class="option-desc">完整观看30秒广告，获得{{(pageKey === 'prize-list' || pageKey === 'prize-query') ? '3' : '100'}}次免费使用机会</view>
          <view class="option-button watch-ad-button" bindtap="addFreeCount">获取免费次数</view>
        </view>

        <view class="option-item">
          <view class="option-title">
            <text class="option-icon">👑</text>
            <text>成为VIP会员</text>
          </view>
          <view class="option-desc">选择合适的VIP套餐，享受所有特权</view>
          <view class="option-benefits" wx:if="{{simpleMode}}">
            <view class="benefit-item">✅ 无限制使用所有工具箱工具</view>
            <view class="benefit-item">✅ 无广告体验</view>
            <view class="benefit-item">✅ 专属VIP标识</view>
            <view class="benefit-item">✅ 优先体验新功能</view>
          </view>

          <view class="vip-plans">
            <view class="vip-plan-item {{selectedPlan === 'month' ? 'selected' : ''}}" bindtap="selectPlan" data-plan="month">
              <view class="plan-name">月卡</view>
              <view class="plan-price">¥9.9</view>
              <view class="plan-desc">31天VIP特权</view>
            </view>

            <view class="vip-plan-item {{selectedPlan === 'year' ? 'selected' : ''}}" bindtap="selectPlan" data-plan="year">
              <view class="plan-name">年卡</view>
              <view class="plan-price">¥59.9</view>
              <view class="plan-desc">365天VIP特权</view>
              <view class="plan-tag">超值优惠</view>
            </view>
          </view>

          <view class="option-button vip-button" bindtap="buyVip">立即开通</view>
        </view>
      </block>
    </view>
  </view>
</view>