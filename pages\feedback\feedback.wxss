.container {
  min-height: 100vh;
  position: relative;
}

/* 背景图片样式 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.8;
}
.group-number {
  color: #007AFF;
  font-weight: bold;
  text-decoration: underline;
  cursor: pointer;
}

.group-number:active {
  opacity: 0.7;
}
.feedback-container {
  padding: 30rpx;
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.feedback-type {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  border-radius: 12rpx;
}
.type-group {
  display: flex;
  flex-direction: column;
}
.type-item {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}
.type-item text {
  margin-left: 10rpx;
}
.feedback-content {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  border-radius: 12rpx;
}
.content-textarea {
  width: 100%;
  height: 300rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.8);
}
.contact-info {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  border-radius: 12rpx;
}
.contact-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.8);
}
.device-info {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  border-radius: 12rpx;
}
.device-text {
  font-size: 24rpx;
  color: #666;
}
.submit-btn {
  margin-top: 60rpx;
  width: 90%;
  margin: 20rpx auto;
  background: #007AFF;
  color: #fff;
  border: none;
  padding: 20rpx 0;
  font-size: 32rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.section-tip {
  font-size: 24rpx;
  color: #999;
  margin: 8rpx 0;
}
.device-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.8);
}
.scene-info {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  border-radius: 12rpx;
  width: auto;
}
.scene-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.8);
  margin-top: 16rpx;
}
.check-feedback-btn {
  width: 90%;
  margin: 20rpx auto;
  background: #f0f0f0;
  color: #666;
  font-size: 30rpx;
}