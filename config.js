const config = {
  development: {
    baseUrl: 'http://localhost:8000'
  },
  production: {
    baseUrl: 'https://pikario.site'
  }
}

// 根据环境返回对应配置
const env = 'production'  // 切换到生产环境

const getConfig = () => {
  try {
    const currentConfig = config[env];
    if (!currentConfig) {
      console.error('环境配置不存在:', env);
      return config.production; // 默认使用生产环境配置
    }
    return currentConfig;
  } catch (error) {
    console.error('获取配置失败:', error);
    return config.production; // 出错时使用生产环境配置
  }
}

module.exports = {
  getConfig
} 