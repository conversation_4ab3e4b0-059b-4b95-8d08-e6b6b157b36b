<!-- VIP徽章组件 -->
<vip-badge id="vipBadge" 
  isVip="{{isVip}}" 
  freeCount="{{freeCount}}"
  remainingDays="{{vipRemainingDays}}" 
  bindtap="onVipBadgeTap"
/>

<!-- VIP对话框 -->
<vip-dialog 
  show="{{showVipDialog}}" 
  pageKey="{{pageKey}}"
  freeCount="{{freeCount}}"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bind:close="onVipDialogClose"
  bind:buy="onBuyVip"
  bind:freeCountUpdated="onFreeCountUpdated"
/>

<!-- 显示免费次数的UI元素 -->
<view class="free-count-display">
  剩余免费次数: {{freeCount}}
</view>

<!-- 十连抽按钮 -->
<button class="ten-draw-btn" bindtap="onTenDrawTap">十连抽</button> 