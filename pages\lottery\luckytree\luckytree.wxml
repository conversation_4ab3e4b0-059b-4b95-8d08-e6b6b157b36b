<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 页面标题 -->
  <view class="page-title">
    <text>{{activity.name}}</text>
    <text class="subtitle">抽奖概率模拟器</text>
  </view>

  <!-- VIP徽章容器 - 右上角 -->
  <view class="vip-badge-container" bindtap="onVipBadgeTap">
    <vip-badge id="vipBadge"
      pageType="lottery"
      pageKey="luckytree"
      isVip="{{isVip}}"
      freeCount="{{freeCount}}"
      remainingDays="{{vipRemainingDays}}">
    </vip-badge>
  </view>

  <!-- 添加排行榜按钮到活动信息区域 -->
  <!--<view class="ranking-button-top" bindtap="openRanking">
    <text class="ranking-icon">🏆</text>
    <text class="ranking-text">排行榜</text>
  </view>-->

  <!-- 活动规则卡片 - 深色半透明 -->
  <view class="rules-card dark-card">
    <text class="rules-title">活动规则</text>
    <view class="rules-content">
      <text>1. 通过单抽和十连抽模拟幸运摇钱树活动</text>
      <text>2. 抽奖结果会在统计面板中记录，可随时查看</text>
      <text>3. 抽奖概率基于游戏公示数据，仅供娱乐参考</text>
      <view class="probability-link" bindtap="toggleProbabilityPanel">
        <text class="highlight-text">查看概率公示 >></text>
      </view>
    </view>
  </view>

  <!-- 概率公示面板 - 深色半透明 -->
  <view class="probability-panel dark-card {{showProbabilityPanel ? 'show-panel' : 'hide-panel'}}">
    <view class="panel-header">
      <text class="panel-title">概率公示</text>
      <view class="close-button" bindtap="toggleProbabilityPanel">×</view>
    </view>

    <view class="probability-table">
      <view class="table-header">
        <text class="table-cell-left">奖励</text>
        <text class="table-cell-right">概率</text>
      </view>

      <!-- 金色品质奖励 -->
      <view class="table-row gold-row">
        <text class="table-cell-left">龙胆逆鳞/炼狱使者（永久）</text>
        <text class="table-cell-right">0.001%</text>
      </view>
      <view class="table-row gold-row">
        <text class="table-cell-left">月影/利刃针尖(永久)</text>
        <text class="table-cell-right">0.002%</text>
      </view>
      <view class="table-row gold-row">
        <text class="table-cell-left">龙胆逆鳞/炼狱使者(15天)</text>
        <text class="table-cell-right">0.042%</text>
      </view>
      <view class="table-row gold-row">
        <text class="table-cell-left">月影/利刃针尖(30天)</text>
        <text class="table-cell-right">4.205%</text>
      </view>

      <!-- 紫色品质奖励 -->
      <view class="table-row purple-row">
        <text class="table-cell-left">稀有套装服饰(永久)</text>
        <text class="table-cell-right">0.168%</text>
      </view>
      <view class="table-row purple-row">
        <text class="table-cell-left">稀有套装发饰(永久)</text>
        <text class="table-cell-right">0.841%</text>
      </view>
      <!-- 其他奖励 -->
      <view class="table-row">
        <text class="table-cell-left">改装道具+1 x 88</text>
        <text class="table-cell-right">4.205%</text>
      </view>
      <view class="table-row">
        <text class="table-cell-left">点券×28888</text>
        <text class="table-cell-right">4.205%</text>
      </view>
      <view class="table-row">
        <text class="table-cell-left">点券×26888</text>
        <text class="table-cell-right">5.046%</text>
      </view>
      <view class="table-row">
        <text class="table-cell-left">点券×18888</text>
        <text class="table-cell-right">12.615%</text>
      </view>
      <view class="table-row">
        <text class="table-cell-left">点券×16888</text>
        <text class="table-cell-right">15.138%</text>
      </view>
      <view class="table-row">
        <text class="table-cell-left">点券×8888</text>
        <text class="table-cell-right">25.23%</text>
      </view>
    </view>
  </view>

  <!-- 中奖结果展示区域 - 白色卡片 -->
  <view class="results-display light-card" wx:if="{{showResult && !running}}">
    <view class="results-title">{{isMultiDraw ? '十连抽结果' : '单抽结果'}}</view>

    <!-- 单抽结果显示 -->
    <view class="single-result-container" wx:if="{{!isMultiDraw}}">
      <view class="single-result-item {{results[0].background === 'gold' ? 'gold-bg' : (results[0].background === 'purple' ? 'purple-bg' : '')}}">
        <image src="{{results[0].image}}" mode="aspectFit"></image>
        <text class="result-name">{{results[0].name}}</text>
      </view>
    </view>

    <!-- 十连抽结果显示 -->
    <view class="results-grid" wx:if="{{isMultiDraw}}">
      <view class="result-grid-item {{item.background === 'gold' ? 'gold-bg' : (item.background === 'purple' ? 'purple-bg' : '')}}"
            wx:for="{{results}}" wx:key="index">
        <image src="{{item.image}}" mode="aspectFit"></image>
        <text>{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 抽奖按钮区域 -->
  <view class="draw-buttons-container">
    <view class="draw-button single {{running ? 'disabled' : ''}}" bindtap="singleDraw">单抽</view>
    <view class="draw-button multi {{running ? 'disabled' : ''}}" bindtap="multiDraw">十连抽</view>
  </view>

  <!-- 抽奖进行中的动画 -->
  <view class="draw-animation" wx:if="{{running}}">
    <view class="loading">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="loading-text">抽奖中...</text>
  </view>

  <!-- 抽奖统计 - 白色卡片 -->
  <view class="stats-container light-card">
    <view class="stats-header">
      <text class="stats-title">抽奖统计</text>
      <view class="stats-summary-container">
        <view class="stats-summary-row">
          <text class="stats-summary">总抽数: {{totalDraws}} | 预计花费: {{totalDraws*3 || 0}}元 | 累计点券: {{totalTickets}}</text>
        </view>
      </view>
      <view class="reset-button" bindtap="resetStats">重置</view>
    </view>

    <!-- 金色品质奖励统计 -->
    <view class="stats-category">
      <text class="category-title">金色稀有奖励</text>
      <view class="simple-stats-list">
        <view class="simple-stats-item gold-bg" wx:for="{{statistics}}" wx:key="id" wx:if="{{item.item.background === 'gold' && item.count > 0}}">
          <image class="item-icon" src="{{item.item.image}}" mode="aspectFit"></image>
          <text class="item-name">{{item.item.name}}</text>
          <text class="item-count">{{item.count}}次</text>
        </view>
        <view class="no-stats" wx:if="{{!hasGoldItems}}">
          <text>暂无获得</text>
        </view>
      </view>
    </view>

    <!-- 紫色品质奖励统计 -->
    <view class="stats-category">
      <text class="category-title">紫色稀有奖励</text>
      <view class="simple-stats-list">
        <view class="simple-stats-item purple-bg" wx:for="{{statistics}}" wx:key="id" wx:if="{{item.item.background === 'purple' && item.count > 0}}">
          <image class="item-icon" src="{{item.item.image}}" mode="aspectFit"></image>
          <text class="item-name">{{item.item.name}}</text>
          <text class="item-count">{{item.count}}次</text>
        </view>
        <view class="no-stats" wx:if="{{!hasPurpleItems}}">
          <text>暂无获得</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部区域 -->
  <view class="footer">
    <!-- 免责声明卡片 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 抽奖概率基于游戏公示数据，仅供娱乐，非官方工具</view>
    </view>

    <!-- 排行榜和返回按钮容器 -->
    <view class="bottom-buttons">
      <view class="ranking-button" bindtap="openRanking">
        排行榜
      </view>
      <view class="back-button" bindtap="goBack">
        返回活动列表
      </view>
    </view>

    <!-- 广告组件 -->
    <!--<view class="ad-container">
      <ad-button text="支持我们" />
    </view>-->
  </view>
</view>

<!-- 在文件末尾添加自定义导航栏 -->
<custom-tabbar selected="2"></custom-tabbar>

<!-- VIP对话框组件 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="luckytree"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  freeCount="{{freeCount}}"
  bind:close="onVipDialogClose"
  bind:buy="onBuyVip">
</vip-dialog>