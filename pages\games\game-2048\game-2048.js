// pages/games/game-2048/game-2048.js
const app = getApp();

// 引入API工具类
const api = require('../../../utils/api');

// 页面KEY标识
const PAGE_KEY = 'game-2048';

// API基础URL
const API_BASE_URL = 'https://pikario.site/api';

// 游戏配置
const GRID_SIZE = 4;
const GAME_STORAGE_KEY = 'game_2048_state';
const BEST_SCORE_STORAGE_KEY = 'game_2048_best_score';

// 积分系统配置
const SCORE_CONFIG = {
  // 连击奖励系数
  COMBO_MULTIPLIERS: [1, 1.1, 1.2, 1.5, 2],
  // VIP用户每局游戏的撤销次数限制
  VIP_UNDO_LIMIT: 3
};

// 音效配置
const SOUND_EFFECTS = {
  MOVE: '/audio/move.mp3',
  MERGE: '/audio/merge.mp3',
  APPEAR: '/audio/appear.mp3',
  WIN: '/audio/win.mp3',
  LOSE: '/audio/lose.mp3'
};

Page({
  data: {
    // 游戏数据
    tiles: [],
    score: 0,
    bestScore: 0,
    gameOver: false,
    gameWon: false,
    hasUploaded: false,
    canUndo: false,

    // 触摸数据
    startX: 0,
    startY: 0,

    // 背景图片
    backgroundImage: '/images/bg.jpg',

    // VIP相关数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,
    undoCount: 0, // 当前游戏已使用的撤销次数

    // 用户信息
    userInfo: null,
    isLoggedIn: false,
    showUserInfoModal: false,
    nickName: '',

    // 游戏统计
    moves: 0,
    gameTime: 0,
    gameStartTime: 0,
    maxTile: 0,

    // 积分系统
    comboCount: 0, // 连击次数
    comboMultiplier: 1, // 连击奖励系数
    baseScore: 0, // 基础积分
    finalScore: 0, // 最终得分（包含连击和时间奖励）
    timeBonus: 0, // 时间奖励

    // 音效设置
    soundEnabled: true, // 是否启用音效

    // 游戏设置
    showSettingsModal: false // 设置弹窗
  },

  onLoad: function() {
    console.log('2048游戏页面加载');

    // 设置背景图片
    this.setData({
      backgroundImage: '/images/bg.jpg'
    });

    // 初始化VIP状态
    this.initVipAndFreeCount();

    // 加载最高分
    this.loadBestScore();

    // 加载音效设置
    this.loadSoundSettings();

    // 检查游戏次数限制（非VIP用户）
    if (!this.checkGameLimit()) {
      return;
    }

    // 如果非VIP用户，减少免费次数（无论是恢复还是新游戏都消耗一次）
    if (!this.data.isVip) {
      const newFreeCount = api.updateFreeCount(PAGE_KEY, -1);
      this.setData({ freeCount: newFreeCount });
    }

    // 尝试恢复游戏状态
    if (!this.restoreGameState()) {
      // 如果没有恢复成功，开始新游戏（不再消耗免费次数，因为已经在上面消耗了）
      this.startNewGameWithoutCost();
    }

    // 检查登录状态
    this.checkLoginStatus();
  },

  onShow: function() {
    // 如果游戏正在进行中，继续计时
    if (!this.data.gameOver && this.data.gameStartTime > 0) {
      this.startTimer();
    }
  },

  onHide: function() {
    // 停止计时器
    this.stopTimer();

    // 保存游戏状态
    this.saveGameState();
  },

  onUnload: function() {
    // 停止计时器
    this.stopTimer();

    // 保存游戏状态
    this.saveGameState();
  },

  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    // 从缓存中获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    if (userInfo && token) {
      try {
        // 尝试解析用户信息
        const parsedUserInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;

        this.setData({
          userInfo: parsedUserInfo,
          isLoggedIn: true,
          nickName: parsedUserInfo.nickName || ''
        });
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }
  },

  /**
   * 加载最高分
   */
  loadBestScore: function() {
    const bestScore = wx.getStorageSync(BEST_SCORE_STORAGE_KEY) || 0;
    this.setData({ bestScore });
  },

  /**
   * 保存最高分
   */
  saveBestScore: function() {
    const { score, bestScore } = this.data;
    if (score > bestScore) {
      const newBestScore = score;
      wx.setStorageSync(BEST_SCORE_STORAGE_KEY, newBestScore);
      this.setData({ bestScore: newBestScore });
    }
  },

  /**
   * 保存游戏状态
   */
  saveGameState: function() {
    const gameState = {
      tiles: this.data.tiles,
      score: this.data.score,
      baseScore: this.data.baseScore,
      gameOver: this.data.gameOver,
      gameWon: this.data.gameWon,
      moves: this.data.moves,
      gameTime: this.data.gameTime,
      gameStartTime: this.data.gameStartTime,
      maxTile: this.data.maxTile,
      previousState: this.previousState,
      comboCount: this.data.comboCount,
      comboMultiplier: this.data.comboMultiplier,
      undoCount: this.data.undoCount,
      finalScore: this.data.finalScore,
      timeBonus: this.data.timeBonus
    };

    wx.setStorageSync(GAME_STORAGE_KEY, gameState);
  },

  /**
   * 恢复游戏状态
   * @returns {boolean} 是否成功恢复
   */
  restoreGameState: function() {
    const gameState = wx.getStorageSync(GAME_STORAGE_KEY);

    if (gameState) {
      this.setData({
        tiles: gameState.tiles || [],
        score: gameState.score || 0,
        baseScore: gameState.baseScore || 0,
        gameOver: gameState.gameOver || false,
        gameWon: gameState.gameWon || false,
        moves: gameState.moves || 0,
        gameTime: gameState.gameTime || 0,
        gameStartTime: gameState.gameStartTime || 0,
        maxTile: gameState.maxTile || 0,
        comboCount: gameState.comboCount || 0,
        comboMultiplier: gameState.comboMultiplier || 1,
        undoCount: gameState.undoCount || 0,
        finalScore: gameState.finalScore || 0,
        timeBonus: gameState.timeBonus || 0
      });

      this.previousState = gameState.previousState;

      // 如果游戏没有结束，继续计时
      if (!gameState.gameOver && gameState.gameStartTime > 0) {
        this.startTimer();
      }

      // 检查是否可以撤销
      const canUndoByVip = this.data.isVip && this.data.undoCount < SCORE_CONFIG.VIP_UNDO_LIMIT;
      this.setData({
        canUndo: this.previousState != null && canUndoByVip
      });

      return true;
    }

    return false;
  },

  /**
   * 开始新游戏（消耗免费次数）
   */
  startNewGame: function() {
    // 停止计时器
    this.stopTimer();

    // 初始化游戏数据
    const tiles = [];

    // 清空网格
    this.setData({
      tiles: [],
      score: 0,
      baseScore: 0,
      gameOver: false,
      gameWon: false,
      hasUploaded: false,
      moves: 0,
      gameTime: 0,
      maxTile: 0,
      canUndo: false,
      comboCount: 0,
      comboMultiplier: 1,
      undoCount: 0,
      finalScore: 0,
      timeBonus: 0
    });

    // 添加初始方块
    this.addRandomTile();
    this.addRandomTile();

    // 开始计时
    this.startTimer();

    // 清除上一步状态
    this.previousState = null;

    // 如果非VIP用户，减少免费次数
    if (!this.data.isVip) {
      const newFreeCount = api.updateFreeCount(PAGE_KEY, -1);
      this.setData({ freeCount: newFreeCount });
    }

    // 播放新方块出现音效
    this.playSound(SOUND_EFFECTS.APPEAR);
  },

  /**
   * 开始新游戏（不消耗免费次数）
   */
  startNewGameWithoutCost: function() {
    // 停止计时器
    this.stopTimer();

    // 初始化游戏数据
    const tiles = [];

    // 清空网格
    this.setData({
      tiles: [],
      score: 0,
      baseScore: 0,
      gameOver: false,
      gameWon: false,
      hasUploaded: false,
      moves: 0,
      gameTime: 0,
      maxTile: 0,
      canUndo: false,
      comboCount: 0,
      comboMultiplier: 1,
      undoCount: 0,
      finalScore: 0,
      timeBonus: 0
    });

    // 添加初始方块
    this.addRandomTile();
    this.addRandomTile();

    // 开始计时
    this.startTimer();

    // 清除上一步状态
    this.previousState = null;

    // 播放新方块出现音效
    this.playSound(SOUND_EFFECTS.APPEAR);
  },

  /**
   * 开始计时器
   */
  startTimer: function() {
    // 如果游戏已经结束，不启动计时器
    if (this.data.gameOver) return;

    // 如果没有开始时间，设置开始时间
    if (this.data.gameStartTime === 0) {
      this.setData({
        gameStartTime: Date.now()
      });
    }

    // 清除现有计时器
    if (this.timer) {
      clearInterval(this.timer);
    }

    // 启动新计时器
    this.timer = setInterval(() => {
      const currentTime = Date.now();
      const elapsedTime = Math.floor((currentTime - this.data.gameStartTime) / 1000) + this.data.gameTime;

      this.setData({
        gameTime: elapsedTime
      });
    }, 1000);
  },

  /**
   * 停止计时器
   */
  stopTimer: function() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  /**
   * 添加随机方块
   */
  addRandomTile: function() {
    // 获取空格子
    const emptyCells = this.getEmptyCells();

    if (emptyCells.length > 0) {
      // 随机选择一个空格子
      const randomIndex = Math.floor(Math.random() * emptyCells.length);
      const cell = emptyCells[randomIndex];

      // 90%概率生成2，10%概率生成4
      const value = Math.random() < 0.9 ? 2 : 4;

      // 创建新方块
      const newTile = {
        id: Date.now().toString() + Math.floor(Math.random() * 1000),
        x: cell.x,
        y: cell.y,
        value: value,
        isNew: true,
        isMerged: false
      };

      // 添加到方块数组
      const tiles = [...this.data.tiles, newTile];

      this.setData({ tiles });

      // 更新最大方块值
      if (value > this.data.maxTile) {
        this.setData({ maxTile: value });
      }

      // 播放新方块出现音效
      this.playSound(SOUND_EFFECTS.APPEAR);

      // 检查图片是否存在，如果不存在则使用默认样式
      wx.getFileSystemManager().access({
        path: `/images/2048/${value}.png`,
        fail: () => {
          console.warn(`图片 /images/2048/${value}.png 不存在，使用默认样式`);
        }
      });
    }
  },

  /**
   * 获取空格子
   * @returns {Array} 空格子数组
   */
  getEmptyCells: function() {
    const cells = [];
    const { tiles } = this.data;

    // 遍历所有格子
    for (let y = 0; y < GRID_SIZE; y++) {
      for (let x = 0; x < GRID_SIZE; x++) {
        // 检查该位置是否有方块
        const hasTile = tiles.some(tile => tile.x === x && tile.y === y);

        if (!hasTile) {
          cells.push({ x, y });
        }
      }
    }

    return cells;
  },

  /**
   * 触摸开始事件
   */
  onTouchStart: function(e) {
    if (this.data.gameOver) return;

    this.setData({
      startX: e.touches[0].clientX,
      startY: e.touches[0].clientY
    });
  },

  /**
   * 触摸结束事件
   */
  onTouchEnd: function(e) {
    if (this.data.gameOver) return;

    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;

    const { startX, startY } = this.data;

    // 计算滑动距离
    const deltaX = endX - startX;
    const deltaY = endY - startY;

    // 如果滑动距离太小，忽略
    if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) return;

    // 保存当前状态用于撤销
    this.saveCurrentState();

    // 判断滑动方向
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // 水平滑动
      if (deltaX > 0) {
        this.move('right');
      } else {
        this.move('left');
      }
    } else {
      // 垂直滑动
      if (deltaY > 0) {
        this.move('down');
      } else {
        this.move('up');
      }
    }
  },



  /**
   * 保存当前状态用于撤销
   */
  saveCurrentState: function() {
    this.previousState = {
      tiles: JSON.parse(JSON.stringify(this.data.tiles)),
      score: this.data.score,
      baseScore: this.data.baseScore,
      moves: this.data.moves,
      maxTile: this.data.maxTile,
      comboCount: this.data.comboCount,
      comboMultiplier: this.data.comboMultiplier
    };

    // 更新撤销按钮状态
    const canUndoByVip = this.data.isVip && this.data.undoCount < SCORE_CONFIG.VIP_UNDO_LIMIT;
    this.setData({
      canUndo: canUndoByVip
    });
  },

  /**
   * 加载音效设置
   */
  loadSoundSettings: function() {
    const soundEnabled = wx.getStorageSync('game_2048_sound_enabled');
    // 如果没有设置过，默认开启音效
    if (soundEnabled === '' || soundEnabled === undefined) {
      wx.setStorageSync('game_2048_sound_enabled', true);
      this.setData({ soundEnabled: true });
    } else {
      this.setData({ soundEnabled: soundEnabled });
    }
  },

  /**
   * 保存音效设置
   */
  saveSoundSettings: function() {
    wx.setStorageSync('game_2048_sound_enabled', this.data.soundEnabled);
  },

  /**
   * 切换音效设置
   */
  toggleSound: function() {
    const newSoundEnabled = !this.data.soundEnabled;
    this.setData({ soundEnabled: newSoundEnabled });
    this.saveSoundSettings();

    wx.showToast({
      title: newSoundEnabled ? '音效已开启' : '音效已关闭',
      icon: 'none'
    });
  },

  /**
   * 播放音效
   * @param {string} soundPath 音效路径
   */
  playSound: function(soundPath) {
    if (!this.data.soundEnabled) return;

    // 播放音效
    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.src = soundPath;
    innerAudioContext.play();

    // 播放完成后销毁实例
    innerAudioContext.onEnded(() => {
      innerAudioContext.destroy();
    });

    // 错误处理
    innerAudioContext.onError((res) => {
      console.error('音效播放失败:', res);
      innerAudioContext.destroy();
    });
  },

  /**
   * 检查游戏次数限制
   * @returns {boolean} 是否可以继续游戏
   */
  checkGameLimit: function() {
    // VIP用户无限制
    if (this.data.isVip) return true;

    // 非VIP用户检查免费次数
    if (this.data.freeCount <= 0) {
      wx.showModal({
        title: '游戏次数已用完',
        content: '非VIP用户每天只能免费体验1次，观看广告可获得更多次数，开通VIP可无限游戏',
        confirmText: '看广告',
        cancelText: '开通VIP',
        success: (res) => {
          if (res.confirm) {
            // 用户点击了"看广告"
            this.watchAdForFreeCount();
          } else {
            // 用户点击了"开通VIP"
            this.setData({ showVipDialog: true });
          }
        }
      });
      return false;
    }

    return true;
  },

  /**
   * 观看广告获取免费次数
   */
  watchAdForFreeCount: function() {
    console.log('观看广告获取免费次数');

    // 创建激励视频广告实例
    if (!this.videoAd && wx.createRewardedVideoAd) {
      this.videoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-919b8b04a2997a01'
      });

      // 监听加载事件
      this.videoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      // 监听错误事件
      this.videoAd.onError((err) => {
        console.error('激励视频广告加载失败:', err);

        // 在开发环境中模拟广告成功
        if (err.errCode === 1004 || err.errCode === 1007) {
          console.log('开发环境模拟广告成功');

          // 延迟执行，模拟广告播放完成
          setTimeout(() => {
            // 增加免费次数
            const newFreeCount = api.updateFreeCount(PAGE_KEY, 1);
            this.setData({ freeCount: newFreeCount });

            wx.showToast({
              title: '获得1次免费游戏机会',
              icon: 'success'
            });
          }, 1000);
        } else {
          wx.showToast({
            title: '广告加载失败，请稍后再试',
            icon: 'none'
          });
        }
      });

      // 监听关闭事件
      this.videoAd.onClose((res) => {
        // 用户完整观看广告
        if (res && res.isEnded) {
          console.log('用户完整观看广告，奖励免费次数');

          // 增加免费次数
          const newFreeCount = api.updateFreeCount(PAGE_KEY, 1);
          this.setData({ freeCount: newFreeCount });

          wx.showToast({
            title: '获得1次免费游戏机会',
            icon: 'success'
          });
        } else {
          console.log('用户未完整观看广告');
          wx.showToast({
            title: '需完整观看广告才能获得奖励',
            icon: 'none'
          });
        }
      });
    }

    // 显示广告
    if (this.videoAd) {
      wx.showLoading({
        title: '广告加载中...',
        mask: true
      });

      this.videoAd.show()
        .then(() => {
          wx.hideLoading();
        })
        .catch(err => {
          wx.hideLoading();
          console.error('激励视频广告显示失败:', err);

          // 重新加载广告
          this.videoAd.load()
            .then(() => this.videoAd.show())
            .catch(err => {
              console.error('广告重新加载/显示失败:', err);

              // 在开发环境中模拟广告成功
              console.log('模拟广告成功');

              // 延迟执行，模拟广告播放完成
              setTimeout(() => {
                // 增加免费次数
                const newFreeCount = api.updateFreeCount(PAGE_KEY, 1);
                this.setData({ freeCount: newFreeCount });

                wx.showToast({
                  title: '获得1次免费游戏机会',
                  icon: 'success'
                });
              }, 1000);
            });
        });
    } else {
      console.log('当前环境不支持激励视频广告');

      // 在不支持广告的环境中，直接赠送免费次数
      wx.showToast({
        title: '当前环境不支持广告，直接赠送免费次数',
        icon: 'none',
        duration: 2000
      });

      setTimeout(() => {
        // 增加免费次数
        const newFreeCount = api.updateFreeCount(PAGE_KEY, 1);
        this.setData({ freeCount: newFreeCount });
      }, 2000);
    }
  },

  /**
   * 撤销上一步
   */
  undoMove: function() {
    // 只有VIP用户可以撤销
    if (!this.data.isVip) {
      this.setData({ showVipDialog: true });
      return;
    }

    // 检查是否有上一步状态
    if (!this.previousState) return;

    // 检查撤销次数限制
    if (this.data.undoCount >= SCORE_CONFIG.VIP_UNDO_LIMIT) {
      wx.showToast({
        title: `每局游戏最多撤销${SCORE_CONFIG.VIP_UNDO_LIMIT}次`,
        icon: 'none'
      });
      return;
    }

    // 恢复上一步状态
    this.setData({
      tiles: this.previousState.tiles,
      score: this.previousState.score,
      baseScore: this.previousState.baseScore || this.previousState.score,
      moves: this.previousState.moves,
      maxTile: this.previousState.maxTile,
      comboCount: this.previousState.comboCount || 0,
      comboMultiplier: this.previousState.comboMultiplier || 1,
      undoCount: this.data.undoCount + 1,
      canUndo: false
    });

    // 播放移动音效
    this.playSound(SOUND_EFFECTS.MOVE);

    // 清除上一步状态
    this.previousState = null;
  },

  /**
   * 移动方块
   * @param {string} direction 移动方向
   */
  move: function(direction) {
    // 复制当前方块数组
    const oldTiles = JSON.parse(JSON.stringify(this.data.tiles));

    // 根据方向获取移动向量
    let vector = { x: 0, y: 0 };

    switch (direction) {
      case 'up':
        vector = { x: 0, y: -1 };
        break;
      case 'right':
        vector = { x: 1, y: 0 };
        break;
      case 'down':
        vector = { x: 0, y: 1 };
        break;
      case 'left':
        vector = { x: -1, y: 0 };
        break;
    }

    // 获取移动顺序
    const traversals = this.buildTraversals(vector);

    // 标记是否有方块移动
    let moved = false;
    // 标记是否有方块合并
    let merged = false;
    // 记录合并的方块数量
    let mergeCount = 0;
    // 记录本次移动获得的分数
    let moveScore = 0;

    // 清除所有方块的合并标记
    const tiles = this.data.tiles.map(tile => ({
      ...tile,
      isNew: false,
      isMerged: false
    }));

    // 更新方块状态
    this.setData({ tiles });

    // 按顺序移动方块
    traversals.y.forEach(y => {
      traversals.x.forEach(x => {
        // 查找当前位置的方块
        const tile = this.getTileAt(x, y);

        if (tile) {
          // 计算方块的目标位置
          const positions = this.findFarthestPosition({ x, y }, vector);
          const next = this.getTileAt(positions.next.x, positions.next.y);

          // 如果有下一个方块，并且可以合并
          if (next && next.value === tile.value && !next.isMerged) {
            // 合并方块
            const mergedTile = {
              ...next,
              value: tile.value * 2,
              isMerged: true
            };

            // 更新方块数组
            const newTiles = this.data.tiles.filter(t =>
              t.id !== tile.id && t.id !== next.id
            );

            newTiles.push(mergedTile);

            // 更新分数
            const tileScore = mergedTile.value;
            moveScore += tileScore;
            const newScore = this.data.score + tileScore;
            const newBaseScore = this.data.baseScore + tileScore;

            // 更新最大方块值
            const newMaxTile = Math.max(this.data.maxTile, mergedTile.value);

            this.setData({
              tiles: newTiles,
              score: newScore,
              baseScore: newBaseScore,
              maxTile: newMaxTile
            });

            // 检查是否达到2048
            if (mergedTile.value === 2048 && !this.data.gameWon) {
              this.setData({ gameWon: true });
              // 播放胜利音效
              this.playSound(SOUND_EFFECTS.WIN);
              // 不立即结束游戏，允许继续玩
            }

            moved = true;
            merged = true;
            mergeCount++;

            // 播放合并音效
            this.playSound(SOUND_EFFECTS.MERGE);
          } else {
            // 移动方块到最远位置
            if (positions.farthest.x !== tile.x || positions.farthest.y !== tile.y) {
              // 更新方块位置
              const movedTile = {
                ...tile,
                x: positions.farthest.x,
                y: positions.farthest.y
              };

              // 更新方块数组
              const newTiles = this.data.tiles.filter(t => t.id !== tile.id);
              newTiles.push(movedTile);

              this.setData({ tiles: newTiles });

              moved = true;
            }
          }
        }
      });
    });

    // 如果有方块移动，添加新方块
    if (moved) {
      // 增加移动次数
      const newMoves = this.data.moves + 1;

      // 更新连击数据
      let comboCount = this.data.comboCount;
      let comboMultiplier = this.data.comboMultiplier;

      if (merged) {
        // 如果有合并，增加连击次数
        comboCount += mergeCount;
        // 根据连击次数更新连击系数
        const comboIndex = Math.min(Math.floor(comboCount / 2), SCORE_CONFIG.COMBO_MULTIPLIERS.length - 1);
        comboMultiplier = SCORE_CONFIG.COMBO_MULTIPLIERS[comboIndex];
      } else {
        // 如果没有合并，重置连击
        comboCount = 0;
        comboMultiplier = 1;
      }

      // 计算最终得分
      const finalScore = Math.floor(this.data.baseScore * comboMultiplier);

      this.setData({
        moves: newMoves,
        comboCount: comboCount,
        comboMultiplier: comboMultiplier,
        timeBonus: 0,
        finalScore: finalScore
      });

      // 添加新方块
      this.addRandomTile();

      // 保存最高分
      this.saveBestScore();

      // 播放移动音效
      this.playSound(SOUND_EFFECTS.MOVE);

      // 检查游戏是否结束
      if (!this.movesAvailable()) {
        this.setData({ gameOver: true });
        this.stopTimer();
        // 播放失败音效
        this.playSound(SOUND_EFFECTS.LOSE);
      }
    }
  },

  /**
   * 构建遍历顺序
   * @param {Object} vector 移动向量
   * @returns {Object} 遍历顺序
   */
  buildTraversals: function(vector) {
    const traversals = {
      x: [],
      y: []
    };

    // 创建基本遍历顺序
    for (let i = 0; i < GRID_SIZE; i++) {
      traversals.x.push(i);
      traversals.y.push(i);
    }

    // 如果向右移动，从右向左遍历
    if (vector.x === 1) {
      traversals.x = traversals.x.reverse();
    }

    // 如果向下移动，从下向上遍历
    if (vector.y === 1) {
      traversals.y = traversals.y.reverse();
    }

    return traversals;
  },

  /**
   * 获取指定位置的方块
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @returns {Object|null} 方块对象或null
   */
  getTileAt: function(x, y) {
    return this.data.tiles.find(tile => tile.x === x && tile.y === y) || null;
  },

  /**
   * 查找最远位置
   * @param {Object} position 起始位置
   * @param {Object} vector 移动向量
   * @returns {Object} 最远位置和下一个位置
   */
  findFarthestPosition: function(position, vector) {
    let previous;
    let cell = { x: position.x, y: position.y };

    // 不断向前移动，直到碰到边界或其他方块
    do {
      previous = cell;
      cell = {
        x: previous.x + vector.x,
        y: previous.y + vector.y
      };
    } while (this.withinBounds(cell) && !this.getTileAt(cell.x, cell.y));

    return {
      farthest: previous,
      next: cell
    };
  },

  /**
   * 检查位置是否在边界内
   * @param {Object} position 位置
   * @returns {boolean} 是否在边界内
   */
  withinBounds: function(position) {
    return position.x >= 0 && position.x < GRID_SIZE &&
           position.y >= 0 && position.y < GRID_SIZE;
  },

  /**
   * 检查是否有可用移动
   * @returns {boolean} 是否有可用移动
   */
  movesAvailable: function() {
    // 如果还有空格子，可以移动
    if (this.getEmptyCells().length > 0) return true;

    // 检查是否有相邻的相同方块
    for (let y = 0; y < GRID_SIZE; y++) {
      for (let x = 0; x < GRID_SIZE; x++) {
        const tile = this.getTileAt(x, y);

        if (tile) {
          // 检查右侧方块
          const right = this.getTileAt(x + 1, y);
          if (right && right.value === tile.value) return true;

          // 检查下方方块
          const down = this.getTileAt(x, y + 1);
          if (down && down.value === tile.value) return true;
        }
      }
    }

    // 没有可用移动
    return false;
  },

  /**
   * 重新开始游戏
   */
  restartGame: function() {
    // 检查游戏次数限制（非VIP用户）
    if (!this.checkGameLimit()) {
      return;
    }

    this.startNewGame();
  },

  /**
   * 前往排行榜
   */
  goToRanking: function() {
    wx.navigateTo({
      url: '/pages/games/game-2048-ranking/game-2048-ranking'
    });
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  },

  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    this.setData({ showVipDialog: true });
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({ showVipDialog: false });
  },

  /**
   * 购买VIP事件
   */
  async onBuyVip() {
    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 获取当前VIP信息
      const currentVipInfo = wx.getStorageSync('vipInfo');
      let expireAt = new Date();

      // 如果已经是VIP，基于原过期时间增加31天
      if (currentVipInfo && currentVipInfo.is_valid_vip && currentVipInfo.vip_expire_at) {
        // 使用原过期时间作为基准
        expireAt = new Date(currentVipInfo.vip_expire_at);
      }

      // 增加31天
      expireAt.setDate(expireAt.getDate() + 31);

      // 调用设置VIP状态接口
      const vipInfo = await app.setVipStatus(true, expireAt.toISOString());

      // 更新页面状态
      this.setData({
        isVip: vipInfo.is_valid_vip || false,
        vipRemainingDays: vipInfo.remainingDays || 0,
        showVipDialog: false
      });

      // 根据是续费还是首次开通显示不同提示
      wx.showToast({
        title: currentVipInfo?.is_valid_vip ? 'VIP续费成功' : 'VIP开通成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('购买VIP失败:', error);
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 上传分数
   */
  uploadScore: function() {
    // 检查是否已登录
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再上传记录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            this.bindLogin();
          }
        }
      });
      return;
    }

    // 显示昵称输入弹窗
    this.setData({
      showUserInfoModal: true,
      nickName: this.data.userInfo.nickName || ''
    });
  },

  /**
   * 绑定登录
   */
  bindLogin: async function() {
    try {
      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 调用登录接口
      const userInfo = await app.getUserProfile();

      if (userInfo) {
        this.setData({
          userInfo: userInfo,
          isLoggedIn: true,
          nickName: userInfo.nickName || ''
        });

        // 显示昵称输入弹窗
        this.setData({
          showUserInfoModal: true
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 昵称输入事件
   */
  onNickNameInput: function(e) {
    this.setData({
      nickName: e.detail.value
    });
  },

  /**
   * 关闭用户信息弹窗
   */
  closeUserInfoModal: function() {
    this.setData({
      showUserInfoModal: false
    });
  },

  /**
   * 确认上传
   */
  confirmUpload: function() {
    // 检查昵称是否为空
    if (!this.data.nickName.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    // 关闭弹窗
    this.setData({
      showUserInfoModal: false
    });

    // 上传记录
    this.uploadRecordToServer();
  },

  /**
   * 上传记录到服务器
   */
  uploadRecordToServer: function() {
    wx.showLoading({
      title: '上传记录中...',
      mask: true
    });

    // 计算最终得分
    const finalScore = Math.floor(this.data.baseScore * this.data.comboMultiplier);

    // 准备上传数据
    const data = {
      openid: this.data.userInfo.openid || wx.getStorageSync('openid'),
      nickname: this.data.nickName.trim(),
      score: finalScore, // 使用最终得分
      baseScore: this.data.baseScore,
      comboMultiplier: this.data.comboMultiplier,
      timeBonus: this.data.timeBonus,
      maxTile: this.data.maxTile,
      gameTime: this.data.gameTime,
      moves: this.data.moves,
      timestamp: Date.now()
    };

    console.log('上传记录数据:', data);

    // 发送请求
    wx.request({
      url: `${API_BASE_URL}/games/2048/records`,
      method: 'POST',
      data: data,
      success: (res) => {
        wx.hideLoading();

        if (res.statusCode === 200 && res.data.code === 0) {
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });

          this.setData({
            hasUploaded: true
          });

          // 提示用户查看排行榜
          setTimeout(() => {
            wx.showModal({
              title: '上传成功',
              content: '是否前往查看排行榜？',
              confirmText: '去看看',
              cancelText: '稍后再说',
              success: (res) => {
                if (res.confirm) {
                  this.goToRanking();
                }
              }
            });
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.message || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('上传记录失败:', err);

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 阻止滑动穿透
   */
  preventTouchMove: function() {
    return false;
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function(e) {
    if (e) {
      e.stopPropagation();
    }
    return false;
  }
});
