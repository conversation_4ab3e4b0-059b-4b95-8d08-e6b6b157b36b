// 基础敏感词库
const sensitiveWords = [
  // 政治相关
  '政治', '反动', '颠覆', '分裂', '暴乱',
  // 暴力相关  
  '暴力', '血腥', '自杀', '伤害',
  // 色情相关
  '色情', '淫秽', '露骨',
  // 歧视相关
  '歧视', '辱骂', '贬低',
  // 违法相关
  '赌博', '毒品', '诈骗',
  // 其他
  '外挂', '作弊', '脚本'
];

// DFA算法构建前缀树
class SensitiveWordChecker {
  constructor(words) {
    this.root = {};
    this.buildTree(words);
  }

  // 构建前缀树
  buildTree(words) {
    for (const word of words) {
      let node = this.root;
      for (const char of word) {
        if (!node[char]) {
          node[char] = {};
        }
        node = node[char];
      }
      node.isEnd = true;
    }
  }

  // 检查文本是否包含敏感词
  check(text) {
    const result = {
      containsSensitiveWords: false,
      words: new Set()
    };

    for (let i = 0; i < text.length; i++) {
      let node = this.root;
      let word = '';
      
      for (let j = i; j < text.length; j++) {
        const char = text[j];
        if (!node[char]) break;
        
        word += char;
        node = node[char];
        
        if (node.isEnd) {
          result.containsSensitiveWords = true;
          result.words.add(word);
          break;
        }
      }
    }

    return result;
  }

  // 替换敏感词为 *
  filter(text) {
    let result = text;
    const check = this.check(text);
    
    if (check.containsSensitiveWords) {
      check.words.forEach(word => {
        const stars = '*'.repeat(word.length);
        result = result.replace(new RegExp(word, 'g'), stars);
      });
    }
    
    return result;
  }
}

// 导出敏感词检查器实例
const checker = new SensitiveWordChecker(sensitiveWords);

module.exports = {
  checker
}; 