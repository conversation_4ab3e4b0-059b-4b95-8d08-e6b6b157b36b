/* components/ad-button/ad-button.wxss */
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: transparent;
  z-index: 9998;
  pointer-events: none;
  touch-action: none;
}

.mask-layer.active {
  pointer-events: auto;
  background-color: rgba(0, 0, 0, 0.01);
}

.ad-button-container {
  position: fixed;
  z-index: 9999;
  width: 60px;
  height: 60px;
  touch-action: none;
  opacity: 0.75;
  transition: opacity 0.3s, transform 0.3s;
}

/* 拖动状态 */
.ad-button-container.dragging {
  opacity: 0.85;
  transform: scale(1.05);
}

/* 悬停或触摸时恢复完全不透明 */
.ad-button-container:active {
  opacity: 0.95;
}

.ad-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, rgba(255, 150, 166, 0.95), rgba(255, 90, 122, 0.95));
  border-radius: 50%;
  box-shadow: 0 3px 10px rgba(255, 90, 122, 0.3);
  color: white;
  font-size: 12px;
  transition: all 0.3s;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.ad-button:active {
  transform: scale(0.92);
  box-shadow: 0 1px 5px rgba(255, 90, 122, 0.2);
}

.ad-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
}

.ad-text {
  font-size: 12px;
  color: white;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
} 