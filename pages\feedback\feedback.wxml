<view class="container">
  <image class="bg-image" src="/images/bg.jpg" mode="aspectFill"></image>
  
  <view class="feedback-container">
    <view class="feedback-type">
      <text class="section-title">反馈类型</text>
      <radio-group class="type-group" bindchange="handleTypeChange">
        <label class="type-item" wx:for="{{feedbackTypes}}" wx:key="value">
          <radio value="{{item.value}}" checked="{{item.checked}}"/>
          <text>{{item.label}}</text>
        </label>
      </radio-group>
    </view>

    <view class="feedback-content">
      <text class="section-title">反馈内容</text>
      <text class="section-tip">这是匿名反馈，请详细描述您遇到的问题或建议\n</text>
      <text class="section-tip">欢迎加入"飞车图鉴"交流群，群号<text class="group-number" bindtap="copyGroupNumber">2156036977</text></text>
      <textarea 
        class="content-textarea" 
        placeholder="请描述您的问题或建议，我们会认真查看每一条反馈..." 
        bindinput="handleContentInput"
        value="{{content}}"
      ></textarea>
    </view>

    <view class="scene-info" wx:if="{{type === 'bug'}}">
      <text class="section-title">问题场景</text>
      <textarea 
        class="scene-textarea" 
        value="{{deviceInfo}}"
        bindinput="handleDeviceInfoInput"
        placeholder="请描述出现问题的具体场景，如：在查看某车辆详情时无法显示图片"
      ></textarea>
    </view>

    <button class="check-feedback-btn" bindtap="navigateToFeedbackList">查看反馈进度</button>
    <button class="submit-btn" type="primary" bindtap="handleSubmit">提交反馈</button>
  </view>

  <!-- 广告按钮 -->
  <!--<ad-button left="20" top="300"></ad-button>-->
</view>