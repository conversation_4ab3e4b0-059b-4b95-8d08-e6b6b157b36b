Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 按钮的位置
    left: {
      type: Number,
      value: 20 // 默认距离左侧20px
    },
    top: {
      type: Number,
      value: 300 // 默认距离顶部300px
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    buttonLeft: 20,
    buttonTop: 300,
    startX: 0,
    startY: 0,
    moveX: 0,
    moveY: 0,
    videoAdLoaded: false,
    videoAdFailed: false,
    isTouching: false, // 是否正在触摸状态
    pageScrollLocked: false // 页面滚动是否锁定
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 尝试从本地存储读取按钮位置
      try {
        const position = wx.getStorageSync('adButtonPosition');
        if (position) {
          this.properties.left = position.left;
          this.properties.top = position.top;
        }
      } catch (e) {
        console.error('读取按钮位置失败', e);
      }
      
      // 初始化按钮位置
      this.setData({
        buttonLeft: this.properties.left,
        buttonTop: this.properties.top
      });
      
      // 初始化激励视频广告
      this.initVideoAd();
    },
    
    // 组件销毁时清理
    detached() {
      // 确保解锁页面滚动
      if (this.data.pageScrollLocked) {
        this.unlockPageScroll();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 锁定页面滚动
     */
    lockPageScroll() {
      if (!this.data.pageScrollLocked) {
        this.setData({
          pageScrollLocked: true
        });
        // 通知页面锁定滚动
        this.triggerEvent('scrollLock', { locked: true });
      }
    },

    /**
     * 解锁页面滚动
     */
    unlockPageScroll() {
      if (this.data.pageScrollLocked) {
        this.setData({
          pageScrollLocked: false
        });
        // 通知页面解锁滚动
        this.triggerEvent('scrollLock', { locked: false });
      }
    },

    /**
     * 通用事件阻止函数，用于遮罩层
     */
    preventTouchEvent(e) {
      // 彻底阻止事件传播和默认行为
      if (e && e.preventDefault) {
        e.preventDefault();
      }
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      return false;
    },

    /**
     * 初始化激励视频广告
     */
    initVideoAd() {
      console.log('正在初始化激励视频广告组件...');
      // 检查是否支持创建激励视频广告
      if (wx.createRewardedVideoAd) {
        try {
          console.log('创建激励视频广告，广告单元ID: adunit-919b8b04a2997a01');
          this.videoAd = wx.createRewardedVideoAd({
            adUnitId: 'adunit-919b8b04a2997a01'
          });
          
          console.log('激励视频广告对象创建成功:', this.videoAd ? '成功' : '失败');
          
          // 监听广告加载事件
          this.videoAd.onLoad(() => {
            this.setData({
              videoAdLoaded: true,
              videoAdFailed: false
            });
            console.log('激励视频广告加载成功');
          });
          
          // 监听广告错误事件
          this.videoAd.onError((err) => {
            this.setData({
              videoAdLoaded: false,
              videoAdFailed: true
            });
            console.error('激励视频广告加载失败', err);
          });
          
          // 监听广告关闭事件
          this.videoAd.onClose((res) => {
            console.log('广告关闭事件触发，完成状态:', res ? (res.isEnded ? '完整观看' : '未完整观看') : '未知');
            // 用户点击了【关闭广告】按钮
            if (res && res.isEnded) {
              // 保存权益信息到本地存储
              const now = new Date();
              const expireTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0); // 第二天0点过期
              wx.setStorageSync('priorityLoadingPrivilege', {
                hasPrivilege: true,
                expireTime: expireTime.getTime()
              });
              
              // 正常播放结束，使用模态框展示奖励信息
              wx.showModal({
                title: '感谢支持',
                content: '感谢您的热心支持！您今日将获得高峰期优先加载权益！\n\n权益有效期至今日24:00',
                confirmText: '我知道了',
                showCancel: false,
                success: (res) => {
                  if (res.confirm) {
                    console.log('用户点击确定');
                  }
                }
              });
            } else {
              // 播放中途退出，不下发游戏奖励
              wx.showModal({
                title: '未完整观看',
                content: '需要完整观看广告才能获得权益哦，下次再继续支持吧~',
                confirmText: '我知道了',
                showCancel: false
              });
            }
          });
        } catch (err) {
          console.error('创建激励视频广告失败:', err);
          this.setData({
            videoAdLoaded: false,
            videoAdFailed: true
          });
        }
      } else {
        console.error('当前环境不支持激励视频广告');
        this.setData({
          videoAdLoaded: false,
          videoAdFailed: true
        });
      }
    },

    /**
     * 显示激励视频广告
     */
    showAd() {
      console.log('准备显示广告，广告组件状态:', this.videoAd ? '已初始化' : '未初始化');
      if (this.videoAd) {
        try {
          console.log('尝试显示广告...');
          this.videoAd.show()
            .then(() => {
              console.log('广告显示成功');
            })
            .catch(err => {
              console.error('广告显示失败，尝试重新加载', err);
              // 失败重试
              return this.videoAd.load()
                .then(() => {
                  console.log('广告重新加载成功，再次尝试显示');
                  return this.videoAd.show();
                })
                .then(() => {
                  console.log('重试后广告显示成功');
                })
                .catch(loadErr => {
                  console.error('广告重新加载/显示失败', loadErr);
                  wx.showToast({
                    title: '广告加载失败，请稍后再试',
                    icon: 'none',
                    duration: 2000
                  });
                });
            });
        } catch (err) {
          console.error('显示广告时发生异常:', err);
          wx.showToast({
            title: '广告显示异常，请稍后再试',
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        console.log('广告组件未初始化，尝试重新初始化...');
        this.initVideoAd();
        setTimeout(() => {
          if (this.videoAd) {
            this.showAd();
          } else {
            wx.showToast({
              title: '广告组件初始化失败',
              icon: 'none',
              duration: 2000
            });
          }
        }, 1000);
      }
    },

    /**
     * 点击广告按钮，显示确认弹窗
     */
    onAdButtonTap() {
      // 如果是拖动结束，则不触发点击事件
      if (this.data.isTouching) {
        return;
      }
      
      console.log('广告按钮被点击');
      
      wx.showModal({
        title: '助力开发者',
        content: '观看30秒广告，让我们能够持续提供高质量图鉴服务~',
        confirmText: '乐意支持',
        cancelText: '下次一定',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确认，准备展示广告');
            this.showAd();
          }
        }
      });
    },

    /**
     * 按钮触摸开始事件
     */
    buttonTouchStart(e) {
      // 阻止事件传播
      this.preventTouchEvent(e);
      
      // 激活遮罩层并锁定页面滚动
      this.lockPageScroll();
      
      this.setData({
        startX: e.touches[0].clientX,
        startY: e.touches[0].clientY,
        moveX: 0,
        moveY: 0,
        isTouching: true // 开始触摸，激活遮罩
      });
      
      // 增加拖动时的透明度样式
      // 注意：样式变化已在CSS中通过opacity定义
      
      // 临时禁用页面滚动 (额外措施)
      try {
        wx.disableScrolling && wx.disableScrolling({
          disable: true
        });
      } catch (err) {
        console.log('禁用滚动API不存在', err);
      }
    },

    /**
     * 按钮触摸移动事件
     */
    buttonTouchMove(e) {
      // 阻止事件传播
      this.preventTouchEvent(e);
      
      // 确保遮罩层激活
      if (!this.data.isTouching) {
        this.setData({
          isTouching: true
        });
      }
      
      const moveX = e.touches[0].clientX - this.data.startX;
      const moveY = e.touches[0].clientY - this.data.startY;
      
      // 获取屏幕宽高 (使用新的API)
      const windowInfo = wx.getWindowInfo();
      const screenWidth = windowInfo.windowWidth;
      const screenHeight = windowInfo.windowHeight;
      
      // 计算新位置
      let newLeft = this.properties.left + moveX;
      let newTop = this.properties.top + moveY;
      
      // 边界检查，确保按钮不会超出屏幕
      // 左边界
      if (newLeft < 0) {
        newLeft = 0;
      }
      // 右边界 (按钮宽度为60px)
      if (newLeft > screenWidth - 60) {
        newLeft = screenWidth - 60;
      }
      // 上边界
      if (newTop < 0) {
        newTop = 0;
      }
      // 下边界 (按钮高度为60px)
      if (newTop > screenHeight - 60) {
        newTop = screenHeight - 60;
      }
      
      this.setData({
        moveX,
        moveY,
        buttonLeft: newLeft,
        buttonTop: newTop
      });
    },

    /**
     * 按钮触摸结束事件
     */
    buttonTouchEnd(e) {
      // 阻止事件传播
      this.preventTouchEvent(e);
      
      // 解锁页面滚动
      this.unlockPageScroll();
      
      // 计算移动距离
      const moveDistance = Math.sqrt(Math.pow(this.data.moveX, 2) + Math.pow(this.data.moveY, 2));
      console.log('移动距离:', moveDistance);
      
      // 存储是否为点击的标志
      const isClick = moveDistance < 5; // 如果移动距离小于5像素，则认为是点击
      
      // 更新按钮最终位置
      this.setData({
        startX: 0,
        startY: 0,
        moveX: 0,
        moveY: 0,
        isTouching: false // 结束触摸，关闭遮罩
      });
      
      // 更新属性，以便下次拖动基于新位置计算
      this.properties.left = this.data.buttonLeft;
      this.properties.top = this.data.buttonTop;
      
      // 保存位置到本地，使得重新打开小程序时位置不变
      try {
        wx.setStorageSync('adButtonPosition', {
          left: this.data.buttonLeft,
          top: this.data.buttonTop
        });
      } catch (e) {
        console.error('保存按钮位置失败', e);
      }
      
      // 重新启用页面滚动 (额外措施)
      try {
        wx.disableScrolling && wx.disableScrolling({
          disable: false
        });
      } catch (err) {
        console.log('启用滚动API不存在', err);
      }
      
      // 如果是点击行为，则触发点击事件
      if (isClick) {
        console.log('检测到点击行为，触发onAdButtonTap');
        setTimeout(() => {
          this.onAdButtonTap();
        }, 50); // 添加短暂延时，确保状态更新完成
      }
    }
  }
}) 