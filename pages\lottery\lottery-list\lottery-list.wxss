/* 添加响应式配置，适配不同屏幕尺寸 */
page {
  --content-width: 94%;
  --grid-columns-large: 5;
  --grid-columns-medium: 4;
  --grid-columns-small: 3;
  --item-gap-large: 10rpx;
  --item-gap-small: 6rpx;
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --content-width: 96%;
    --grid-columns-large: 4;
    --grid-columns-medium: 3;
    --grid-columns-small: 2;
    --item-gap-large: 6rpx;
    --item-gap-small: 4rpx;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --content-width: 92%;
    --grid-columns-large: 5;
    --grid-columns-medium: 4;
    --grid-columns-small: 3;
    --item-gap-large: 12rpx;
    --item-gap-small: 8rpx;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

/* 抽奖活动列表页面样式 */
.container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: var(--page-padding);
  padding-bottom: calc(48px + env(safe-area-inset-bottom) + 30rpx); /* 为自定义tabBar预留空间 */
  box-sizing: border-box;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加一个半透明的遮罩层 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: -1;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
  width: var(--content-width);
  max-width: 700rpx;
}

.title {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10rpx;
  transform: scale(1.5);
  margin-top: 10rpx;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: var(--font-size-small);
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 活动列表样式 */
.activity-list {
  width: var(--content-width);
  max-width: 700rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
  padding: 10rpx 0;
}

/* 活动列表项 */
.activity-item {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 0;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transition: all 0.15s ease;
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
}

/* 活动列表项最后一个元素样式 */
.activity-item:last-child {
  margin-bottom: 0;
}

.activity-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.85);
}

/* 加载状态样式 */
.activity-item.loading {
  opacity: 0.8;
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid rgba(74, 144, 226, 0.3);
  border-top: 3rpx solid rgba(74, 144, 226, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 添加漂亮的渐变边框效果 */
.activity-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #4a90e2, #9c27b0);
  border-radius: 3rpx 3rpx 0 0;
}

.activity-header {
  display: flex;
  align-items: center;
  width: 65%;
  padding-right: 10rpx;
}

.activity-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 16rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.activity-icon image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

/* 调整布局使描述和标签间距更美观 */
.activity-desc {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.3;
  width: 65%;
  margin: 4rpx 0 8rpx;
  padding-left: 80rpx;
}

.activity-items {
  width: 65%;
  display: flex;
  padding-left: 80rpx;
  flex-wrap: wrap;
  align-items: center;
}

.item-label {
  font-size: 20rpx;
  color: #999999;
  margin-right: 6rpx;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  flex: 1;
}

.item-tag {
  display: inline-block;
  font-size: 18rpx;
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.go-btn {
  width: 140rpx;
  text-align: center;
  background: linear-gradient(135deg, #4a90e2, #3670b2);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.go-btn:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.footer {
  margin-top: 40rpx;
  padding: 20rpx 0;
  text-align: center;
  width: var(--content-width);
  max-width: 700rpx;
}

.footer-text {
  font-size: var(--font-size-small);
  color: #ffffff;
  opacity: 0.7;
  margin-bottom: 20rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.back-button {
  display: inline-block;
  background: linear-gradient(135deg, #4a90e2, #3670b2);
  color: #ffffff;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  font-size: var(--font-size-normal);
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.15);
}

.ad-container {
  margin-top: 20rpx;
}

/* 免责声明卡片 */
.disclaimer-card {
  width: var(--content-width);
  max-width: 700rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 auto 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.disclaimer {
  font-size: var(--font-size-small);
  color: #666666;
  text-align: center;
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding: 30rpx 40rpx;
    padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  }

  .activity-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 30rpx;
    width: 90%;
    max-width: 1200rpx;
    justify-content: center;
  }

  .activity-item {
    width: 100%;
  }
}

.vip-badge-container {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 10;
}

/* 简化版VIP徽章样式 */
.simple-vip-badge {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 30rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.simple-vip-badge.vip {
  background: linear-gradient(135deg, #7b5314, #ffd700);
  border: 1rpx solid rgba(255, 215, 0, 0.5);
}

.simple-vip-badge image {
  width: 34rpx;
  height: 34rpx;
  margin-right: 8rpx;
}

.simple-vip-badge text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}