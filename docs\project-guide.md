# QQ飞车图鉴小程序开发指南

## 1. 项目概述

QQ飞车图鉴小程序是一个提供QQ飞车游戏相关信息和工具的微信小程序，包括赛车信息查询、宠物图鉴、抽奖模拟器等功能。这份指南旨在帮助开发者理解项目结构并遵循统一的设计规范。

## 2. 项目结构

```
/
├── components/         # 全局组件
│   ├── ad-button/      # 广告按钮组件
│   ├── custom-tabbar/  # 自定义底部导航栏组件
│   ├── vip-badge/      # VIP徽章组件
│   ├── vip-dialog/     # VIP对话框组件
│   └── feedback-button/ # 反馈按钮组件
├── pages/              # 页面
│   ├── index/          # 主页(赛车页面)
│   ├── pet/            # 宠物页面
│   ├── toolbox/        # 工具箱页面
│   ├── feedback/       # 用户反馈页面
│   ├── feedback-list/  # 反馈列表页面
│   └── lottery/        # 抽奖模拟器相关页面
│       ├── lottery-list/   # 抽奖列表页面
│       ├── luckytree/      # 幸运摇钱树抽奖页面
│       ├── treasure-hunting/ # 赛车夺宝抽奖页面
│       └── supertreasure/  # 至尊夺宝抽奖页面
├── images/             # 全局图片资源
├── utils/              # 工具类
│   ├── api.js          # API接口封装
│   └── page-with-vip.js # VIP页面增强器
├── app.js              # 应用入口
├── app.json            # 应用配置
└── app.wxss            # 全局样式
```

## 3. 设计规范

### 3.1 颜色系统

- **主色调**：蓝色 `#4a90e2`
- **辅助色**：
  - 紫色 `#9c27b0`（用于突出显示）
  - 金色 `#f9d776`（用于稀有物品）
- **灰度**：
  - 背景：`#f5f7fa`
  - 文本：`#333333`（主文本），`#666666`（次要文本），`#999999`（提示文本）
- **状态色**：
  - 成功：`#4CAF50`
  - 警告：`#FF9800`
  - 错误：`#F44336`

### 3.2 字体规范

小程序默认字体为系统字体：
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
```

字体大小使用响应式配置：
```css
--font-size-title: 32rpx;
--font-size-normal: 26rpx;
--font-size-small: 22rpx;
--font-size-mini: 20rpx;
```

### 3.3 间距与圆角

- **间距**：
  - 卡片内边距：16rpx - 24rpx
  - 元素间距：20rpx - 30rpx
- **圆角**：
  - 大圆角：16rpx（卡片）
  - 中圆角：12rpx（按钮）
  - 小圆角：8rpx（标签）

### 3.4 阴影效果

```css
/* 浅阴影 */
box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

/* 中阴影 */
box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);

/* 深阴影 */
box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
```

## 4. 全局资源

### 4.1 背景图片

全局默认背景图片路径：`/images/bg.jpg`

在页面中使用背景图片的标准方式：
```wxml
<image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>
```

```js
data: {
  backgroundImage: '/images/bg.jpg'
}
```

### 4.2 图片资源

- 抽奖模拟器相关图片资源已迁移到云存储，使用云文件ID路径：
  `cloud://cloud1-1gpha037e961200e.636c-cloud1-1gpha037e961200e-1334033817/[目录名]/[文件名]`

- 根据不同功能模块，图片存放在相应的云存储目录中：
  - `/luckytree/` - 幸运摇钱树相关图片
  - `/treasure-hunting/` - 赛车夺宝相关图片
  - `/supertreasure/` - 至尊夺宝相关图片

- 图标命名规则：
  - 功能图标：`[功能]-icon.png`
  - 选中状态：`[功能]-icon-active.png`

## 5. 组件系统

### 5.1 全局组件

- **VIP徽章 (vip-badge)**：显示VIP状态
  ```wxml
  <vip-badge id="vipBadge" isVip="{{isVip}}" remainingDays="{{vipRemainingDays}}" bind:tap="handleVipBadgeClick" />
  ```

  VIP徽章位置样式：
  ```css
  /* VIP徽章容器样式 */
  .vip-badge-container {
    position: fixed;
    top: 20rpx;
    right: 20rpx;
    z-index: 100;
  }
  ```

  **重要说明**：
  1. 使用 `position: fixed` 确保VIP徽章始终固定在右上角
  2. 设置足够高的 `z-index` 确保徽章显示在其他元素之上
  3. 所有页面应保持VIP徽章位置一致，便于用户识别
  4. 在游戏页面（如2048）中，徽章同样应固定在右上角，不随页面滚动

- **VIP对话框 (vip-dialog)**：VIP购买/续费对话框
  ```wxml
  <vip-dialog
    show="{{showVipDialog}}"
    pageKey="{{pageKey}}"
    isVip="{{isVip}}"
    vipRemainingDays="{{vipRemainingDays}}"
    bind:close="onVipDialogClose"
    bind:buy="onBuyVip">
  </vip-dialog>
  ```

- **底部导航栏 (custom-tabbar)**：自定义底部导航
  ```wxml
  <custom-tabbar selected="{{当前页索引}}"></custom-tabbar>
  ```

- **广告按钮 (ad-button)**：广告组件
  ```wxml
  <ad-button text="支持我们" />
  ```

- **反馈按钮 (feedback-button)**：可拖动的悬浮反馈按钮
  ```wxml
  <feedback-button bind:tap="onFeedbackTap" />
  ```

  **反馈按钮拖动实现**：
  ```wxml
  <!-- 反馈按钮模板 feedback-button.wxml -->
  <view class="feedback-button-container">
    <!-- 透明遮罩层，仅在拖动时显示，防止背景元素响应触摸 -->
    <view class="drag-mask {{isDragging ? 'show' : ''}}" catchtouchmove="preventBubble"></view>

    <!-- 可拖动按钮 -->
    <view class="feedback-button {{isDragging ? 'dragging' : ''}}"
      catchtouchstart="touchStart"
      catchtouchmove="touchMove"
      catchtouchend="touchEnd"
      style="top: {{buttonTop}}px; left: {{buttonLeft}}px;">
      <image src="/images/feedback.png" />
      <text wx:if="{{showText}}">反馈</text>
    </view>
  </view>
  ```

  ```css
  /* 反馈按钮样式 feedback-button.wxss */
  .feedback-button-container {
    position: relative;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  /* 拖动时的全屏遮罩，防止背景元素响应触摸 */
  .drag-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 900;
    background-color: transparent; /* 完全透明 */
    pointer-events: auto;
    visibility: hidden;
  }

  .drag-mask.show {
    visibility: visible;
  }

  /* 反馈按钮样式 */
  .feedback-button {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #4a90e2, #3670b2);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
    z-index: 999;
    pointer-events: auto;
    transition: box-shadow 0.3s ease;
  }

  .feedback-button.dragging {
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  }

  .feedback-button image {
    width: 44rpx;
    height: 44rpx;
  }
  ```

  ```javascript
  // 反馈按钮逻辑 feedback-button.js
  Component({
    data: {
      buttonTop: wx.getSystemInfoSync().windowHeight - 160, // 默认位置
      buttonLeft: wx.getSystemInfoSync().windowWidth - 80,
      isDragging: false,
      startX: 0,
      startY: 0,
      showText: false
    },

    methods: {
      // 触摸开始时记录起始位置
      touchStart(e) {
        this.setData({
          isDragging: true,
          startX: e.touches[0].clientX,
          startY: e.touches[0].clientY
        });
      },

      // 拖动时更新按钮位置
      touchMove(e) {
        const { startX, startY, buttonLeft, buttonTop } = this.data;
        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;

        const moveX = touchX - startX;
        const moveY = touchY - startY;

        // 计算新位置，并限制在屏幕范围内
        const windowWidth = wx.getSystemInfoSync().windowWidth;
        const windowHeight = wx.getSystemInfoSync().windowHeight;
        const buttonWidth = 50; // 按钮宽度的一半

        let newLeft = buttonLeft + moveX;
        let newTop = buttonTop + moveY;

        newLeft = Math.max(buttonWidth, Math.min(windowWidth - buttonWidth, newLeft));
        newTop = Math.max(buttonWidth, Math.min(windowHeight - buttonWidth, newTop));

        this.setData({
          buttonLeft: newLeft,
          buttonTop: newTop,
          startX: touchX,
          startY: touchY
        });
      },

      // 触摸结束时，结束拖动状态
      touchEnd() {
        this.setData({ isDragging: false });
      },

      // 阻止冒泡，确保背景元素不会响应
      preventBubble() {
        return false;
      }
    }
  });
  ```

  **使用说明**：
  1. 按钮会自动贴合屏幕边缘，避免干扰内容
  2. 拖动时会显示透明遮罩层，防止背景元素响应触摸
  3. 可以通过设置默认位置调整按钮初始位置

  **优化版拖动实现**：
  ```wxml
  <!-- 反馈按钮WXML实现 -->
  <!-- 拖动时的遮罩层 - 放在反馈按钮前面，确保按钮在遮罩层之上 -->
  <view class="drag-mask {{isDragging ? 'active' : ''}}"
    catch:touchstart="preventTouchMove"
    catch:touchmove="preventTouchMove"
    catch:touchend="preventTouchMove"
    catch:touchcancel="preventTouchMove">
  </view>

  <!-- 悬浮反馈按钮 -->
  <view class="feedback-btn {{ isDragging ? 'dragging' : '' }}"
    catch:touchstart="onFeedbackBtnTouchStart"
    catch:touchmove="onFeedbackBtnTouchMove"
    catch:touchend="onFeedbackBtnTouchEnd"
    catch:touchcancel="onFeedbackBtnTouchEnd"
    style="left: {{feedbackBtnLeft}}px; top: {{feedbackBtnTop}}px;">
    <image class="feedback-icon" src="/images/feedback.png" mode="aspectFit" />
    <text>反馈</text>
  </view>
  ```

  ```css
  /* 优化版拖动遮罩层样式 */
  .drag-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999;
    /* 完全透明的背景 */
    background-color: transparent;
    pointer-events: none;
    touch-action: none;
    display: none;
  }

  .drag-mask.active {
    display: block;
    /* 激活时添加微量不透明度以捕获事件 */
    background-color: rgba(0, 0, 0, 0.01);
    pointer-events: auto;
  }

  /* 悬浮反馈按钮样式 */
  .feedback-btn {
    position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: transform 0.2s ease;
  }

  .feedback-btn.dragging {
    transform: scale(1.1);
    transition: none;
  }
  ```

  ```javascript
  // 优化版拖动逻辑
  // 阻止滚动穿透
  preventTouchMove(e) {
    // 阻止事件传播和默认行为
    if (e && e.preventDefault) {
      e.preventDefault();
    }
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    return false;
  },

  onFeedbackBtnTouchStart(e) {
    // 阻止事件传播
    this.preventTouchMove(e);

    if (e.touches.length !== 1) return;

    const touch = e.touches[0];
    this.startX = touch.clientX;
    this.startY = touch.clientY;
    this.offsetX = this.data.feedbackBtnLeft;
    this.offsetY = this.data.feedbackBtnTop;
    this.hasMoved = false;

    // 激活遮罩层，用于阻止背景滚动
    this.setData({ isDragging: true });

    // 临时禁用页面滚动 (额外措施)
    try {
      wx.disableScrolling && wx.disableScrolling({
        disable: true
      });
    } catch (err) {
      console.log('禁用滚动API不存在', err);
    }
  },

  onFeedbackBtnTouchMove(e) {
    // 阻止事件传播
    this.preventTouchMove(e);

    if (e.touches.length !== 1) return;

    // 确保遮罩层激活
    if (!this.data.isDragging) {
      this.setData({ isDragging: true });
    }

    const touch = e.touches[0];

    // 计算与起始位置的偏移量
    const deltaX = touch.clientX - this.startX;
    const deltaY = touch.clientY - this.startY;

    const windowInfo = wx.getWindowInfo();

    // 计算新位置
    let newLeft = this.offsetX + deltaX;
    let newTop = this.offsetY + deltaY;

    // 限制按钮在屏幕内
    newLeft = Math.max(0, Math.min(newLeft, windowInfo.windowWidth - 60));
    newTop = Math.max(0, Math.min(newTop, windowInfo.windowHeight - 60));

    this.setData({
      feedbackBtnLeft: newLeft,
      feedbackBtnTop: newTop
    });

    // 移动距离超过5px视为拖动
    if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
      this.hasMoved = true;
    }
  },

  onFeedbackBtnTouchEnd(e) {
    // 阻止事件传播
    this.preventTouchMove(e);

    // 延迟关闭遮罩层，确保所有触摸事件都处理完毕
    setTimeout(() => {
      this.setData({ isDragging: false });
    }, 50);

    // 重新启用页面滚动 (额外措施)
    try {
      wx.disableScrolling && wx.disableScrolling({
        disable: false
      });
    } catch (err) {
      console.log('启用滚动API不存在', err);
    }

    // 如果是点击而非拖动，则触发反馈按钮点击事件
    if (!this.hasMoved) {
      setTimeout(() => {
        this.onFeedbackBtnTap();
      }, 100);
    }

    this.hasMoved = false;
  }
  ```

  **优化版实现说明**：
  1. 使用`catch:`前缀代替`bind`，确保事件不会传播到背景元素
  2. 在所有触摸事件中主动调用`preventTouchMove`阻止事件冒泡
  3. 遮罩层采用`display: none/block`结合`pointer-events`控制，比visibility更可靠
  4. 在遮罩层激活时添加微量不透明度(0.01)，确保能捕获事件
  5. 延迟关闭遮罩层，确保所有触摸事件处理完毕
  6. 使用`wx.disableScrolling`API进一步加强对背景滚动的控制
  7. 区分点击和拖动行为，只有非拖动状态才触发点击事件

### 5.2 页面容器结构

标准页面结构：
```wxml
<view class="container">
  <!-- 背景图片 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 头部区域 -->
  <view class="header">
    <view class="title">页面标题</view>
    <view class="subtitle">副标题</view>

    <!-- VIP徽章 -->
    <view class="vip-badge-container" bindtap="onVipBadgeTap">
      <vip-badge id="vipBadge" isVip="{{isVip}}" remainingDays="{{vipRemainingDays}}" />
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 页面特定内容 -->
  </view>

  <!-- 底部区域 -->
  <view class="footer">
    <!-- 免责声明 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 仅供娱乐，非官方工具</view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" bindtap="goBack">
      返回上一页
    </view>

    <!-- 广告组件 -->
    <view class="ad-container">
      <ad-button text="支持我们" />
    </view>
  </view>

  <!-- 底部导航栏 -->
  <custom-tabbar selected="{{tabBarIndex}}"></custom-tabbar>

  <!-- VIP对话框 -->
  <vip-dialog
    show="{{showVipDialog}}"
    pageKey="{{pageKey}}"
    isVip="{{isVip}}"
    vipRemainingDays="{{vipRemainingDays}}"
    bind:close="onVipDialogClose"
    bind:buy="onBuyVip">
  </vip-dialog>
</view>
```

## 6. VIP系统集成

### 6.1 PageWithVIP 增强器

对于需要VIP功能的页面，使用 PageWithVIP 包装页面：

```js
import PageWithVIP from '../../utils/page-with-vip';

// 页面KEY标识，用于区分不同页面的免费次数
const PAGE_KEY = 'your-page-key';

PageWithVIP({
  data: {
    // 页面数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false
  },

  onLoad: function() {
    // 自定义加载逻辑
  },

  // VIP徽章点击事件
  handleVipBadgeClick() {
    this.setData({ showVipDialog: true });
  }
});
```

### 6.2 免费次数管理

每个抽奖活动页面有独立的免费次数计数系统，使用唯一的 `PAGE_KEY` 标识符进行区分。这些标识符在各页面JS文件顶部定义：

```js
// treasure-hunting.js
const PAGE_KEY = 'treasure-hunting';

// luckytree.js
const PAGE_KEY = 'luckytree';

// supertreasure.js
const PAGE_KEY = 'supertreasure';
```

API 工具类使用这些 `PAGE_KEY` 在本地存储中创建独立的键值对，确保每个活动的免费次数互不影响：

```js
/**
 * 获取当前页面的免费次数
 * @param {string} pageKey 页面标识
 * @returns {number} 剩余免费次数
 */
const getFreeCount = (pageKey) => {
  // 确保每个页面都有独立的存储键
  const key = `freeCount_${pageKey}`;
  const freeCount = wx.getStorageSync(key);

  // 如果是第一次访问该页面，给予初始免费次数
  if (freeCount === '' || freeCount === undefined) {
    // 每个活动页面初始赠送100次免费抽奖机会
    const initialFreeCount = 100;
    wx.setStorageSync(key, initialFreeCount);
    return initialFreeCount;
  }

  return freeCount;
};
```

在页面中使用这些函数来管理免费次数：

```js
const api = require('../../utils/api');

// 获取当前活动的免费次数
const freeCount = api.getFreeCount(PAGE_KEY);

// 更新免费次数（增加）
const newFreeCount = api.updateFreeCount(PAGE_KEY, 100);

// 更新免费次数（减少）
const newFreeCount = api.updateFreeCount(PAGE_KEY, -1);

// 检查VIP状态（VIP用户不消耗免费次数）
const isVip = api.isVip();
```

抽奖消耗次数的标准实现方式：

```js
// 抽奖前检查是否有足够的免费次数
canDraw(count = 1) {
  // VIP用户不受限制
  if (this.data.isVip) {
    return true;
  }

  // 非VIP用户，检查免费次数是否足够
  if (this.data.freeCount < count) {
    // 显示VIP对话框
    this.setData({ showVipDialog: true });
    return false;
  }

  return true;
}

// 抽奖后更新免费次数
singleDraw() {
  if (!this.canDraw(1)) return;

  // ... 抽奖逻辑 ...

  // 非VIP用户消耗免费次数
  if (!this.data.isVip) {
    const newFreeCount = Math.max(0, this.data.freeCount - 1);
    this.setData({ freeCount: newFreeCount });
    // 更新存储
    api.updateFreeCount(PAGE_KEY, -1);
  }
}
```

## 7. 抽奖模块开发指南

### 7.1 抽奖页面结构

所有抽奖页面应遵循以下结构：

```
/pages/lottery/[抽奖活动名称]/
├── [抽奖活动名称].js    # 页面逻辑
├── [抽奖活动名称].json  # 页面配置
├── [抽奖活动名称].wxml  # 页面模板
└── [抽奖活动名称].wxss  # 页面样式
```

### 7.2 抽奖配置

抽奖配置标准结构：

```js
const lotteryConfig = {
  // 抽奖活动ID
  activityId: {
    name: '抽奖活动名称',
    items: [
      {
        id: 1,
        name: '奖品名称',
        probability: 0.00001, // 概率，小数形式
        image: 'cloud://..../奖品图片.png',
        background: 'gold', // 背景类型：gold, purple, normal
        type: 'fixed', // 固定奖励，或 'select' 表示自选奖励
        fragmentValue: 10 // 碎片价值
      },
      // 更多奖品...
    ],
    cost: 300, // 单次抽奖消耗点券
    multiCost: 3000 // 10连抽消耗点券
  }
};
```

### 7.3 抽奖结果展示

对于单抽和十连抽，使用统一的结果展示组件：

```wxml
<!-- 抽奖结果展示 -->
<view class="result-container {{showResult ? 'show' : 'hide'}}">
  <!-- 单抽结果 -->
  <view class="single-result" wx:if="{{!isMultiDraw && results.length > 0}}">
    <view class="result-item {{results[0].background}}">
      <image class="item-image" src="{{results[0].image}}" mode="aspectFit"></image>
      <view class="item-name">{{results[0].displayName || results[0].name}}</view>
    </view>
  </view>

  <!-- 十连抽结果 -->
  <view class="multi-result" wx:if="{{isMultiDraw && results.length > 0}}">
    <view class="result-grid">
      <view class="result-item {{item.background}}" wx:for="{{results}}" wx:key="index">
        <image class="item-image" src="{{item.image}}" mode="aspectFit"></image>
        <view class="item-name">{{item.displayName || item.name}}</view>
      </view>
    </view>
  </view>
</view>
```

### 7.4 统计数据

所有抽奖页面应包含统计功能，使用以下数据结构：

```js
data: {
  statistics: [], // 抽奖统计
  totalDraws: 0, // 总抽奖次数
  // 其他相关数据
}
```

标准统计函数：

```js
/**
 * 添加物品到统计数据
 * @param {Object} item - 抽到的物品对象
 * @param {boolean} updateDrawCount - 是否更新总抽数，默认为false
 */
addToStatistics: function(item, updateDrawCount = false) {
  const statistics = this.data.statistics;
  let found = false;

  // 处理可能需要的自选物品特殊逻辑
  const itemForStats = { ...item };
  if (item.type === 'select' && item.displayName) {
    itemForStats.specificName = item.displayName;
  }

  // 查找现有统计项
  for (let i = 0; i < statistics.length; i++) {
    // 确定匹配逻辑（针对自选物品需特殊处理）
    let isMatch = false;
    if (item.type === 'select' && item.displayName) {
      isMatch = statistics[i].item.specificName === item.displayName &&
                statistics[i].item.id === item.id;
    } else {
      isMatch = statistics[i].item.id === item.id;
    }

    if (isMatch) {
      statistics[i].count++;
      statistics[i].probabilityText = (item.probability * 100).toFixed(3) + '%';
      found = true;
      break;
    }
  }

  // 如未找到则添加新统计项
  if (!found) {
    statistics.push({
      item: itemForStats,
      count: 1,
      percentage: '0.00',
      probabilityText: (item.probability * 100).toFixed(3) + '%'
    });
  }

  // 更新统计数据
  const totalDraws = updateDrawCount ? this.data.totalDraws + 1 : this.data.totalDraws;

  // 重新计算百分比
  for (let i = 0; i < statistics.length; i++) {
    statistics[i].percentage = (statistics[i].count / totalDraws * 100).toFixed(2);
  }

  // 按抽取次数排序
  statistics.sort((a, b) => b.count - a.count);

  // 更新数据
  const updateData = { statistics };
  if (updateDrawCount) {
    updateData.totalDraws = totalDraws;
  }

  this.setData(updateData);
}
```

## 8. 交互逻辑

### 8.1 页面导航

页面之间的标准导航方式：

```js
// 导航到新页面
wx.navigateTo({
  url: '/pages/path/to/page'
});

// 返回上一页
wx.navigateBack();

// 切换Tab页
wx.switchTab({
  url: '/pages/index/index'
});
```

### 8.2 用户提示

统一的用户提示样式：

```js
// 成功提示
wx.showToast({
  title: '操作成功',
  icon: 'success',
  duration: 1500
});

// 错误提示
wx.showToast({
  title: '操作失败',
  icon: 'none',
  duration: 1500
});

// 加载提示
wx.showLoading({
  title: '加载中...',
  mask: true
});
wx.hideLoading();

// 确认对话框
wx.showModal({
  title: '提示',
  content: '确定要执行此操作吗？',
  success(res) {
    if (res.confirm) {
      // 用户点击确认
    } else if (res.cancel) {
      // 用户点击取消
    }
  }
});
```

### 8.3 点击动效

所有可点击元素应有统一的点击动效：

```css
.button {
  transition: all 0.3s ease;
}

.button:active {
  transform: scale(0.95);
  opacity: 0.9;
}
```

### 8.4 弹窗设计

弹窗是小程序中常用的交互元素，设计弹窗时应遵循以下规范：

#### 8.4.1 基本弹窗结构

```wxml
<!-- 弹窗基本结构 -->
<view class="modal-container {{showModal ? 'show' : 'hide'}}">
  <!-- 透明遮罩层，防止背景滑动 -->
  <view class="modal-mask" catchtouchmove="preventTouchMove"></view>

  <!-- 弹窗内容 -->
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">弹窗标题</text>
      <view class="close-button" bindtap="closeModal">×</view>
    </view>

    <view class="modal-body">
      <!-- 弹窗主体内容 -->
    </view>

    <view class="modal-footer">
      <view class="modal-button cancel" bindtap="closeModal">取消</view>
      <view class="modal-button confirm" bindtap="confirmModal">确定</view>
    </view>
  </view>
</view>
```

```css
/* 弹窗样式 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-container.show {
  opacity: 1;
  visibility: visible;
}

/* 遮罩层样式 */
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: -1;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
}
```

```javascript
// 阻止背景滑动的函数
preventTouchMove: function() {
  return false; // 阻止事件冒泡和默认行为
},

// 打开弹窗
showModal: function() {
  this.setData({ showModal: true });
},

// 关闭弹窗
closeModal: function() {
  this.setData({ showModal: false });
}
```

#### 8.4.2 优化版弹窗结构（推荐）

以下是优化版弹窗结构，解决了内容滚动与背景滑动冲突的问题：

```wxml
<!-- 优化版弹窗结构 -->
<view wx:if="{{showModal}}">
  <!-- 透明遮罩层，防止背景滑动 -->
  <view class="modal-mask" catchtouchmove="preventTouchMove" bindtap="closeModal"></view>

  <!-- 弹窗容器 -->
  <view class="modal-container show">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">弹窗标题</text>
        <view class="close-button" bindtap="closeModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 弹窗主体内容，可滚动 -->
      </view>

      <view class="modal-footer">
        <view class="modal-button cancel" bindtap="closeModal">取消</view>
        <view class="modal-button confirm" bindtap="confirmModal">确定</view>
      </view>
    </view>
  </view>
</view>
```

```css
/* 优化版弹窗样式 */
/* 遮罩层样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.modal-container.show {
  opacity: 1;
  pointer-events: auto;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 1000; /* 确保内容在遮罩层之上 */
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.modal-container.show .modal-content {
  transform: scale(1);
}

.modal-body {
  padding: 30rpx;
  max-height: calc(80vh - 180rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}
```

```javascript
// 优化版阻止背景滑动的函数
preventTouchMove: function(e) {
  // 阻止事件传播和默认行为
  if (e && e.preventDefault) {
    e.preventDefault();
  }
  if (e && e.stopPropagation) {
    e.stopPropagation();
  }
  return false;
},

// 阻止事件冒泡
stopPropagation: function(e) {
  if (e) {
    e.stopPropagation();
  }
  return false;
},

// 打开弹窗
showModal: function() {
  this.setData({ showModal: true });
},

// 关闭弹窗
closeModal: function() {
  this.setData({ showModal: false });
}
```

**重要说明**：
1. 使用 `catchtouchmove="preventTouchMove"` 阻止背景滑动
2. 遮罩层 `modal-mask` 必须是全屏的，确保整个屏幕都无法滑动
3. 使用 `z-index` 确保弹窗显示在最顶层
4. 弹窗显示/隐藏应使用CSS过渡效果，提升用户体验
5. 优化版弹窗结构将遮罩层和弹窗内容分开，不再嵌套在同一个容器中
6. 使用 `wx:if` 条件渲染，而不是通过CSS类控制显示/隐藏
7. 不要在弹窗内容上添加 `catchtouchmove`，以确保内容可以正常滚动
8. 使用 `-webkit-overflow-scrolling: touch` 增强iOS设备上的滚动体验

## 9. 新页面开发流程

1. **在app.json中注册页面**
   ```json
   "pages": [
     // 现有页面...
     "pages/lottery/your-new-lottery/your-new-lottery"
   ]
   ```

2. **创建页面文件结构**
   ```
   /pages/lottery/your-new-lottery/
   ├── your-new-lottery.js
   ├── your-new-lottery.json
   ├── your-new-lottery.wxml
   ├── your-new-lottery.wxss
   ```

3. **准备云资源**
   - 在云开发控制台创建对应目录
   - 上传所需图片资源

4. **编写页面逻辑**
   - 使用PageWithVIP包装页面
   - 实现抽奖逻辑和统计功能
   - 添加VIP和免费次数管理

5. **设计页面布局**
   - 遵循本文档中的样式规范
   - 使用响应式设计适配不同屏幕

6. **测试与优化**
   - 测试页面功能和体验
   - 优化性能和用户体验

## 10. 卡片与弹窗最佳实践

### 10.1 奖品卡片设计

奖品卡片应使用以下样式规范，确保名称突出显示：

```wxml
<!-- 奖品卡片结构 -->
<view class="prize-card">
  <image class="prize-image" src="{{item.image_url}}" mode="aspectFit"></image>
  <view class="prize-info">
    <view class="prize-name-container">
      <view class="prize-name-large">{{item.name}}</view>
      <view class="prize-quantity" wx:if="{{item.quantity}}">x{{item.quantity}}</view>
    </view>
    <view class="prize-meta">
      <view class="prize-type">{{item.prize_type}}</view>
      <view class="prize-source">{{item.source_name}}</view>
    </view>
  </view>
</view>
```

```css
/* 奖品卡片样式 */
.prize-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  display: flex;
  padding: 20rpx;
}

.prize-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.prize-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.prize-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  margin-top: 2rpx;
}

.prize-name-large {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 100rpx);
  line-height: 1.3;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.prize-quantity {
  font-size: var(--font-size-normal);
  color: #4a90e2;
  margin-left: 10rpx;
  white-space: nowrap;
  font-weight: 600;
  background-color: rgba(74, 144, 226, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

.prize-meta {
  display: flex;
  font-size: var(--font-size-small);
  color: #666;
}

.prize-type {
  margin-right: 16rpx;
}
```

**重要说明**：
1. 奖品名称应使用更大的字体（var(--font-size-title)）和黑体字，增强可读性
2. 使用轻微的文字阴影（text-shadow）使奖品名称更加突出
3. 奖品数量应使用背景色和圆角，使其更加醒目
4. 奖品卡片应保持图片大小不变，减小卡片整体高度，使用圆角展示

### 10.2 弹窗遮罩层实现

弹窗遮罩层的正确实现方式，确保背景不会滑动而弹窗内容可以正常滚动：

```wxml
<!-- 弹窗结构 -->
<view wx:if="{{showModal}}">
  <!-- 透明遮罩层，防止背景滑动 -->
  <view class="modal-mask" catchtouchmove="preventTouchMove" bindtap="closeModal"></view>

  <!-- 弹窗容器 -->
  <view class="modal-container show">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">弹窗标题</text>
        <view class="close-button" bindtap="closeModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 弹窗主体内容，可滚动 -->
      </view>
    </view>
  </view>
</view>
```

```css
/* 遮罩层样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.modal-container.show {
  opacity: 1;
  pointer-events: auto;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 1000; /* 确保内容在遮罩层之上 */
}

.modal-body {
  padding: 30rpx;
  max-height: calc(80vh - 180rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}
```

```javascript
// 阻止滑动穿透的函数
preventTouchMove: function(e) {
  // 阻止事件传播和默认行为
  if (e && e.preventDefault) {
    e.preventDefault();
  }
  if (e && e.stopPropagation) {
    e.stopPropagation();
  }
  return false;
},

// 阻止事件冒泡
stopPropagation: function(e) {
  if (e) {
    e.stopPropagation();
  }
  return false;
}
```

**重要说明**：
1. 遮罩层和弹窗内容应分开，不再嵌套在同一个容器中
2. 使用`catchtouchmove="preventTouchMove"`阻止背景滑动
3. 不要在弹窗内容上添加`catchtouchmove`，以确保内容可以正常滚动
4. 使用`z-index`确保弹窗内容显示在遮罩层之上
5. 使用`-webkit-overflow-scrolling: touch`增强iOS设备上的滚动体验

## 11. 小游戏开发指南

### 11.1 2048游戏

2048游戏是工具箱中的一个小游戏，具有以下特点：

1. **游戏布局**
   - 使用4x4网格布局
   - 方块使用图片背景，数字显示在右下角作为角标
   - 游戏区域应足够大，与屏幕边缘保持较小间距

2. **方块样式**
   ```css
   .tile {
     position: relative;
     width: 100%;
     height: 100%;
     box-sizing: border-box;
     grid-column: span 1;
     grid-row: span 1;
   }

   .tile-inner {
     width: 100%;
     height: 100%;
     border-radius: 6rpx;
     display: flex;
     position: relative;
     overflow: hidden;
   }

   .tile-image {
     width: 100%;
     height: 100%;
     position: absolute;
     top: 0;
     left: 0;
     z-index: 1;
     object-fit: cover;
   }

   .tile-value {
     font-size: calc(var(--cell-size) * 0.3);
     line-height: 1;
     position: absolute;
     bottom: 5rpx;
     right: 5rpx;
     z-index: 2;
     color: #FFFFFF;
     -webkit-text-stroke: 1.5px #000000;
     text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.8);
     font-weight: bold;
   }
   ```

3. **网格对齐**
   - 使用CSS Grid布局确保方块与网格完全对齐
   - 在WXML中使用grid-column和grid-row定位方块
   ```wxml
   <view class="grid-tiles">
     <view wx:for="{{tiles}}" wx:key="id"
           class="tile tile-{{item.value}}"
           style="grid-column: {{item.x + 1}}; grid-row: {{item.y + 1}};">
       <view class="tile-inner">
         <image class="tile-image" src="/images/2048/{{item.value}}.png"></image>
         <text class="tile-value">{{item.value}}</text>
       </view>
     </view>
   </view>
   ```

4. **区域对齐**
   - 所有区域（得分、最高分、连击信息、游戏控制区域等）应保持相同宽度
   - 使用统一的容器样式确保对齐：
   ```css
   /* 游戏信息区域 */
   .game-info {
     display: flex;
     justify-content: space-between;
     width: var(--grid-width);
     max-width: 98vw;
     margin: 0 auto 20rpx;
     box-sizing: border-box;
   }

   /* 连击和最终得分信息 */
   .bonus-info {
     display: flex;
     flex-wrap: wrap;
     justify-content: space-between;
     width: var(--grid-width);
     max-width: 98vw;
     margin: 0 auto 20rpx;
     box-sizing: border-box;
   }

   /* 游戏操作区域 */
   .game-controls {
     display: flex;
     flex-wrap: wrap;
     justify-content: space-between;
     width: var(--grid-width);
     max-width: 98vw;
     margin: 0 auto 20rpx;
     box-sizing: border-box;
   }
   ```

5. **VIP徽章位置**
   - 与其他页面保持一致，固定在右上角
   - 使用position: fixed确保在滚动时仍然可见

6. **标题样式**
   - 与工具箱页面保持一致的字体样式
   - 使用统一的颜色和大小

### 11.2 其他小游戏开发规范

开发其他小游戏时，应遵循以下规范：

1. **统一的页面结构**
   - 使用标准的页面容器和背景
   - 保持VIP徽章位置一致
   - 添加返回按钮和免责声明

2. **游戏资源管理**
   - 图片资源放在对应游戏的目录下
   - 使用合适的图片格式和压缩方式

3. **性能优化**
   - 避免频繁的DOM操作
   - 使用requestAnimationFrame进行动画
   - 合理管理内存，避免内存泄漏

4. **区域对齐**
   - 所有游戏页面的UI元素应保持对齐
   - 使用相同的容器宽度和边距
   - 确保在不同屏幕尺寸下保持一致的布局

## 12. 通用最佳实践

1. **使用云存储存放图片资源**，减小小程序包大小
2. **统一使用PageWithVIP增强器**，确保VIP功能一致性
3. **保持样式一致性**，遵循本文档的设计规范
4. **优化性能**，避免不必要的重复渲染
5. **完善错误处理**，提供友好的用户提示
6. **添加详细注释**，方便后续维护

---

本文档将随项目发展持续更新，如有问题请联系项目维护者。