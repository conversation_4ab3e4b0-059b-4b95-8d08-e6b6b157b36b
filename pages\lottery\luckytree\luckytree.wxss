/* 抽奖模拟器页面样式 */
.container {
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  position: relative;
  padding-bottom: 180rpx;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 页面标题 */
.page-title {
  text-align: center;
  color: white;
  padding: 30rpx 0;
  font-size: 40rpx;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.subtitle {
  font-size: 24rpx;
  font-weight: normal;
  margin-top: 10rpx;
  opacity: 0.8;
}

/* VIP徽章容器 */
.vip-badge-container {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 10;
}

/* 卡片样式 */
.dark-card {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 0 auto 30rpx auto;
  max-width: 92%;
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.light-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 0 auto 30rpx auto;
  max-width: 92%;
  color: #333;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 活动规则卡片 */
.rules-card {
  position: relative;
}

.rules-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
  color: #f0f0f0;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.rules-content text {
  font-size: 26rpx;
  color: #e0e0e0;
}

.probability-link {
  margin-top: 16rpx;
}

.highlight-text {
  color: #ffcc00;
  font-size: 26rpx;
}

/* 概率公示面板 */
.probability-panel {
  transition: all 0.3s ease;
  overflow: hidden;
}

.show-panel {
  max-height: 800rpx;
  opacity: 1;
  margin-bottom: 30rpx;
}

.hide-panel {
  max-height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.panel-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #f0f0f0;
}

.close-button {
  font-size: 40rpx;
  color: #e0e0e0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.probability-table {
  width: 100%;
}

.table-header {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #555;
  font-weight: bold;
}

.table-row {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.gold-row {
  background-color: rgba(255, 215, 0, 0.1);
}

.purple-row {
  background-color: rgba(128, 0, 128, 0.1);
}

.table-cell-left {
  flex: 3;
  font-size: 24rpx;
  color: #e0e0e0;
}

.table-cell-right {
  flex: 1;
  font-size: 24rpx;
  color: #e0e0e0;
  text-align: right;
}

.more-probability-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  color: #ffcc00;
  font-size: 24rpx;
}

.toggle-icon {
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  margin-left: 10rpx;
}

.toggle-icon.down {
  border-top: 12rpx solid #ffcc00;
  border-bottom: none;
}

.toggle-icon.up {
  border-bottom: 12rpx solid #ffcc00;
  border-top: none;
}

.more-probability {
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.more-probability.show {
  max-height: 1000rpx;
}

.more-probability.hide {
  max-height: 0;
}

/* 抽奖结果展示区域 */
.results-display {
  width: 100%;
}

.results-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

/* 单抽结果容器 */
.single-result-container {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
}

.single-result-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  width: 200rpx;
  box-sizing: border-box;
}

.single-result-item image {
  width: 140rpx;
  height: 140rpx;
  object-fit: contain;
}

.single-result-item .result-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-top: 16rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 改进十连抽结果网格样式 */
.results-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12rpx;
  padding: 8rpx;
}

.result-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 8rpx;
  box-sizing: border-box;
  height: 130rpx;
  width: 100%;
  overflow: hidden;
}

.result-grid-item image {
  width: 70rpx;
  height: 70rpx;
  min-height: 70rpx;
  object-fit: contain;
  flex-shrink: 0;
  margin-bottom: 6rpx;
}

.result-grid-item text {
  font-size: 20rpx;
  color: #333;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  max-height: 40rpx;
  line-height: 1;
}

.gold-bg {
  background: linear-gradient(180deg, #fff8e7 0%, #ffeabc 100%);
  border: 1px solid #ffd700;
}

.purple-bg {
  background: linear-gradient(180deg, #f8f1ff 0%, #e9d5ff 100%);
  border: 1px solid #800080;
}

/* 抽奖按钮区域 */
.draw-buttons-container {
  display: flex;
  justify-content: space-around;
  margin: 30rpx auto;
  width: 90%;
  max-width: 600rpx;
}

.draw-button {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.draw-button.single {
  background: linear-gradient(135deg, #9254de 0%, #6b46c1 100%);
  color: white;
}

.draw-button.multi {
  background: linear-gradient(135deg, #ff9a44 0%, #ff6b6b 100%);
  color: white;
}

.draw-button.disabled {
  opacity: 0.7;
  pointer-events: none;
}

.draw-animation {
  text-align: center;
  margin: 30rpx 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
  margin: 0 10rpx;
  display: inline-block;
  animation: loading 0.4s infinite ease-in-out both;
}

@keyframes loading {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.loading-text {
  font-size: 28rpx;
  color: white;
  margin-top: 16rpx;
  display: block;
}

/* 统计区域 */
.stats-container {
  width: 100%;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 15rpx;
}

.stats-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.stats-summary {
  font-size: 24rpx;
  color: #666;
  margin: 8rpx 0;
  flex: 1;
  text-align: center;
}

.reset-button {
  background-color: #f44336;
  color: white;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

.stats-category {
  margin-bottom: 24rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #6b46c1;
}

.simple-stats-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.simple-stats-item {
  display: flex;
  align-items: center;
  padding: 12rpx;
  border-radius: 8rpx;
  width: calc(50% - 8rpx);
  box-sizing: border-box;
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  object-fit: contain;
  margin-right: 16rpx;
}

.item-name {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-count {
  font-size: 22rpx;
  color: #666;
  min-width: 60rpx;
  text-align: right;
}

.no-stats {
  width: 100%;
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 30rpx;
}

/* 底部区域 */
.footer {
  padding: 20rpx 0;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.disclaimer-card {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  width: 90%;
}

.disclaimer {
  font-size: 20rpx;
  color: #ccc;
  text-align: center;
}

/* 底部按钮容器 */
.bottom-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.ranking-button {
  background-color: #ff9a44;
  color: white;
  font-size: 28rpx;
  padding: 10rpx 40rpx;
  border-radius: 30rpx;
}

.back-button {
  background-color: #1e88e5;
  color: white;
  font-size: 28rpx;
  padding: 10rpx 40rpx;
  border-radius: 30rpx;
}

.ad-container {
  width: 90%;
}

/* 页面上方排行榜按钮样式 */
.ranking-button-top {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  display: flex;
  align-items: center;
  background-color: #e08400;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.ranking-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
  color: #fff;
}

.ranking-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
}

/* 排行榜按钮点击效果 */
.ranking-button-top:active {
  transform: scale(0.95);
  background-color: #bb6e00;
}

/* 响应式布局调整 */
@media (max-width: 320px) {
  .results-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 6rpx;
  }

  .result-grid-item {
    height: 120rpx;
    padding: 6rpx;
  }

  .result-grid-item image {
    width: 60rpx;
    height: 60rpx;
    min-height: 60rpx;
  }

  .result-grid-item text {
    font-size: 18rpx;
  }

  .single-result-item {
    width: 160rpx;
  }

  .single-result-item image {
    width: 120rpx;
    height: 120rpx;
  }
}

@media (min-width: 768px) {
  .results-grid {
    gap: 16rpx;
  }

  .result-grid-item {
    height: 150rpx;
    padding: 10rpx;
  }

  .result-grid-item image {
    width: 90rpx;
    height: 90rpx;
    min-height: 90rpx;
  }

  .result-grid-item text {
    font-size: 22rpx;
  }

  .single-result-item {
    width: 240rpx;
  }

  .single-result-item image {
    width: 180rpx;
    height: 180rpx;
  }
}

/* 添加到统计区域样式中 */
.stats-summary-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.stats-summary-row {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}