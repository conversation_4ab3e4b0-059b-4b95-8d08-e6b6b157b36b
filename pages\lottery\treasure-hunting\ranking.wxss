/* 赛车夺宝排行榜页面样式 */

/* 容器样式 */
.ranking-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  color: #fff;
}

/* 背景图片 */
.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 头部样式 */
.ranking-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  position: relative;
}

.back-button, .refresh-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}

.back-icon, .refresh-icon {
  font-size: 40rpx;
  color: #fff;
}

.ranking-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 标签切换样式 */
.ranking-tabs {
  width: 90%;
  display: flex;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #aaa;
  position: relative;
}

.tab.active {
  color: #ffcb05;
  font-weight: bold;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  width: 60%;
  height: 6rpx;
  background-color: #ffcb05;
  border-radius: 3rpx;
}

/* 用户排名信息样式 */
.user-ranking {
  width: 90%;
  background-color: rgba(255, 203, 5, 0.2);
  border: 2rpx solid #ffcb05;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
}

.user-rank-text, .user-score-text {
  font-size: 28rpx;
  color: #ffcb05;
}

/* 排行榜内容样式 */
.ranking-content {
  width: 90%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.ranking-header-row {
  display: flex;
  padding: 20rpx 10rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
  font-weight: bold;
  color: #ffcb05;
  background-color: rgba(255, 203, 5, 0.1);
  border-radius: 8rpx 8rpx 0 0;
}

.ranking-list {
  max-height: 700rpx;
  transition: opacity 0.3s ease;
}

.ranking-item {
  display: flex;
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

/* 当前用户高亮 */
.ranking-item.current-user {
  background-color: rgba(255, 203, 5, 0.2);
}

/* 前三名行样式 */
.ranking-item:nth-child(-n+3) {
  padding: 25rpx 10rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
}

/* 第一名行样式 */
.ranking-item:first-child {
  background: linear-gradient(to right, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05), rgba(255, 215, 0, 0.1));
  border-bottom: 2rpx solid rgba(255, 215, 0, 0.3);
}

/* 第二名行样式 */
.ranking-item:nth-child(2) {
  background: linear-gradient(to right, rgba(192, 192, 192, 0.1), rgba(192, 192, 192, 0.05), rgba(192, 192, 192, 0.1));
  border-bottom: 2rpx solid rgba(192, 192, 192, 0.3);
}

/* 第三名行样式 */
.ranking-item:nth-child(3) {
  background: linear-gradient(to right, rgba(205, 127, 50, 0.1), rgba(205, 127, 50, 0.05), rgba(205, 127, 50, 0.1));
  border-bottom: 2rpx solid rgba(205, 127, 50, 0.3);
}

.rank-cell {
  width: 15%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 60rpx;
}

.rank-number {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  font-size: 28rpx;
}

/* 前三名样式 */
.rank-number.top-rank {
  background-color: #ffcb05;
  color: #333;
  font-weight: bold;
  box-shadow: 0 0 10rpx rgba(255, 203, 5, 0.8);
  transform: scale(1.1);
}

/* 第一名特殊样式 */
.ranking-item:first-child .rank-number.top-rank {
  background: linear-gradient(135deg, #ffd700, #ffaa00);
  color: #fff;
  box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.8);
  transform: scale(1.2);
}

/* 第二名特殊样式 */
.ranking-item:nth-child(2) .rank-number.top-rank {
  background: linear-gradient(135deg, #c0c0c0, #e0e0e0);
  color: #333;
  box-shadow: 0 0 12rpx rgba(192, 192, 192, 0.8);
  transform: scale(1.15);
}

/* 第三名特殊样式 */
.ranking-item:nth-child(3) .rank-number.top-rank {
  background: linear-gradient(135deg, #cd7f32, #e0955e);
  color: #fff;
  box-shadow: 0 0 10rpx rgba(205, 127, 50, 0.8);
  transform: scale(1.1);
}

.user-cell {
  width: 20%;
  display: flex;
  align-items: center;
  padding-left: 10rpx;
}

.user-name {
  font-size: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 前三名用户名样式 */
.ranking-item:nth-child(-n+3) .user-name {
  font-weight: bold;
  font-size: 30rpx;
}

/* 第一名用户名样式 */
.ranking-item:first-child .user-name {
  color: #ffd700; /* 金色 */
  text-shadow: 0 0 3rpx rgba(0, 0, 0, 0.3);
}

/* 第二名用户名样式 */
.ranking-item:nth-child(2) .user-name {
  color: #c0c0c0; /* 银色 */
  text-shadow: 0 0 3rpx rgba(0, 0, 0, 0.3);
}

/* 第三名用户名样式 */
.ranking-item:nth-child(3) .user-name {
  color: #cd7f32; /* 铜色 */
  text-shadow: 0 0 3rpx rgba(0, 0, 0, 0.3);
}

.score-cell {
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #ffcb05;
}

/* 前三名分数样式 */
.ranking-item:nth-child(-n+3) .score-cell {
  font-weight: bold;
  font-size: 30rpx;
}

/* 第一名分数样式 */
.ranking-item:first-child .score-cell {
  color: #ffd700; /* 金色 */
  text-shadow: 0 0 5rpx rgba(255, 215, 0, 0.5);
}

/* 第二名分数样式 */
.ranking-item:nth-child(2) .score-cell {
  color: #e0e0e0; /* 亮银色 */
  text-shadow: 0 0 5rpx rgba(192, 192, 192, 0.5);
}

/* 第三名分数样式 */
.ranking-item:nth-child(3) .score-cell {
  color: #e0955e; /* 亮铜色 */
  text-shadow: 0 0 5rpx rgba(205, 127, 50, 0.5);
}

.time-cell {
  width: 25%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22rpx;
  color: #aaa;
}

.no-data {
  padding: 100rpx 0;
  text-align: center;
  color: #aaa;
  font-size: 28rpx;
}

/* 底部提示区域样式 */
.ranking-footer {
  width: 90%;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.info-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10rpx;
}

.info-text text {
  font-size: 24rpx;
  color: #ddd;
  line-height: 1.5;
}

.info-text .info-title {
  font-size: 28rpx;
  color: #ffcb05;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.info-text .info-note {
  margin-top: 10rpx;
  color: #aaa;
  font-style: italic;
  align-self: center;
}

/* 底部按钮容器 */
.footer-buttons {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-top: 20rpx;
}

.upload-button, .delete-button {
  width: 45%;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.upload-button {
  background-color: #ffcb05;
  color: #333;
}

.delete-button {
  background-color: #ff6b6b;
  color: #fff;
}

/* 用户信息填写弹窗样式 */
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  background-color: #f5f5f5;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 40rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  width: 160rpx;
  height: 160rpx;
  padding: 0;
  background: none;
  border: none;
  margin: 0;
  line-height: normal;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 2rpx solid #eee;
}

.avatar-tips {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}

.nickname-section {
  margin-bottom: 40rpx;
}

.nickname-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  color: #333;
  background-color: #fff;
}

.wechat-nickname-tip {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #1aad19;
  text-align: center;
}

/* 显示选中昵称的样式 */
.selected-nickname {
  margin-top: 20rpx;
  padding: 15rpx;
  background: rgba(26, 173, 25, 0.1);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.nickname-value {
  color: #1aad19;
  font-weight: bold;
}

/* 表单按钮 */
.form-buttons {
  display: flex;
  margin-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.form-buttons .cancel-btn,
.form-buttons .confirm-btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  background: #fff;
  border-radius: 0;
  margin: 0;
  padding: 0;
  border: none;
}

.form-buttons .cancel-btn {
  color: #999;
  border-right: 1rpx solid #eee;
}

.form-buttons .confirm-btn {
  color: #ffcb05;
  font-weight: bold;
}

/* 重置微信按钮样式 */
button {
  background: none;
  padding: 0;
  margin: 0;
  line-height: normal;
}

button::after {
  border: none;
}