<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>
  
  <!-- 赛车夺宝活动信息 -->
  <view class="activity-info">
    <view class="activity-name">{{activity.name}}</view>
    <view class="vip-badge-container" bindtap="onVipBadgeTap">
      <vip-badge 
        id="vipBadge"
        pageType="lottery"
        pageKey="{{pageKey}}"
        isVip="{{isVip}}" 
        freeCount="{{freeCount}}" 
        remainingDays="{{vipRemainingDays}}">
      </vip-badge>
    </view>
    <view class="activity-description">
      <text wx:if="{{activityId === 'treasurehunting'}}" class="real-cost-info">抽奖概率模拟器</text>
    </view>
    
    <!-- 添加排行榜按钮到活动信息区域 -->
    <view class="ranking-button-top" bindtap="openRanking">
      <text class="ranking-icon">🏆</text>
      <text class="ranking-text">排行榜</text>
    </view>
    
    <view class="activity-rules card-width">
      <text class="rules-title">活动规则</text>
      <view class="rules-content">
        <text>1. 通过单抽和十连抽模拟赛车夺宝活动</text>
        <text>2. 十连抽保底至少获得1个自选A级赛车(30天)</text>
        <text class="guarantee-note">3. 抽奖概率基于游戏公示数据，仅供娱乐，非官方工具</text>
        <view class="probability-link" bindtap="toggleProbabilityPanel"><text class="highlight-text">查看概率公示 >></text></view>
      </view>
    </view>
  </view>
  <!-- 恢复中奖结果展示区域，使用5列布局 -->
  <view class="results-display card-width" wx:if="{{showResult && !running}}">
    <view class="results-title">{{isMultiDraw ? '十连抽结果' : '单抽结果'}}</view>
    <view class="results-grid five-column">
      <view class="result-grid-item {{item.background === 'legendary' ? 'legendary-bg' : (item.background === 'gold' ? 'gold-bg' : (item.background === 'purple' ? 'purple-bg' : ''))}}" 
            wx:for="{{results}}" wx:key="index">
        <image src="{{item.image}}" mode="aspectFit"></image>
        <text>{{item.displayName || item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 抽奖按钮区域 -->
  <view class="draw-area" wx:if="{{selectionCompleted}}">
    <view class="buttons">
      <view class="draw-button single {{running ? 'disabled' : ''}}" bindtap="singleDraw">单抽</view>
      <view class="draw-button multi {{running ? 'disabled' : ''}}" bindtap="multiDraw">十连抽</view>
    </view>
    
    <!-- 抽奖进行中的动画 -->
    <view class="draw-animation" wx:if="{{running}}">
      <view class="loading">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <text class="loading-text">抽奖中...</text>
    </view>
  </view>

  <!-- 提示完成自选设置 -->
  <view class="setup-reminder" wx:if="{{!selectionCompleted}}">
    <view class="reminder-text">请先完成自选设置才能开始抽奖</view>
    <view class="setup-button" bindtap="openSelectPanel">设置自选奖励</view>
  </view>

  <!-- 统计面板 -->
  <view class="panels-container">
    <!-- 概率公示面板 -->
    <view class="probability-panel {{showProbabilityPanel ? 'show-panel' : 'hide-panel'}}">
      <view class="panel-header">
        <text class="panel-title">概率公示</text>
        <view class="close-button" bindtap="toggleProbabilityPanel">×</view>
      </view>
      
      <view class="probability-table">
        <view class="table-header">
          <text class="table-cell-left">奖励</text>
          <text class="table-cell-right">概率</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">至尊·盘龙</text>
          <text class="table-cell-right">0.002%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">至尊·盘龙(15天)</text>
          <text class="table-cell-right">0.004%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">自选S级赛车</text>
          <text class="table-cell-right">0.008%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">自选S级赛车(15天)</text>
          <text class="table-cell-right">0.020%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">自选T1机甲</text>
          <text class="table-cell-right">0.030%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">电玩雷诺</text>
          <text class="table-cell-right">0.030%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">自选T1机甲(30天)</text>
          <text class="table-cell-right">0.510%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">电玩雷诺(30天)</text>
          <text class="table-cell-right">0.510%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">自选A级赛车</text>
          <text class="table-cell-right">0.030%</text>
        </view>
        
        <view class="table-row">
          <text class="table-cell-left">自选A级赛车(30天)</text>
          <text class="table-cell-right">0.810%</text>
        </view>
      </view>
    </view>
    
    <!-- 简易统计面板 -->
    <view class="simple-stats-panel">
      <view class="panel-header">
        <text class="panel-title">抽奖统计</text>
        <view class="stats-summary-container">
          <view class="stats-summary-row">
            <text class="stats-summary">总抽数: {{totalDraws}}</text>
            <text class="stats-summary fragments">碎片数: {{totalFragments}}</text>
          </view>
        </view>
        <view class="reset-button" bindtap="resetDrawState">重置</view>
      </view>
      
      <view class="action-buttons">
        <view class="action-button" bindtap="openSelectPanel">修改自选</view>
        <view class="action-button reset-selection" bindtap="resetSelection">重置自选</view>
      </view>
      
      <!-- 简单统计列表，按稀有度分类 -->
      <view class="simple-stats-list">
        <view class="stats-category">
          <text class="category-title">永久奖励</text>
          <view class="simple-stats-item legendary-bg" wx:for="{{statistics}}" wx:key="id" wx:if="{{item.item.background === 'legendary' && item.count > 0}}">
            <image class="item-icon" src="{{item.item.image}}" mode="aspectFit"></image>
            <text class="item-name">{{item.item.specificName || item.item.displayName || item.item.name}}</text>
            <text class="item-count">{{item.count}}次</text>
          </view>
          <view class="simple-stats-item gold-bg" wx:for="{{statistics}}" wx:key="id" wx:if="{{item.item.background === 'gold' && item.count > 0}}">
            <image class="item-icon" src="{{item.item.image}}" mode="aspectFit"></image>
            <text class="item-name">{{item.item.specificName || item.item.displayName || item.item.name}}</text>
            <text class="item-count">{{item.count}}次</text>
          </view>
          <view class="no-stats" wx:if="{{!hasLegendaryItems && !hasTotalGoldItems}}">
            <text>暂无获得</text>
          </view>
        </view>
        
        <view class="stats-category">
          <text class="category-title">稀有奖励</text>
          <view class="simple-stats-item purple-bg" wx:for="{{statistics}}" wx:key="id" wx:if="{{item.item.background === 'purple' && item.count > 0}}">
            <image class="item-icon" src="{{item.item.image}}" mode="aspectFit"></image>
            <text class="item-name">{{item.item.specificName || item.item.displayName || item.item.name}}</text>
            <text class="item-count">{{item.count}}次</text>
          </view>
          <view class="no-stats" wx:if="{{!hasTotalPurpleItems}}">
            <text>暂无获得</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部区域 -->
  <view class="footer">
    <!-- 免责声明卡片 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 抽奖概率基于游戏公示数据，仅供娱乐，非官方工具</view>
    </view>
    
    <view class="back-button" bindtap="goBack">
      返回活动列表
    </view>
    
    <!-- 广告组件 -->
    <!--<view class="ad-container">
      <ad-button text="支持我们" />
    </view>-->
  </view>
  
  <!-- 自选面板 -->
  <view class="select-panel {{showSelectPanel ? 'show' : ''}}" wx:if="{{showSelectPanel}}" catchtouchmove="preventTouchMove">
    <view class="select-panel-content">
      <view class="select-header">
        <text class="select-title">自选奖励设置 ({{currentStep}}/3)</text>
        <view class="close-button" bindtap="closeSelectPanel">×</view>
      </view>
      
      <!-- 加载上次保存的设置按钮 -->
      <view class="load-previous-button" bindtap="loadPreviousSelection">加载上次保存的设置</view>
      
      <!-- 进度指示器 -->
      <view class="step-indicator">
        <view class="step {{currentStep >= 1 ? 'active' : ''}}">S级赛车</view>
        <view class="step-line"></view>
        <view class="step {{currentStep >= 2 ? 'active' : ''}}">T1机甲</view>
        <view class="step-line"></view>
        <view class="step {{currentStep >= 3 ? 'active' : ''}}">A级赛车</view>
      </view>
      
      <!-- 步骤按钮 - 移动到顶部 -->
      <view class="step-buttons top-buttons" wx:if="{{currentStep == 1}}">
        <view class="step-button next {{selectedOptions.sCars.length === 2 ? '' : 'disabled'}}" bindtap="nextStep">下一步</view>
      </view>
      
      <view class="step-buttons top-buttons" wx:if="{{currentStep == 2}}">
        <view class="step-button prev" bindtap="prevStep">上一步</view>
        <view class="step-button next {{selectedOptions.t1Mechs.length === 2 ? '' : 'disabled'}}" bindtap="nextStep">下一步</view>
      </view>
      
      <view class="step-buttons top-buttons" wx:if="{{currentStep == 3}}">
        <view class="step-button prev" bindtap="prevStep">上一步</view>
        <view class="step-button complete {{selectedOptions.aCars.length === 6 ? '' : 'disabled'}}" bindtap="confirmSelection">完成设置</view>
      </view>
      
      <!-- S级赛车选择 (步骤1) -->
      <view class="select-section" wx:if="{{currentStep == 1}}">
        <view class="section-title">自选S级赛车（选择2个，当前已选{{selectedOptions.sCars.length}}/2）</view>
        <view class="selection-hint">请从以下7个S级赛车中选择2个作为奖池中的自选赛车</view>
        
        <!-- 已选赛车区域 -->
        <view class="selected-items-area">
          <view class="selected-items-title">已选赛车</view>
          <view class="selected-items-container">
            <view class="selected-item-placeholder" wx:if="{{selectedOptions.sCars.length === 0}}">
              <text>请选择赛车</text>
            </view>
            <view class="selected-item" wx:for="{{selectedOptions.sCars}}" wx:key="*this" wx:for-index="idx">
              <image class="selected-item-image" src="{{selectionOptions.sCars[item].image}}" mode="aspectFit"></image>
              <text class="selected-item-name">{{selectionOptions.sCars[item].name}}</text>
              <view class="remove-button" data-type="sCars" data-index="{{item}}" bindtap="removeSelectedItem">×</view>
            </view>
          </view>
        </view>
        
        <!-- 可选赛车网格 -->
        <view class="selection-grid">
          <view class="grid-item {{selectedOptions.sCars.indexOf(i) >= 0 ? 'grid-item-selected' : ''}} {{selectedOptions.sCars.length >= 2 && selectedOptions.sCars.indexOf(i) < 0 ? 'grid-item-disabled' : ''}}" 
                wx:for="{{selectionOptions.sCars}}" wx:key="id" wx:for-index="i"
                data-index="{{i}}" data-type="sCars" bindtap="toggleSelection">
            <image class="grid-item-image" src="{{item.image}}" mode="aspectFit"></image>
            <text class="grid-item-name">{{item.name}}</text>
            <view class="selected-mark" wx:if="{{selectedOptions.sCars.indexOf(i) >= 0}}">已选</view>
          </view>
        </view>
      </view>
      
      <!-- T1机甲选择 (步骤2) -->
      <view class="select-section" wx:if="{{currentStep == 2}}">
        <view class="section-title">自选T1机甲（选择2个，当前已选{{selectedOptions.t1Mechs.length}}/2）</view>
        <view class="selection-hint">请从以下T1机甲中选择2个作为奖池中的自选机甲</view>
        
        <!-- 已选机甲区域 -->
        <view class="selected-items-area">
          <view class="selected-items-title">已选机甲</view>
          <view class="selected-items-container">
            <view class="selected-item-placeholder" wx:if="{{selectedOptions.t1Mechs.length === 0}}">
              <text>请选择机甲</text>
            </view>
            <view class="selected-item" wx:for="{{selectedOptions.t1Mechs}}" wx:key="*this" wx:for-index="idx">
              <image class="selected-item-image" src="{{selectionOptions.t1Mechs[item].image}}" mode="aspectFit"></image>
              <text class="selected-item-name">{{selectionOptions.t1Mechs[item].name}}</text>
              <view class="remove-button" data-type="t1Mechs" data-index="{{item}}" bindtap="removeSelectedItem">×</view>
            </view>
          </view>
        </view>
        
        <!-- 可选机甲网格 -->
        <view class="selection-grid">
          <view class="grid-item {{selectedOptions.t1Mechs.indexOf(i) >= 0 ? 'grid-item-selected' : ''}} {{selectedOptions.t1Mechs.length >= 2 && selectedOptions.t1Mechs.indexOf(i) < 0 ? 'grid-item-disabled' : ''}}" 
                wx:for="{{selectionOptions.t1Mechs}}" wx:key="id" wx:for-index="i"
                data-index="{{i}}" data-type="t1Mechs" bindtap="toggleSelection">
            <image class="grid-item-image" src="{{item.image}}" mode="aspectFit"></image>
            <text class="grid-item-name">{{item.name}}</text>
            <view class="selected-mark" wx:if="{{selectedOptions.t1Mechs.indexOf(i) >= 0}}">已选</view>
          </view>
        </view>
      </view>
      
      <!-- A级赛车选择 (步骤3) -->
      <view class="select-section" wx:if="{{currentStep == 3}}">
        <view class="section-title">自选A级赛车（选择6个，当前已选{{selectedOptions.aCars.length}}/6）</view>
        <view class="selection-hint">请从以下9个A级赛车中选择6个作为奖池中的自选赛车</view>
        
        <!-- 已选赛车区域 -->
        <view class="selected-items-area">
          <view class="selected-items-title">已选赛车</view>
          <view class="selected-items-container">
            <view class="selected-item-placeholder" wx:if="{{selectedOptions.aCars.length === 0}}">
              <text>请选择赛车</text>
            </view>
            <view class="selected-item" wx:for="{{selectedOptions.aCars}}" wx:key="*this" wx:for-index="idx">
              <image class="selected-item-image" src="{{selectionOptions.aCars[item].image}}" mode="aspectFit"></image>
              <text class="selected-item-name">{{selectionOptions.aCars[item].name}}</text>
              <view class="remove-button" data-type="aCars" data-index="{{item}}" bindtap="removeSelectedItem">×</view>
            </view>
          </view>
        </view>
        
        <!-- 可选赛车网格 -->
        <view class="selection-grid">
          <view class="grid-item {{selectedOptions.aCars.indexOf(i) >= 0 ? 'grid-item-selected' : ''}} {{selectedOptions.aCars.length >= 6 && selectedOptions.aCars.indexOf(i) < 0 ? 'grid-item-disabled' : ''}}" 
                wx:for="{{selectionOptions.aCars}}" wx:key="id" wx:for-index="i"
                data-index="{{i}}" data-type="aCars" bindtap="toggleSelection">
            <image class="grid-item-image" src="{{item.image}}" mode="aspectFit"></image>
            <text class="grid-item-name">{{item.name}}</text>
            <view class="selected-mark" wx:if="{{selectedOptions.aCars.indexOf(i) >= 0}}">已选</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- VIP对话框组件 -->
  <vip-dialog 
    show="{{showVipDialog}}" 
    pageKey="treasure-hunting"
    isVip="{{isVip}}"
    vipRemainingDays="{{vipRemainingDays}}"
    freeCount="{{freeCount}}"
    bind:close="onVipDialogClose" 
    bind:buy="onBuyVip">
  </vip-dialog>
</view> 