Page({
  onLoad() {
    // 从本地存储初始化抽奖次数
    try {
      const freeCount = wx.getStorageSync('freeCount') || 0;
      this.setData({ freeCount });
    } catch (e) {
      console.error('获取免费次数失败', e);
    }
  },
  
  // VIP 弹窗更新免费次数的事件处理函数
  onFreeCountUpdated(e) {
    console.log('免费次数已更新:', e.detail.freeCount);
    // 更新页面数据
    this.setData({
      freeCount: e.detail.freeCount
    });
    
    // 可能需要刷新页面其他依赖于免费次数的元素
    this.updateRelatedElements();
  },
  
  updateRelatedElements() {
    // 更新页面上依赖于免费次数的其他元素
    // ...
  }
}); 