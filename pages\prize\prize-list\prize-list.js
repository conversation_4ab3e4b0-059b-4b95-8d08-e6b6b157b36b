// 道具出货查询列表页面
import PageWithVIP from '../../../utils/page-with-vip';
const app = getApp();

// 引入API工具类和配置
const api = require('../../../utils/api');
const { getConfig } = require('../../../config');

// 页面KEY标识，用于区分不同页面的免费次数
const PAGE_KEY = 'prize-query';

// 每日免费查询次数
const FREE_QUERY_COUNT = 3;
// 观看广告获得的免费次数
const AD_REWARD_COUNT = 3;

// 标签页类型
const TAB_SOURCES = 'sources';
const TAB_PRIZES = 'prizes';

PageWithVIP({
  data: {
    // 页面数据
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 标签页控制
    activeTab: TAB_SOURCES, // 默认显示道具标签页

    // 道具列表数据
    sources: [],
    prizes: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,

    // 筛选条件
    search: '',
    sourceType: '',

    // 筛选选项
    sourceTypes: [],

    // 广告相关
    adLoaded: false,
    showAdButton: false,

    // 背景图片
    backgroundImage: '/images/bg.jpg',

    // 详情弹窗
    showSourceDetail: false,
    showPrizeDetail: false,
    currentSource: {},
    currentPrize: {},

    // 导航状态控制
    isNavigating: false,

    // 页面初始化状态
    pageInitialized: false
  },

  onLoad: function() {
    console.log('道具出货查询页面加载');

    // 只进行最基础的初始化，确保页面能快速显示
    this.initPageData();

    // 延迟执行非关键初始化，避免阻塞页面渲染
    setTimeout(() => {
      // 获取筛选选项
      this.getFilterOptions();

      // 初始加载数据（不消耗查询次数）
      this.loadDataWithoutCost();

      // 标记页面初始化完成
      this.setData({
        pageInitialized: true
      });
    }, 100);

    // 进一步延迟广告预加载，避免影响页面性能
    setTimeout(() => {
      this.preloadAd();
    }, 1000);
  },

  /**
   * 页面显示时触发
   */
  onShow: function() {
    console.log('道具出货查询页面显示');

    // 检查并更新免费次数
    const freeCount = this.checkAndResetDailyFreeCount();

    // 更新页面数据
    this.setData({
      freeCount: freeCount,
      showAdButton: !this.data.isVip && freeCount < FREE_QUERY_COUNT
    });

    // 更新VIP徽章显示
    const vipBadge = this.selectComponent('#vipBadge');
    if (vipBadge) {
      vipBadge.refreshFreeCount();
    }

    // 输出当前免费次数到控制台，方便调试
    console.log('当前免费查询次数:', freeCount, '是否VIP:', this.data.isVip);
  },

  /**
   * 预加载广告
   */
  preloadAd: function() {
    // 使用正确的广告单元ID
    const adUnitId = 'adunit-919b8b04a2997a01'; // 使用您提供的广告单元ID

    console.log('预加载激励视频广告，广告单元ID:', adUnitId);

    // 检查是否支持创建激励视频广告
    if (wx.createRewardedVideoAd) {
      // 创建激励视频广告实例
      if (!this.videoAd) {
        this.videoAd = wx.createRewardedVideoAd({
          adUnitId: adUnitId
        });

        // 监听加载事件
        this.videoAd.onLoad(() => {
          console.log('广告预加载成功');
          this.setData({
            adLoaded: true
          });
        });

        // 监听错误事件
        this.videoAd.onError(err => {
          console.error('广告预加载失败:', err);

          // 在开发者工具中可能无法正常加载广告，添加模拟成功的逻辑
          if (err.errCode) {
            console.log('广告错误码:', err.errCode);

            // 在开发环境中模拟广告成功
            if (err.errCode === 1004 || err.errCode === 1007) {
              console.log('开发环境模拟广告成功');

              // 延迟执行，模拟广告播放完成
              setTimeout(() => {
                // 增加免费次数，确保只增加3次
                // 先获取当前次数
                let currentFreeCount = api.getFreeCount(PAGE_KEY);
                // 直接设置为当前次数+3，避免使用updateFreeCount可能导致的问题
                const newFreeCount = currentFreeCount + AD_REWARD_COUNT;
                // 保存到缓存
                wx.setStorageSync(`freeCount_${PAGE_KEY}`, newFreeCount);

                this.setData({
                  freeCount: newFreeCount,
                  showAdButton: newFreeCount < FREE_QUERY_COUNT
                });

                // 更新VIP徽章显示
                const vipBadge = this.selectComponent('#vipBadge');
                if (vipBadge) {
                  vipBadge.refreshFreeCount();
                }

                wx.showToast({
                  title: `获得${AD_REWARD_COUNT}次免费查询`,
                  icon: 'success'
                });
              }, 1000);
            }
          }
        });

        // 监听关闭事件
        this.videoAd.onClose(res => {
          console.log('广告关闭事件:', res);

          // 用户完整观看广告
          if (res && res.isEnded) {
            console.log('用户完整观看广告，奖励免费次数');

            // 使用专门的函数增加广告奖励次数
            this.addAdRewardCount();
          } else {
            console.log('用户未完整观看广告');
            wx.showToast({
              title: '需完整观看广告才能获得奖励',
              icon: 'none'
            });
          }
        });

        // 预加载广告
        this.videoAd.load().catch(err => {
          console.error('广告预加载请求失败:', err);
        });
      }
    } else {
      console.log('当前环境不支持激励视频广告');

      // 在不支持广告的环境中，直接赠送免费次数
      wx.showToast({
        title: '当前环境不支持广告，直接赠送免费次数',
        icon: 'none',
        duration: 2000
      });

      setTimeout(() => {
        // 使用专门的函数增加广告奖励次数
        this.addAdRewardCount();
      }, 2000);
    }
  },

  /**
   * 初始化页面数据
   */
  initPageData: function() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    // 检查并重置每日免费次数
    const freeCount = this.checkAndResetDailyFreeCount();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays,
      showAdButton: !isVip && freeCount < FREE_QUERY_COUNT
    });
  },

  /**
   * 检查并重置每日免费次数
   * 每天重置为初始的免费查询次数
   * @returns {number} 当前可用的免费次数
   */
  checkAndResetDailyFreeCount: function() {
    // 获取当前日期（格式：YYYY-MM-DD）
    const today = new Date();
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    // 获取上次重置日期
    const lastResetDate = wx.getStorageSync(`${PAGE_KEY}_last_reset_date`);

    // 获取当前免费次数
    let freeCount = api.getFreeCount(PAGE_KEY);

    console.log('检查免费次数重置 - 当前日期:', dateStr, '上次重置日期:', lastResetDate, '当前免费次数:', freeCount);

    // 如果日期不同或者没有上次重置日期记录，重置免费次数
    if (dateStr !== lastResetDate) {
      console.log('重置免费次数 - 从', freeCount, '到', FREE_QUERY_COUNT);

      // 重置为每日免费查询次数
      freeCount = FREE_QUERY_COUNT;

      // 更新存储
      wx.setStorageSync(`${PAGE_KEY}_last_reset_date`, dateStr);
      wx.setStorageSync(`freeCount_${PAGE_KEY}`, freeCount);
    }

    return freeCount;
  },

  /**
   * 获取筛选选项
   */
  getFilterOptions: function() {
    // 获取来源类型选项
    this.getSourceTypes();
  },

  /**
   * 获取来源类型选项
   */
  getSourceTypes: function() {
    wx.request({
      url: `${getConfig().baseUrl}/api/treasure/prize-sources/source_types/`,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            sourceTypes: res.data.data
          });
        }
      },
      fail: (err) => {
        console.error('获取来源类型选项失败:', err);
      }
    });
  },

  /**
   * 切换标签页
   * @param {Object} e 事件对象
   */
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (tab === this.data.activeTab) {
      return;
    }

    this.setData({
      activeTab: tab,
      page: 1,
      hasMore: true,
      search: '',
      loading: false
    });

    // 加载对应标签页的数据（不消耗次数）
    this.loadDataWithoutCost(true);
  },

  /**
   * 加载数据（消耗查询次数）
   * @param {boolean} refresh 是否刷新列表
   */
  loadData: function(refresh = false) {
    console.log('加载数据，当前标签页:', this.data.activeTab);

    // 根据当前标签页加载对应的数据
    if (this.data.activeTab === 'sources') {
      this.loadSourcesList(refresh);
    } else {
      this.loadPrizesList(refresh);
    }
  },

  /**
   * 加载数据（不消耗查询次数）
   * @param {boolean} refresh 是否刷新列表
   */
  loadDataWithoutCost: function(refresh = false) {
    console.log('加载数据（不消耗次数），当前标签页:', this.data.activeTab);

    // 根据当前标签页加载对应的数据
    if (this.data.activeTab === 'sources') {
      this.loadSourcesList(refresh);
    } else {
      this.loadPrizesList(refresh);
    }
  },

  /**
   * 加载道具列表
   * @param {boolean} refresh 是否刷新列表
   */
  loadSourcesList: function(refresh = false) {
    // 如果正在加载或没有更多数据，则返回
    if (this.data.loading || (!this.data.hasMore && !refresh)) {
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true
    });

    // 如果是刷新，重置页码
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        sources: []
      });
    }

    // 显示加载提示，但不显示遮罩，让用户可以继续浏览
    if (!refresh) {
      wx.showLoading({
        title: '加载更多...',
        mask: false
      });
    }

    // 构建请求参数
    const params = {
      page: this.data.page,
      page_size: this.data.pageSize,
      include_prizes: true  // 确保返回奖品数据
    };

    // 添加筛选条件
    if (this.data.search) {
      params.search = this.data.search;
    }
    if (this.data.sourceType) {
      // 查找对应的英文值
      const sourceTypeItem = this.data.sourceTypes.find(item => item.label === this.data.sourceType);
      if (sourceTypeItem) {
        params.source_type = sourceTypeItem.label;
      } else {
        // 如果找不到对应的英文值，直接使用中文值
        params.source_type = this.data.sourceType;
      }
    }

    // 构建URL查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    // 发起请求
    const apiUrl = `${getConfig().baseUrl}/api/treasure/prize-sources/?${queryString}`;
    console.log('请求URL:', apiUrl);

    wx.request({
      url: apiUrl,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200) {
          // 直接从响应中获取结果数组，适配API返回格式
          const newSources = res.data.results || [];

          // 处理每个道具的奖品数据，为卡片显示做准备
          newSources.forEach(source => {
            // 确保prizes字段存在
            if (source.prizes && source.prizes.length > 0) {
              // 按稀有度排序：传说 > 史诗 > 稀有 > 普通
              source.prizes.sort((a, b) => {
                const rarityOrder = {
                  'legendary': 0,
                  'epic': 1,
                  'rare': 2,
                  'common': 3
                };

                // 优先显示包含"永久"字样的物品
                const aHasPermanent = a.quantity && a.quantity.includes('永久');
                const bHasPermanent = b.quantity && b.quantity.includes('永久');
                if (aHasPermanent && !bHasPermanent) return -1;
                if (!aHasPermanent && bHasPermanent) return 1;

                const orderA = rarityOrder[a.rarity] !== undefined ? rarityOrder[a.rarity] : 999;
                const orderB = rarityOrder[b.rarity] !== undefined ? rarityOrder[b.rarity] : 999;

                return orderA - orderB;
              });
            }
          });

          // 调试输出
          console.log('道具列表数据:', newSources);
          console.log('道具列表数据结构:', JSON.stringify(newSources[0] || {}, null, 2));

          // 检查奖品数据
          let hasPrizes = false;
          newSources.forEach(source => {
            if (source.prizes && source.prizes.length > 0) {
              hasPrizes = true;
              console.log(`道具 ${source.name} 包含 ${source.prizes.length} 个奖品`);
              console.log('第一个奖品:', source.prizes[0]);
            }
          });
          console.log('是否有奖品数据:', hasPrizes);

          // 更新数据
          this.setData({
            sources: refresh ? newSources : [...this.data.sources, ...newSources],
            hasMore: newSources.length === this.data.pageSize,
            page: this.data.page + 1
          });

          console.log('道具列表更新后数据:', this.data.sources.length);
        } else {
          wx.showToast({
            title: '获取道具列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取道具列表失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          loading: false
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();

        // 隐藏加载提示
        wx.hideLoading();
      }
    });
  },

  /**
   * 加载奖品列表
   * @param {boolean} refresh 是否刷新列表
   */
  loadPrizesList: function(refresh = false) {
    // 如果正在加载或没有更多数据，则返回
    if (this.data.loading || (!this.data.hasMore && !refresh)) {
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true
    });

    // 如果是刷新，重置页码
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        prizes: []
      });
    }

    // 显示加载提示，但不显示遮罩，让用户可以继续浏览
    if (!refresh) {
      wx.showLoading({
        title: '加载更多...',
        mask: false
      });
    }

    // 构建请求参数
    const params = {
      page: this.data.page,
      page_size: this.data.pageSize
    };

    // 添加筛选条件
    if (this.data.search) {
      params.search = this.data.search;
    }

    // 构建URL查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    // 发起请求
    wx.request({
      url: `${getConfig().baseUrl}/api/treasure/prizes/?${queryString}`,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200) {
          // 直接从响应中获取结果数组，适配API返回格式
          const newPrizes = res.data.results || [];

          // 处理奖品数据，确保来源信息正确显示
          newPrizes.forEach(prize => {
            // 处理来源数据
            if (prize.sources_info && prize.sources_info.length > 0) {
              // 按概率排序，从高到低
              prize.sources_info.sort((a, b) => {
                return (b.probability || 0) - (a.probability || 0);
              });

              prize.sources_info.forEach(source => {
                if (source.probability) {
                  // 将概率转换为百分比，保留2位小数
                  source.probability = parseFloat((source.probability * 100).toFixed(2));
                }
              });
            }
          });

          // 调试输出
          console.log('奖品列表数据:', newPrizes);
          console.log('奖品列表数据结构:', JSON.stringify(newPrizes[0] || {}, null, 2));

          // 检查来源数据
          let hasSources = false;
          newPrizes.forEach(prize => {
            if (prize.sources_info && prize.sources_info.length > 0) {
              hasSources = true;
              console.log(`奖品 ${prize.name} 包含 ${prize.sources_info.length} 个来源`);
              console.log('第一个来源:', prize.sources_info[0]);
            }
          });
          console.log('是否有来源数据:', hasSources);

          // 更新数据
          this.setData({
            prizes: refresh ? newPrizes : [...this.data.prizes, ...newPrizes],
            hasMore: newPrizes.length === this.data.pageSize,
            page: this.data.page + 1
          });

          console.log('奖品列表更新后数据:', this.data.prizes.length);
        } else {
          wx.showToast({
            title: '获取奖品列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取奖品列表失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          loading: false
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();

        // 隐藏加载提示
        wx.hideLoading();
      }
    });
  },

  /**
   * 格式化日期时间
   * @param {string} dateString 日期字符串
   * @returns {string} 格式化后的日期字符串
   */
  formatDateTime: function(dateString) {
    if (!dateString) return '未知';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 查看道具详情
   * @param {Object} e 事件对象
   */
  viewSourceDetail: function(e) {
    const sourceId = e.currentTarget.dataset.sourceId;



    // 显示加载状态
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 获取道具详情
    wx.request({
      url: `${getConfig().baseUrl}/api/treasure/prize-sources/${sourceId}/`,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200) {
          // 处理奖品数据，添加概率百分比显示
          const sourceDetail = res.data;

          // 格式化更新时间
          if (sourceDetail.updated_at) {
            sourceDetail.formatted_updated_at = this.formatDateTime(sourceDetail.updated_at);
          }

          // 处理奖品数据
          if (sourceDetail.prizes && sourceDetail.prizes.length > 0) {
            // 按永久 > 稀有度排序：永久 > 传说 > 史诗 > 稀有 > 普通
            sourceDetail.prizes.sort((a, b) => {
              const rarityOrder = {
                'legendary': 0,
                'epic': 1,
                'rare': 2,
                'common': 3
              };

              // 优先显示包含"永久"字样的物品
              const aHasPermanent = a.quantity && a.quantity.includes('永久');
              const bHasPermanent = b.quantity && b.quantity.includes('永久');
              if (aHasPermanent && !bHasPermanent) return -1;
              if (!aHasPermanent && bHasPermanent) return 1;

              // 然后按稀有度排序
              const orderA = rarityOrder[a.rarity] !== undefined ? rarityOrder[a.rarity] : 999;
              const orderB = rarityOrder[b.rarity] !== undefined ? rarityOrder[b.rarity] : 999;

              return orderA - orderB;
            });

            // 处理概率显示
            sourceDetail.prizes.forEach(prize => {
              if (prize.probability) {
                // 将概率转换为百分比，保留4位小数
                prize.probability = parseFloat((prize.probability * 100).toFixed(4));
              }
            });
          }

          // 更新数据并显示弹窗
          this.setData({
            currentSource: sourceDetail,
            showSourceDetail: true
          });
        } else {
          wx.showToast({
            title: '获取道具详情失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取道具详情失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 关闭道具详情弹窗
   */
  closeSourceDetail: function() {
    this.setData({
      showSourceDetail: false
    });
  },

  /**
   * 查看奖品详情
   * @param {Object} e 事件对象
   */
  viewPrizeDetail: function(e) {
    const prizeId = e.currentTarget.dataset.prizeId;

    // 显示加载状态
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 获取奖品详情
    wx.request({
      url: `${getConfig().baseUrl}/api/treasure/prizes/${prizeId}/`,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200) {
          // 处理来源数据，添加概率百分比显示
          const prizeDetail = res.data;

          // 格式化更新时间
          if (prizeDetail.updated_at) {
            prizeDetail.formatted_updated_at = this.formatDateTime(prizeDetail.updated_at);
          }

          // 处理来源数据
          if (prizeDetail.sources_info && prizeDetail.sources_info.length > 0) {
            // 按概率排序，从高到低
            prizeDetail.sources_info.sort((a, b) => {
              return (b.probability || 0) - (a.probability || 0);
            });

            prizeDetail.sources_info.forEach(source => {
              if (source.probability) {
                // 将概率转换为百分比，保留2位小数
                source.probability = parseFloat((source.probability * 100).toFixed(2));
              }
            });
          }

          // 更新数据并显示弹窗
          this.setData({
            currentPrize: prizeDetail,
            showPrizeDetail: true
          });
        } else {
          wx.showToast({
            title: '获取奖品详情失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取奖品详情失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 关闭奖品详情弹窗
   */
  closePrizeDetail: function() {
    this.setData({
      showPrizeDetail: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function(e) {
    // 阻止事件冒泡
    if (e) {
      e.stopPropagation();
    }
    return false;
  },

  /**
   * 阻止滑动穿透
   */
  preventTouchMove: function(e) {
    // 阻止事件传播和默认行为
    if (e && e.preventDefault) {
      e.preventDefault();
    }
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    return false;
  },

  /**
   * 处理弹窗内容区域的滚动
   * 允许弹窗内容滚动，但阻止事件冒泡
   */
  modalBodyTouchMove: function(e) {
    // 阻止事件冒泡，但不阻止默认行为
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    // 不返回false，允许滚动继续
  },

  /**
   * 检查是否可以查询
   * @returns {boolean} 是否可以查询
   */
  canQuery: function() {
    // 重新从缓存获取最新的免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);

    // 如果缓存中的免费次数与页面中的不一致，更新页面数据
    if (freeCount !== this.data.freeCount) {
      console.log('更新免费次数 - 从缓存:', freeCount, '页面中:', this.data.freeCount);
      this.setData({
        freeCount: freeCount,
        showAdButton: !this.data.isVip && freeCount < FREE_QUERY_COUNT
      });

      // 更新VIP徽章显示
      const vipBadge = this.selectComponent('#vipBadge');
      if (vipBadge) {
        vipBadge.refreshFreeCount();
      }
    }

    // VIP用户不受限制
    if (this.data.isVip) {
      return true;
    }

    // 非VIP用户，检查免费次数是否足够
    if (freeCount <= 0) {
      // 显示提示
      wx.showModal({
        title: '免费次数已用完',
        content: '您今日的免费查询次数已用完，可以观看广告获取更多次数，或开通VIP无限查询',
        confirmText: '看广告',
        cancelText: '开通VIP',
        success: (res) => {
          if (res.confirm) {
            // 用户点击了"看广告"
            this.watchAdForFreeCount();
          } else {
            // 用户点击了"开通VIP"
            this.setData({
              showVipDialog: true
            });
          }
        }
      });
      return false;
    }

    return true;
  },

  /**
   * 观看广告获取免费次数
   */
  watchAdForFreeCount: function() {
    console.log('观看广告获取免费次数');

    // 如果没有创建广告实例，先创建
    if (!this.videoAd && wx.createRewardedVideoAd) {
      this.preloadAd();
    }

    // 如果广告实例存在，显示广告
    if (this.videoAd) {
      wx.showLoading({
        title: '广告加载中...',
        mask: true
      });

      // 显示广告
      this.videoAd.show()
        .then(() => {
          console.log('广告显示成功');
          wx.hideLoading();
        })
        .catch(err => {
          console.error('激励视频广告显示失败:', err);
          wx.hideLoading();

          // 失败重试
          wx.showLoading({
            title: '重新加载广告...',
            mask: true
          });

          this.videoAd.load()
            .then(() => {
              console.log('广告重新加载成功');
              return this.videoAd.show();
            })
            .then(() => {
              console.log('广告重新显示成功');
              wx.hideLoading();
            })
            .catch(loadErr => {
              console.error('广告重新加载/显示失败:', loadErr);
              wx.hideLoading();

              // 微信小程序中直接模拟广告成功
              console.log('模拟广告成功');

              // 延迟执行，模拟广告播放完成
              setTimeout(() => {
                // 使用专门的函数增加广告奖励次数
                this.addAdRewardCount();
              }, 1000);
            });
        });
    } else {
      console.log('当前环境不支持激励视频广告');

      // 在不支持广告的环境中，直接赠送免费次数
      wx.showToast({
        title: '当前环境不支持广告，直接赠送免费次数',
        icon: 'none',
        duration: 2000
      });

      setTimeout(() => {
        // 使用专门的函数增加广告奖励次数
        this.addAdRewardCount();
      }, 2000);
    }
  },

  /**
   * 搜索输入
   * @param {Object} e 事件对象
   */
  onSearchInput: function(e) {
    this.setData({
      search: e.detail.value
    });
  },

  /**
   * 搜索道具
   */
  onSearch: function() {
    // 检查是否可以查询
    if (!this.canQuery()) {
      return;
    }

    // 非VIP用户消耗免费次数
    if (!this.data.isVip) {
      console.log('消耗免费次数 - 搜索查询');
      const newFreeCount = api.updateFreeCount(PAGE_KEY, -1);
      this.setData({
        freeCount: newFreeCount,
        showAdButton: newFreeCount < FREE_QUERY_COUNT
      });

      // 更新VIP徽章显示
      const vipBadge = this.selectComponent('#vipBadge');
      if (vipBadge) {
        vipBadge.refreshFreeCount();
      }
    }

    // 加载数据
    this.loadData(true);
  },

  /**
   * 筛选条件变更
   * @param {Object} e 事件对象
   */
  onFilterChange: function(e) {
    const { type, value } = e.currentTarget.dataset;

    this.setData({
      [type]: value
    });

    // 不消耗次数，直接加载数据
    this.loadDataWithoutCost(true);
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function(e) {
    // 阻止事件冒泡
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    } else {
      console.log('无法阻止事件冒泡：事件对象不包含stopPropagation方法');
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadDataWithoutCost(true);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadDataWithoutCost();
    }
  },

  /**
   * 页面滚动时触发
   */
  onPageScroll: function() {
    // 获取页面高度
    const query = wx.createSelectorQuery();
    query.selectViewport().scrollOffset();
    query.exec((res) => {
      if (res && res[0]) {
        const scrollTop = res[0].scrollTop;
        const windowHeight = wx.getSystemInfoSync().windowHeight;

        // 当滚动到距离底部150px时，加载更多数据
        if (scrollTop + windowHeight + 150 >= res[0].scrollHeight) {
          if (this.data.hasMore && !this.data.loading) {
            this.loadDataWithoutCost();
          }
        }
      }
    });
  },

  /**
   * 返回上一页（优化版本）
   */
  goBack: function() {
    // 防止频繁点击
    if (this.data.isNavigating) {
      return;
    }

    this.setData({
      isNavigating: true
    });

    // 使用无动画返回，提升性能
    wx.navigateBack({
      delta: 1,
      animationType: 'none',
      success: () => {
        console.log('返回上一页成功');
      },
      fail: (err) => {
        console.error('返回上一页失败', err);
        // 重置状态
        this.setData({
          isNavigating: false
        });
      }
    });
  },

  /**
   * 增加广告奖励次数
   * 这个函数专门用于增加广告奖励的次数，确保只增加指定次数
   */
  addAdRewardCount: function() {
    console.log('增加广告奖励次数');

    // 直接从缓存中获取当前次数
    const key = `freeCount_${PAGE_KEY}`;
    let currentFreeCount = wx.getStorageSync(key) || 0;

    // 在奖品查询页面，如果当前次数是100，说明是错误的初始值，我们重置为0
    // 因为奖品查询页面的初始值应该是3，不是100
    if (currentFreeCount === 100 && PAGE_KEY === 'prize-query') {
      currentFreeCount = 0;
    }

    // 根据页面标识设置不同的广告奖励次数
    let rewardCount = AD_REWARD_COUNT; // 默认使用页面常量

    // 计算新的免费次数
    let newFreeCount;

    // 在奖品查询页面
    if (PAGE_KEY === 'prize-query') {
      // 如果当前次数为0，重置为3次；否则累加3次
      if (currentFreeCount === 0) {
        newFreeCount = rewardCount; // 重置为3次
        console.log(`重置广告奖励次数: ${currentFreeCount} -> ${newFreeCount} (重置为${rewardCount}次)`);
      } else {
        newFreeCount = currentFreeCount + rewardCount; // 累加3次
        console.log(`增加广告奖励次数: ${currentFreeCount} -> ${newFreeCount} (奖励${rewardCount}次)`);
      }
    } else {
      // 其他页面始终累加
      newFreeCount = currentFreeCount + rewardCount;
      console.log(`增加广告奖励次数: ${currentFreeCount} -> ${newFreeCount} (奖励${rewardCount}次)`);
    }

    // 直接保存到缓存
    wx.setStorageSync(key, newFreeCount);

    // 更新页面数据
    this.setData({
      freeCount: newFreeCount,
      showAdButton: newFreeCount < FREE_QUERY_COUNT
    });

    // 更新VIP徽章显示
    const vipBadge = this.selectComponent('#vipBadge');
    if (vipBadge) {
      vipBadge.refreshFreeCount();
    }

    // 显示提示
    wx.showToast({
      title: `获得${rewardCount}次免费查询`,
      icon: 'success'
    });
  },

  /**
   * 页面卸载
   */
  onUnload: function() {
    // 页面卸载时的清理工作
    // 注意：不要在这里销毁广告实例，避免"video-ad has been destroyed"错误
    // 只需要将引用设为null
    this.videoAd = null;
  },

  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    // 显示VIP对话框
    this.setData({
      showVipDialog: true
    });
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({
      showVipDialog: false
    });
  },

  /**
   * 购买VIP事件
   * @param {Object} e 事件对象
   */
  onBuyVip: function(e) {
    console.log('购买VIP:', e.detail);

    // 更新VIP状态
    this.setData({
      isVip: true,
      showVipDialog: false
    });

    // 显示购买成功提示
    wx.showToast({
      title: '开通VIP成功',
      icon: 'success'
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function() {
    return {
      title: 'QQ飞车道具出货查询 - 全面了解游戏道具',
      path: '/pages/prize/prize-list/prize-list'
    };
  }
});
