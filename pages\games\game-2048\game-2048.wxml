<!--pages/games/game-2048/game-2048.wxml-->
<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 游戏标题 -->
  <view class="header">
    <view class="title">2048</view>
    <view class="subtitle">经典数字方块游戏</view>

    <!-- 使用统一的VIP徽章组件 -->
    <view class="vip-badge-container">
      <vip-badge id="vipBadge"
        pageType="common"
        isVip="{{isVip}}"
        freeCount="{{freeCount}}"
        remainingDays="{{vipRemainingDays}}"
        bindtap="onVipBadgeTap"
      />
    </view>
  </view>

  <!-- 游戏信息区域 -->
  <view class="game-info">
    <view class="score-container">
      <view class="score-label">得分</view>
      <view class="score-value">{{score}}</view>
    </view>
    <view class="best-container">
      <view class="score-label">最高分</view>
      <view class="score-value">{{bestScore}}</view>
    </view>
  </view>

  <!-- 连击和最终得分信息 -->
  <view class="bonus-info" wx:if="{{comboCount > 0}}">
    <view class="bonus-item" wx:if="{{comboCount > 0}}">
      <view class="bonus-label">连击</view>
      <view class="bonus-value">{{comboCount}}次 (×{{comboMultiplier}})</view>
    </view>
    <view class="bonus-item" wx:if="{{finalScore > 0 && finalScore != score}}">
      <view class="bonus-label">最终得分</view>
      <view class="bonus-value">{{finalScore}}</view>
    </view>
  </view>

  <!-- 游戏操作区域 -->
  <view class="game-controls">
    <view class="control-button" catchtap="restartGame">重新开始</view>
    <view class="control-button" catchtap="undoMove" wx:if="{{isVip && canUndo}}">撤销 ({{SCORE_CONFIG.VIP_UNDO_LIMIT - undoCount}})</view>
    <view class="control-button" catchtap="toggleSound">{{soundEnabled ? '关闭音效' : '开启音效'}}</view>
    <view class="control-button" catchtap="goToRanking">排行榜</view>
  </view>

  <!-- 游戏网格 -->
  <view class="game-container"
        bindtouchstart="onTouchStart"
        bindtouchend="onTouchEnd">
    <view class="game-grid">
      <!-- 背景网格 -->
      <view class="grid-background">
        <view class="grid-cell" wx:for="{{16}}" wx:key="index"></view>
      </view>

      <!-- 游戏方块 -->
      <view class="grid-tiles">
        <view wx:for="{{tiles}}" wx:key="id"
              class="tile tile-{{item.value}} {{item.isNew ? 'tile-new' : ''}} {{item.isMerged ? 'tile-merged' : ''}}"
              style="grid-column: {{item.x + 1}}; grid-row: {{item.y + 1}};">
          <view class="tile-inner">
            <image class="tile-image" src="/images/2048/{{item.value}}.png" mode="aspectFill"></image>
            <text class="tile-value">{{item.value}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏结束提示 -->
  <view class="game-over {{gameOver ? 'show' : ''}}">
    <view class="game-over-message" catchtap="stopPropagation">
      <view class="game-over-title">{{gameWon ? '恭喜!' : '游戏结束'}}</view>
      <view class="game-over-subtitle">{{gameWon ? '你达到了2048!' : '无法继续移动'}}</view>

      <!-- 游戏统计信息 -->
      <view class="game-over-stats">
        <view class="game-over-stat-item">
          <view class="game-over-stat-label">基础得分</view>
          <view class="game-over-stat-value">{{baseScore}}</view>
        </view>

        <view class="game-over-stat-item" wx:if="{{comboCount > 0}}">
          <view class="game-over-stat-label">连击奖励</view>
          <view class="game-over-stat-value">×{{comboMultiplier}}</view>
        </view>



        <view class="game-over-stat-item">
          <view class="game-over-stat-label">游戏时间</view>
          <view class="game-over-stat-value">{{Math.floor(gameTime / 60)}}分{{gameTime % 60}}秒</view>
        </view>

        <view class="game-over-stat-item">
          <view class="game-over-stat-label">移动次数</view>
          <view class="game-over-stat-value">{{moves}}次</view>
        </view>
      </view>

      <view class="game-over-score">最终得分: {{finalScore || score}}</view>
      <view class="game-over-button" catchtap="restartGame">点击重新开始</view>
      <view class="game-over-button" catchtap="uploadScore" wx:if="{{gameOver && !hasUploaded}}">上传分数</view>
      <view class="game-over-button" catchtap="watchAdForFreeCount" wx:if="{{gameOver && !isVip}}">看广告获得免费次数</view>
    </view>
  </view>

  <!-- 底部区域 -->
  <view class="footer">
    <!-- 免责声明 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 仅供娱乐，非官方工具</view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" bindtap="goBack">
      返回上一页
    </view>
  </view>
</view>

<!-- VIP对话框组件 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="game-2048"
  simpleMode="{{true}}"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bindclose="onVipDialogClose"
  bindbuy="onBuyVip">
</vip-dialog>

<!-- 用户信息输入弹窗 -->
<view class="modal-container {{showUserInfoModal ? 'show' : ''}}">
  <view class="modal-mask" catchtouchmove="preventTouchMove" bindtap="closeUserInfoModal"></view>
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">输入昵称</text>
      <view class="close-button" bindtap="closeUserInfoModal">×</view>
    </view>
    <view class="modal-body">
      <view class="input-group">
        <text class="input-label">昵称</text>
        <input class="input-field" type="text" value="{{nickName}}" bindinput="onNickNameInput" placeholder="请输入昵称" maxlength="20" />
      </view>
      <view class="modal-tip">推荐使用微信昵称</view>
    </view>
    <view class="modal-footer">
      <view class="modal-button cancel" bindtap="closeUserInfoModal">取消</view>
      <view class="modal-button confirm" bindtap="confirmUpload">确定</view>
    </view>
  </view>
</view>
