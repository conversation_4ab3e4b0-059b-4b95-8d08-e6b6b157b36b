<view class="vip-badge" bindtap="handleTap">
  <view class="vip-badge-content {{isVip ? 'vip' : ''}} {{vipInfoFailed ? 'failed' : ''}}">
    <view class="badge-icon">
      <image src="/images/vip-icon.png" wx:if="{{isVip}}"></image>
      <image src="/images/count-icon.png" wx:else></image>
    </view>
    <view class="badge-text">
      <!-- VIP用户显示逻辑：任何页面都显示VIP会员和剩余天数 -->
      <block wx:if="{{isVip}}">
        <text class="vip-text">VIP会员</text>
        <text class="sub-text">剩余{{remainingDays}}天</text>
      </block>
      
      <!-- 非VIP用户显示逻辑：根据页面类型显示不同内容 -->
      <block wx:else>
        <!-- 通用页面显示"开通VIP"字样 -->
        <block wx:if="{{pageType === 'common'}}">
          <text class="count-text">开通VIP</text>
          <text class="sub-text">{{vipInfoFailed ? '点击刷新' : '畅享特权'}}</text>
        </block>
        
        <!-- 抽奖页面显示剩余次数 -->
        <block wx:elif="{{pageType === 'lottery'}}">
          <text class="count-text">剩余次数</text>
          <text class="sub-text">{{vipInfoFailed ? '点击刷新' : freeCount+'次'}}</text>
        </block>
      </block>
    </view>
  </view>
</view> 