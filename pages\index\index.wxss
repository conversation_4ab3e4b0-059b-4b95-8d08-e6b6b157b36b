/* 容器背景样式 */
.container {
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + var(--safe-bottom));
  min-height: 100vh;
  position: relative;
}

/* 背景图片样式 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.8;
}

/* 搜索栏样式 */
.search-bar {
  position: sticky;
  top: calc(var(--safe-top));
  padding-top: calc(10rpx + var(--safe-top));
  z-index: 100;
}

/* 搜索历史样式优化 */
.search-history {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  z-index: 1000;
  opacity: 0;
  transform: translateY(-20rpx);
  pointer-events: none;
  transition: all 0.3s ease;
  margin-top: 4rpx;
}

.search-history.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  margin-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.history-title {
  font-size: 26rpx;
  color: #999;
}

.clear-history {
  font-size: 26rpx;
  color: #4a90e2;
  padding: 4rpx 12rpx;
}

.clear-history:active {
  opacity: 0.8;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.history-item {
  background: #f5f7fa;
  padding: 12rpx 24rpx;
  border-radius: 28rpx;
  font-size: 26rpx;
  color: #333;
  transition: all 0.2s ease;
}

.history-item:active {
  background: #e8edf5;
  transform: scale(0.98);
}

.history-item text {
  font-size: 26rpx;
  color: #333;
}

/* 修改搜索栏样式以适应历史记录 */
.search-bar {
  position: relative;
  z-index: 1000;
}

.search-input-wrap {
  position: relative;
  z-index: 1001;
  background: rgba(255, 255, 255, 0.95);
}

.search-input-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background: rgba(235, 242, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 80rpx;
  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.05);
  max-width: 100%;
  margin: 0 auto;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input-wrap input {
  flex: 1;
  height: 100%;
  color: #333;
  font-size: 28rpx;
  min-width: 200rpx;
}

.placeholder {
  color: #999;
}

.search-btn {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.search-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #357abd, #2868a9);
}

.custom-picker {
  margin: 0 16rpx;
}

.level-picker {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.5);
  height: 60rpx;
  padding: 0 20rpx;
  border-radius: 8rpx;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s;
}

.level-picker.active {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 12rpx;
  opacity: 0.6;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}

/* 赛车列表样式 */
.car-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 保持16rpx的间距 */
  padding: 8rpx 0; /* 调整为8rpx，让边缘更紧凑 */
}

/* 赛车卡片样式 */
.car-card {
  background: rgba(235, 242, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.05);
  display: flex;
  height: 180rpx;
  transition: all 0.2s ease-out;
  transform: translateZ(0);
  align-items: center;
}

.car-image-container {
  width: 220rpx;
  height: 160rpx;
  position: relative;
  background: rgba(248, 248, 248, 0.5);
  margin: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}

.car-info {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

.car-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 8rpx;
  gap: 8rpx;
}

.car-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 16rpx;
}

/* 添加悬挂标签样式 */
.suspension-tag {
  padding: 4rpx 12rpx;
  color: #fff;
  border-radius: 16rpx;
  font-size: 24rpx;
  flex-shrink: 0;
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.2);
}

.car-level {
  padding: 4rpx 12rpx;
  color: #fff;
  border-radius: 16rpx;
  font-size: 24rpx;
  flex-shrink: 0;
  background: rgba(144, 238, 144, 0.9);
}

/* 详情布局 */
.car-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 8rpx 16rpx;
  padding: 8rpx 0;
}

.car-details .detail-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  min-width: 0;
}

.car-details .detail-item .label {
  color: #999;
  min-width: 70rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.car-details .detail-item .value {
  color: #333;
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 等级标签样式 */
.car-level[data-level^="S"] {
  background: rgba(147, 112, 219, 0.9);
}

.car-level[data-level^="A"] {
  background: rgba(255, 165, 0, 0.9);
}

.car-level[data-level^="B"] {
  background: rgba(102, 187, 106, 0.9);
}

.car-level[data-level^="C"] {
  background: rgba(30, 144, 255, 0.9);
}

.car-level[data-level^="D"] {
  background: rgba(169, 169, 169, 0.9);
}

.car-level[data-level^="T"] {
  background: rgba(255, 140, 0, 0.9);
}

/* 确保图片加载状态的样式 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(245, 247, 250, 0.8);
  transition: opacity 0.3s;
}

.image-loading.hide {
  opacity: 0;
  pointer-events: none;
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 320px) {
  .container {
    padding: 16rpx 20rpx;
  }

  .car-card {
    height: 160rpx;
  }

  .car-image-container {
    width: 180rpx;
    height: 140rpx;
  }

  .car-name {
    font-size: 24rpx;
  }

  .car-level {
    font-size: 22rpx;
    padding: 2rpx 10rpx;
  }

  .detail-item {
    font-size: 20rpx;
  }

  .detail-item .label {
    min-width: 60rpx;
  }

  /* 对比表格适配 */
  .row-label {
    width: 140rpx;
    font-size: 22rpx;
    padding: 0 16rpx;
  }

  .row-values .value {
    font-size: 22rpx;
    padding: 8rpx;
  }

  .group-title {
    font-size: 24rpx;
    padding: 16rpx 20rpx;
  }
}

@media screen and (min-width: 321px) and (max-width: 375px) {
  .car-card {
    height: 170rpx;
  }

  .car-image-container {
    width: 200rpx;
    height: 150rpx;
  }

  .detail-item {
    font-size: 22rpx;
  }

  .row-label {
    width: 160rpx;
    font-size: 24rpx;
  }

  .row-values .value {
    font-size: 24rpx;
  }
}

@media screen and (min-width: 768px) {
  .container {
    padding: 30rpx 40rpx;
  }

  .car-card {
    height: 200rpx;
  }

  .car-image-container {
    width: 260rpx;
    height: 180rpx;
  }

  .car-name {
    font-size: 32rpx;
  }

  .car-level {
    font-size: 26rpx;
  }

  .detail-item {
    font-size: 26rpx;
  }

  .compare-dialog {
    max-width: 1200rpx;
  }

  .row-label {
    width: 200rpx;
    font-size: 28rpx;
  }

  .row-values .value {
    font-size: 28rpx;
    padding: 16rpx;
  }

  .group-title {
    font-size: 32rpx;
    padding: 24rpx 28rpx;
  }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) {
  .compare-dialog {
    width: 85%;
    max-height: 90vh;
  }

  .car-info-bar {
    max-height: 180rpx;
  }

  .compare-content {
    padding: 20rpx;
  }
}

/* 底部安全区域适配 */
.compare-bar {
  padding-bottom: calc(20rpx + var(--safe-bottom));
}

.options-content {
  padding-bottom: calc(32rpx + var(--safe-bottom));
}

/* 刘海屏适配 */
.compare-header {
  padding-top: max(20rpx, var(--safe-top));
}

/* 折叠屏适配 */
@media screen and (min-width: 1024px) {
  .container {
    max-width: 1200rpx;
    margin: 0 auto;
  }

  .compare-dialog {
    max-width: 1400rpx;
  }
}

/* 修改展开遮罩层样式 */
.expanded-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s ease;
  will-change: opacity;
  display: flex;
  align-items: flex-end; /* 确保卡片从底部弹出 */
}

.expanded-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 修改展开卡片样式 */
.expanded-card {
  position: relative;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  height: 85vh; /* 固定高度 */
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  will-change: transform;
  /* 使用合成层 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
}

.expanded-card.show {
  transform: translateY(0);
}

/* 修改图片容器样式 */
.expanded-image-container {
  height: 320rpx;
  width: 100%;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 修改展开图片样式 */
.expanded-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 修改内容区域滚动视图 */
.expanded-card scroll-view {
  flex: 1;
  height: calc(100% - 320rpx); /* 调整高度计算 */
  overflow-y: auto;
}

.expanded-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 1;
  padding: 0;
  box-sizing: border-box;
  background: inherit;
  border-radius: 0 0 24rpx 24rpx;
  /* 添加硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 优化滚动性能 */
  will-change: transform;
  /* 使用合成层 */
  backface-visibility: hidden;
}

/* 修改滚动条样式 */
.expanded-content::-webkit-scrollbar {
  width: 6rpx;
  background: transparent;
}

.expanded-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
}

.expanded-content::-webkit-scrollbar-track {
  background: transparent;
}

.expanded-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  font-size: 28rpx;
  background: rgba(230, 238, 250, 0.8);
  padding: 24rpx;
  border-radius: 12rpx;
  margin-top: 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.expanded-header {
  margin: 0 24rpx;
  padding: 32rpx;  /* 增加内边距 */
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expanded-name {
  font-size: 40rpx;  /* 增大标题字号 */
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 24rpx;  /* 增加右边距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expanded-tags {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
  margin-left: 16rpx;
}

/* 展开卡片的悬挂标签样式 */
.expanded-suspension-tag {
  padding: 8rpx 16rpx;
  color: #fff;
  border-radius: 20rpx;
  font-size: 26rpx;
  flex-shrink: 0;
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.2);
  white-space: nowrap;
}

.expanded-level {
  padding: 8rpx 16rpx;
  color: #fff;
  border-radius: 20rpx;
  font-size: 26rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background: rgba(144, 238, 144, 0.9);
  flex-shrink: 0;
}

/* 展开卡片等级标签样式 */
.expanded-level[data-level^="S"] {
  background: rgba(147, 112, 219, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(147, 112, 219, 0.2);
}

.expanded-level[data-level^="A"] {
  background: rgba(255, 165, 0, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(255, 165, 0, 0.2);
}

.expanded-level[data-level^="B"] {
  background: rgba(102, 187, 106, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(102, 187, 106, 0.2);
}

.expanded-level[data-level^="C"] {
  background: rgba(30, 144, 255, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(30, 144, 255, 0.2);
}

.expanded-level[data-level^="D"] {
  background: rgba(169, 169, 169, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(169, 169, 169, 0.2);
}

.expanded-level[data-level^="T"] {
  background: rgba(255, 140, 0, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(255, 140, 0, 0.2);
}

.expanded-details .detail-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  font-size: 30rpx;
}

.expanded-details .detail-item .label {
  width: 180rpx;
  color: #666;
  font-size: 30rpx;
  margin-right: 24rpx;
}

.expanded-details .detail-item .value {
  flex: 1;
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}

/* 底部加载提示 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx;
}

.thanks-text {
  text-align: center;
  color: #666;
  font-size: 24rpx;
  margin-top: 24rpx;
  padding-top: 24rpx;
  opacity: 0.8;
  border-top: 2rpx solid rgba(0, 0, 0, 0.05);
}

/* 修改反馈按钮样式 */
.feedback-btn {
  position: fixed;
  right: 32rpx;
  bottom: 240rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s ease;
  touch-action: none;
  will-change: transform;
}

.feedback-btn.dragging {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.2s ease;
}

.feedback-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.feedback-btn text {
  color: #666;
  font-size: 12px;
  line-height: 1;
}

/* 拖动遮罩层样式 */
.drag-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 998;
  background: transparent;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drag-mask.show {
  visibility: visible;
  opacity: 1;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: 180rpx; /* 在反馈按钮上方 */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 16rpx 20rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  transform: translateX(200%);
  transition: all 0.3s ease;
  z-index: 100;
}

.back-to-top.show {
  transform: translateX(0);
}

.back-to-top-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
  font-weight: bold;
}

/* 对比模式开关样式 */
.compare-mode {
  display: flex;
  align-items: center;
  padding: 0 12rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.compare-mode text {
  font-size: 28rpx;
  color: #333;
  padding: 0 8rpx;
  white-space: nowrap;
}

/* 优化开关组件样式 */
.mode-switch {
  transform: scale(0.8);
  margin: 0 -8rpx 0 4rpx;
}

/* 卡片选择框样式 */
.car-checkbox {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
  z-index: 10;
}

.car-checkbox.show {
  opacity: 1;
  transform: scale(1);
}

/* 底部对比栏样式 */
.compare-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.compare-bar.show {
  transform: translateY(0);
}

.selected-cars {
  display: flex;
  gap: 20rpx;
  padding: 10rpx 0;
}

.selected-car {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
}

.selected-car image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.remove-car {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.compare-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
}

.selected-count {
  font-size: 26rpx;
  color: #666;
}

.compare-btn {
  background: #ccc;
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.compare-btn.active {
  background: #4a90e2;
}

.clear-btn {
  font-size: 26rpx;
  color: #999;
  padding: 10rpx 20rpx;
}

/* 修改对比弹窗样式 */
.compare-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(4px);
}

.compare-overlay.show {
  opacity: 1;
  visibility: visible;
}

.compare-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 92%;
  max-width: 960rpx;
  max-height: 85vh;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
  border-radius: 24rpx;
  overflow: hidden;
  opacity: 0;
  transform: translate(-50%, -45%) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
}

.compare-overlay.show .compare-dialog {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* 标题栏样式 */
.compare-header {
  flex-shrink: 0;
  padding: 24rpx 30rpx; /* 增加内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.compare-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5rpx;
  padding-left: 8rpx; /* 添加左内边距 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24rpx; /* 增加按钮间距 */
}

.share-btn,
.gen-image-btn {
  border: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
  width: auto !important; /* 自适应宽度 */
  height: auto !important; /* 自适应高度 */
  min-width: 0 !important; /* 重要：移除微信小程序默认的最小宽度 */
  min-height: 0 !important; /* 重要：移除微信小程序默认的最小高度 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.share-btn image,
.gen-image-btn image {
  width: 36rpx !important; /* 统一图标大小 */
  height: 36rpx !important; /* 统一图标大小 */
  display: block !important; /* 确保图片正确显示 */
}

.gen-image-btn::after,
.share-btn::after {
  display: none !important; /* 移除默认按钮边框 */
  border: none !important;
}

.close-btn {
  font-size: 40rpx; /* 调整关闭按钮大小 */
  color: #999;
  padding: 0 !important;
  margin: 0 !important;
  width: 36rpx !important; /* 调整为与图标相同的尺寸 */
  height: 36rpx !important; /* 调整为与图标相同的尺寸 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important; /* 重要：确保X居中 */
}

.close-btn:active {
  opacity: 0.7;
}

/* 车辆信息栏样式 */
.car-info-bar {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  max-height: 240rpx;
  background: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 添加内容容器 */
.car-info-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.car-brief {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  width: calc((100% - 48rpx) / 3);
  min-width: 0;
  box-sizing: border-box;
}

.car-brief:not(:last-child) {
  margin-right: 24rpx;
}

.car-brief image {
  width: 100%;
  height: 120rpx;
  object-fit: contain;
  border-radius: 8rpx;
  background: #fff;
  padding: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.car-brief .car-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.car-brief .data-type {
  font-size: 20rpx;
  color: #666;
  background-color: rgba(74, 144, 226, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-top: 4rpx;
  text-align: center;
}

/* 修改小屏幕适配 */
@media screen and (max-width: 320px) {
  .car-brief {
    padding: 8rpx;
  }

  .car-brief image {
    width: 160rpx;
    height: 100rpx;
  }

  .car-brief .car-name {
    font-size: 22rpx;
  }
}

/* 修改中等屏幕适配 */
@media screen and (min-width: 321px) and (max-width: 375px) {
  .car-brief {
    padding: 10rpx;
  }

  .car-brief image {
    width: 170rpx;
    height: 110rpx;
  }
}

/* 修改横屏模式适配 */
@media screen and (orientation: landscape) {
  .car-info-bar {
    max-height: 200rpx;
  }

  .car-brief {
    padding: 8rpx;
  }

  .car-brief image {
    width: 160rpx;
    height: 100rpx;
  }
}

/* 添加滚动指示样式 */
.car-info-bar::-webkit-scrollbar {
  display: none;
}

/* 添加渐变遮罩提示可以滚动 */
.car-info-bar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 40rpx;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.9));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

.car-info-bar.can-scroll::after {
  opacity: 1;
}

/* 修改对比内容样式 */
.compare-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 24rpx;
  padding-bottom: 40rpx; /* 增加底部内边距 */
  -webkit-overflow-scrolling: touch;
  width: 100%;
  box-sizing: border-box;
  background: #f8f9fa;
}

.compare-group {
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.compare-group:last-child {
  margin-bottom: 16rpx; /* 最后一个分组添加底部间距 */
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.group-title::before {
  content: '';
  width: 6rpx;
  height: 24rpx;
  background: #4a90e2;
  margin-right: 12rpx;
  border-radius: 3rpx;
}

/* 表格样式优化 */
.compare-table {
  width: 100%;
  border-collapse: collapse;
  box-sizing: border-box;
}

.compare-row {
  display: flex;
  align-items: center;
  min-height: 88rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: 12rpx 0;
  transition: background-color 0.2s ease;
}

.compare-row:last-child {
  border-bottom: none;
}

.compare-row:hover {
  background-color: rgba(74, 144, 226, 0.05);
}

.row-label {
  width: 180rpx;
  font-size: 26rpx;
  color: #666;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  position: relative;
}

.row-label::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.05);
}

.row-values {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 12rpx;
}

.row-values .value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  padding: 12rpx;
  position: relative;
  font-weight: 500;
  transition: all 0.2s ease;
}

.row-values .value:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.05);
}

/* 高亮值样式优化 */
.value.highlight-higher {
  color: #ff4d4f !important;
  font-weight: 600;
  background: rgba(255, 77, 79, 0.05);
  border-radius: 4rpx;
}

.value.highlight-lower {
  color: #52c41a !important;
  font-weight: 600;
  background: rgba(82, 196, 26, 0.05);
  border-radius: 4rpx;
}

/* 优化滚动条样式 */
.compare-content::-webkit-scrollbar {
  width: 6rpx;
}

.compare-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
}

.compare-content::-webkit-scrollbar-track {
  background: transparent;
}

/* 分享选项弹窗样式优化 */
.share-options {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.share-options.show {
  opacity: 1;
  visibility: visible;
}

.options-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.share-options.show .options-content {
  transform: translateY(0);
}

.option-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  margin-bottom: 16rpx;
  border: none;
  background-color: #fff;
  border-radius: 12rpx;
}

.option-btn:last-child {
  margin-bottom: 0;
}

.option-btn image {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.option-btn text {
  font-size: 32rpx;
  color: #333;
}

.highlight-higher {
  color: #f44336 !important;
  font-weight: 600 !important;
}

.highlight-lower {
  color: #4caf50 !important;
  font-weight: 600 !important;
}

/* 用户信息栏样式 */
.user-info-bar {
  width: 100%;
  padding: 20rpx 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20rpx;
}

.login-btn {
  display: flex;
  align-items: center;
  background: rgba(74, 144, 226, 0.1);
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #4a90e2;
}

.login-btn::after {
  display: none;
}

.user-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.user-info {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 8rpx 20rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.user-nickname {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
  background: rgba(74, 144, 226, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  width: fit-content;
}

.user-nickname.anonymous {
  color: #999;
  background: rgba(153, 153, 153, 0.1);
}

/* 评分统计样式 */
.rating-stats {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin: 20rpx;
  gap: 16rpx;
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.rating-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
}

.rating-overview {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.overall-score {
  display: flex;
  align-items: baseline;
  margin-right: 20rpx;
  padding: 0 10rpx;
  position: relative;
  min-width: 100rpx;
}

.overall-score::after {
  content: '';
  position: absolute;
  right: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60%;
  background: rgba(0, 0, 0, 0.05);
}

.overall-score .score {
  font-size: 48rpx;
  font-weight: bold;
  color: #4a90e2;
  line-height: 1;
}

.overall-score .total {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.score-details {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
  width: calc(100% - 120rpx);
  box-sizing: border-box;
  padding: 0 10rpx;
}

.score-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 12rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 8rpx;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.score-item .label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-right: 8rpx;
  white-space: nowrap;
}

.score-item .value {
  font-size: 30rpx;
  color: #4a90e2;
  font-weight: bold;
  min-width: 50rpx;
  text-align: right;
}

/* 无评分提示样式 */
.no-rating-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
}

.no-rating-tip text {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.no-rating-tip .sub-text {
  font-size: 24rpx;
  color: #bbb;
}

.rating-action {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.rating-action.no-rating {
  border-top: none;
  margin-top: 0;
}

.rate-btn {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 40rpx;
  border-radius: 44rpx;
  border: none;
  min-width: 160rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
  transition: all 0.3s ease;
}

.rate-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #357abd, #2868a9);
  box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.15);
}

.user-rating {
  display: flex;
  align-items: center;
  background: rgba(74, 144, 226, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

.user-rating .label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.user-rating .value {
  font-size: 28rpx;
  color: #4a90e2;
  font-weight: bold;
}

/* 评分弹窗样式 */
.rating-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rating-modal.show {
  background: rgba(0, 0, 0, 0.7);
  opacity: 1;
  visibility: visible;
}

.rating-content {
  width: 85%;
  max-width: 600rpx;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
  border-radius: 24rpx;
  padding: 36rpx;
  transform: scale(0.9) translateY(20rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.rating-modal.show .rating-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.rating-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 36rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.rating-modal-header .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  letter-spacing: 0.5rpx;
}

.rating-modal-header .close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 12rpx;
  line-height: 1;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -20rpx -20rpx -20rpx 0;
}

.rating-modal-header .close-btn:active {
  transform: scale(0.9);
  color: #666;
}

.rating-form {
  margin-bottom: 24rpx;
}

.rating-item {
  margin-bottom: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
  transition: all 0.2s ease;
}

.rating-item:hover {
  background: rgba(248, 250, 252, 0.95);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

.rating-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.rating-label .label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.rating-label .value {
  font-size: 34rpx;
  color: #4a90e2;
  font-weight: bold;
  min-width: 80rpx;
  text-align: right;
  transition: all 0.2s ease;
}

.star-rating {
  padding: 16rpx 0;
}

.star-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
}

.star {
  width: 44rpx;
  height: 44rpx;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.star:active {
  transform: scale(1.15);
}

.star image {
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.2s ease;
}

.star.active {
  transform: scale(1.1);
}

.star.active image {
  filter: drop-shadow(0 2rpx 4rpx rgba(74, 144, 226, 0.2));
}

.rating-tips {
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background: rgba(74, 144, 226, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(74, 144, 226, 0.1);
}

.rating-tips text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 44rpx;
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
  transition: all 0.3s ease;
  border: none;
}

.submit-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #357abd, #2868a9);
  box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.15);
}

/* 标签页样式 */
.tabs {
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  height: 80rpx;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  flex: 1;
  text-align: center;
}

.tab-item.active {
  color: #4a90e2;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 48rpx;
  height: 4rpx;
  background: #4a90e2;
  border-radius: 2rpx;
}

.tab-content {
  padding: 0 24rpx 24rpx;
  width: 100%;
  box-sizing: border-box;
  /* 使用transform而不是position来定位 */
  transform: translateZ(0);
  /* 减少重绘区域 */
  contain: layout style paint;
}

/* 详细信息卡片样式 */
.details-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.details-section {
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.details-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: #4a90e2;
  margin-right: 16rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}

/* 修改悬挂标签在标题中的样式 */
.section-title .expanded-suspension-tag {
  margin-left: 15rpx;
  font-size: 24rpx;
  padding: 6rpx 14rpx;
}

.details-card .detail-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.details-card .detail-item .label {
  width: 180rpx;
  color: #666;
  font-size: 30rpx;
}

.details-card .detail-item .value {
  flex: 1;
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}

/* 评论区样式 */
.comments-section {
  background: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  max-height: none; /* 移除最大高度限制 */
  overflow-y: visible;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.comments-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.comments-title .count {
  font-size: 26rpx;
  color: #666;
  font-weight: normal;
  margin-left: 8rpx;
}

.add-comment-btn-container {
  display: flex;
  justify-content: center;
  margin: 24rpx 0 16rpx;
}

.add-comment-btn {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  border: none;
  line-height: 1.4;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 220rpx;
  text-align: center;
}

.add-comment-btn::before {
  content: '+';
  font-size: 30rpx;
  margin-right: 8rpx;
  font-weight: bold;
}

.add-comment-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #357abd, #2868a9);
  box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.1);
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comment-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.comment-item:active {
  background: rgba(248, 250, 252, 0.9);
}

.comment-user {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background: #f0f2f5;
  flex-shrink: 0;
}

.comment-user-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.comment-nickname {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
  background: rgba(74, 144, 226, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  width: fit-content;
}

.comment-nickname.anonymous {
  color: #999;
  background: rgba(153, 153, 153, 0.1);
}

.comment-time {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
  line-height: 1.2;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.delete-btn, .report-btn, .edit-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
}

.delete-btn {
  color: #ff4d4f;
}

.edit-btn {
  color: #4a90e2;
}

.report-btn {
  color: #999;
}

.delete-btn:active, .report-btn:active, .edit-btn:active {
  opacity: 0.8;
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin: 12rpx 0;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 移除点赞相关样式 */
.comment-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.no-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 0;
  color: #999;
}

.no-comments-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.no-comments text {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.no-comments .sub-text {
  font-size: 24rpx;
  color: #bbb;
}

.load-more {
  text-align: center;
  padding: 24rpx 0 8rpx;
}

.load-more text {
  font-size: 26rpx;
  color: #4a90e2;
  padding: 12rpx 32rpx;
}

.load-more text:active {
  opacity: 0.8;
}

/* 评论弹窗样式 */
.comment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.comment-modal.show {
  background: rgba(0, 0, 0, 0.7);
  opacity: 1;
  visibility: visible;
}

.comment-modal-content {
  width: 85%;
  max-width: 600rpx;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
  border-radius: 24rpx;
  padding: 36rpx;
  transform: scale(0.9) translateY(20rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.comment-modal.show .comment-modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 确保非显示状态下完全隐藏内容 */
.comment-modal:not(.show) .comment-modal-content {
  display: none;
}

/* 适配不同屏幕尺寸 */
@media screen and (min-width: 768px) {
  .expanded-card {
    width: 100%;
    height: 85vh;
  }

  .expanded-image-container {
    height: 320rpx;
  }
}

@media screen and (orientation: landscape) {
  .expanded-card {
    height: 85vh;
  }

  .expanded-image-container {
    height: 260rpx;
  }

  .expanded-image {
    height: 220rpx;
  }
}

.close-expanded-btn {
  position: fixed;
  top: 24rpx;
  right: 24rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 48rpx;
  z-index: 1002;
  transition: all 0.2s ease;
}

.close-expanded-btn:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.3);
}

/* 评论弹窗样式 */
.comment-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.comment-modal-header .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.comment-modal-header .close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 12rpx;
  line-height: 1;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -20rpx -20rpx -20rpx 0;
}

.comment-modal-header .close-btn:active {
  transform: scale(0.9);
  color: #666;
}

.comment-form {
  position: relative;
  margin-bottom: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 4rpx;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.comment-textarea {
  width: 100%;
  min-height: 240rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border-radius: 12rpx;
  box-sizing: border-box;
  line-height: 1.6;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.comment-textarea-placeholder {
  color: #999;
  font-size: 28rpx;
  transition: opacity 0.3s ease;
}

.textarea-counter {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  font-size: 24rpx;
  color: #999;
  z-index: 2;
  background: rgba(248, 249, 250, 0.9);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.submit-comment-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.25);
  transition: all 0.3s ease;
  border: none;
  margin-top: 32rpx;
  position: relative;
  overflow: hidden;
}

.submit-comment-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.submit-comment-btn:active::after {
  transform: translateX(100%);
}

.submit-comment-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #357abd, #2868a9);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.15);
}

/* 评论框聚焦时的效果 */
.comment-form.focused {
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 确保非显示状态下完全隐藏内容 */
.comment-modal:not(.show) .comment-modal-content {
  display: none;
}

/* 功能栏样式 */
.function-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  height: 88rpx;
  box-sizing: border-box;
  gap: 32rpx;
}

/* 对比模式开关区域 */
.compare-mode {
  display: flex;
  align-items: center;
  padding: 0 12rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.compare-mode text {
  font-size: 28rpx;
  color: #333;
  padding: 0 8rpx;
  white-space: nowrap;
}

/* 优化开关组件样式 */
.mode-switch {
  transform: scale(0.8);
  margin: 0 -8rpx 0 4rpx;
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 320px) {
  .compare-mode {
    height: 56rpx;
  }

  .compare-mode text {
    font-size: 24rpx;
  }

  .mode-switch {
    transform: scale(0.7);
  }
}

@media screen and (min-width: 768px) {
  .compare-mode {
    height: 72rpx;
  }

  .compare-mode text {
    font-size: 32rpx;
  }

  .mode-switch {
    transform: scale(0.9);
  }
}



/* 排序组样式 */
.sort-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: auto;  /* 将排序组推到右边 */
  margin-right: 20rpx;  /* 添加右边距，与等级筛选按钮对齐 */
}

/* 排序选择器样式 */
.sort-field {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64rpx;
  padding: 0 24rpx;
  background: #f5f7fa;  /* 默认灰色背景 */
  border-radius: 32rpx;
  transition: all 0.3s;
  color: #666;  /* 默认灰色文字 */
  min-width: 180rpx;  /* 添加最小宽度 */
  max-width: 260rpx;  /* 添加最大宽度 */
}

.sort-field text {
  font-size: 28rpx;
  line-height: 64rpx;
  color: inherit;  /* 继承父元素的文字颜色 */
  text-align: center;  /* 文字居中 */
  flex: 1;  /* 让文字容器占满剩余空间 */
  white-space: nowrap;  /* 不换行 */
  overflow: hidden;     /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 使用省略号 */
}

.sort-field.active {
  background: #4a90e2;  /* 激活时的蓝色背景 */
  color: #fff;  /* 激活时的白色文字 */
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.2);  /* 添加阴影效果 */
}

.sort-field .picker-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;  /* 调整箭头的左边距 */
  opacity: 0.6;
  flex-shrink: 0;  /* 防止箭头被压缩 */
}

.sort-field:active {
  transform: scale(0.98);  /* 点击时的缩放效果 */
  opacity: 0.9;
}

/* 响应式调整 */
@media screen and (max-width: 320px) {
  .function-bar {
    padding: 20rpx 24rpx;
    gap: 24rpx;
  }

  .compare-mode text,
  .sort-field text {
    font-size: 24rpx;
  }
}

@media screen and (min-width: 768px) {
  .function-bar {
    padding: 28rpx 40rpx;
    gap: 40rpx;
  }

  .sort-field {
    padding: 0 32rpx;
  }

  .compare-mode text,
  .sort-field text {
    font-size: 32rpx;
  }
}



/* 排序方向按钮样式 */
.sort-direction-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64rpx;
  width: 64rpx;  /* 设置固定宽度 */
  background: #f5f7fa;
  border-radius: 50%;  /* 改为圆形按钮 */
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sort-direction-btn:active {
  transform: scale(0.95);
  background: #e8edf5;
}

.sort-direction-btn.active {
  background: #4a90e2;
}

.direction-icon {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
  transition: all 0.3s ease;
  line-height: 1;
}

.sort-direction-btn.active .direction-icon {
  color: #fff;
}

/* 添加提示文本 */
.sort-direction-btn::after {
  content: '排序';
  position: absolute;
  font-size: 20rpx;
  color: #999;
  bottom: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sort-direction-btn:hover::after {
  opacity: 1;
}



/* 宠物图鉴入口按钮样式 */
.pet-guide-btn {
  position: fixed;
  right: 30rpx;
  bottom: 240rpx;
  background-color: #4a90e2;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  transition: all 0.3s ease;
}

.pet-guide-btn:active {
  transform: scale(0.95);
  background-color: #357abd;
}

/* 评论功能下线提示样式 */
.comments-offline-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
  background: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.offline-icon {
  width: 128rpx;
  height: 128rpx;
  margin-bottom: 20rpx;
}

.offline-text {
  text-align: center;
}

.offline-text .main-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.offline-text .sub-text {
  display: block;
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

.group-info {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  padding: 20rpx 32rpx;
  margin: -10rpx 0 10rpx;
  background: rgba(74, 144, 226, 0.05);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 4rpx;
}

.group-info .group-number {
  color: #4a90e2;
  font-weight: 500;
  padding: 4rpx 12rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 4rpx;
  margin: 0 4rpx;
  position: relative;
}

.group-info text:last-child {
  color: #999;
  font-size: 22rpx;
}

/* 详情页新样式 */
/* 网格布局 */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin: 16rpx 0;
}

/* 燃料/点火/进气部分专用样式 */
.details-section[data-section-type="fuel"] .detail-item .label {
  font-size: 26rpx;
  width: 190rpx;
}

.details-section[data-section-type="fuel"] .detail-item .value {
  font-size: 28rpx;
  font-weight: 500;
}

/* 数据对比表格样式 */
.comparison-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  margin: 16rpx 0;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
}

.column-title.first-column {
  text-align: left;
  padding-left: 24rpx;
}

.comparison-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
  align-items: center;
}

.row-label {
  color: #666;
  font-size: 30rpx;
  padding-left: 24rpx;
}

.row-value {
  text-align: center;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.row-value.advance {
  color: #4a90e2;
}

/* 齿轮网格布局 */
.gear-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin: 16rpx 0;
}

.gear-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 16rpx;
  padding: 16rpx;
}

.gear-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.gear-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 数据说明样式 */
.data-description {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.05), rgba(74, 144, 226, 0.1));
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.data-description::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(to bottom, #4a90e2, #357abd);
  border-radius: 4rpx;
}

.description-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 16rpx;
  border-bottom: 1px solid rgba(74, 144, 226, 0.1);
  padding-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.description-title::before {
  content: '💡';
  margin-right: 10rpx;
  font-size: 36rpx;
}

.description-item {
  margin-bottom: 24rpx;
  position: relative;
  line-height: 1.6;
  padding-left: 16rpx;
}

.description-item:last-child {
  margin-bottom: 0;
}

.description-item > text {
  font-size: 28rpx;
  color: #555;
  text-align: justify;
  display: block;
  word-wrap: break-word;
  white-space: normal;
  line-height: 1.7;
}

/* 高亮文本样式 - 更新 */
.highlight {
  font-weight: 600;
  display: inline;
}

.description-item > text text {
  display: inline;
}

/* 描述项文本样式 - 更新 */
.description-item > text {
  display: block;
  position: relative;
  line-height: 1.6;
  padding-left: 0;
}

/* 移除左侧蓝色点 */
.description-item > text::before {
  display: none;
}

/* 边界线样式 - 优化版 */
.boundary-line {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16rpx 0; /* 与卡片间距一致 */
  position: relative;
  height: 56rpx; /* 略微降低高度 */
}

.boundary-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
}

.boundary-line-inner {
  height: 1px;
  background: linear-gradient(to right, rgba(74, 144, 226, 0), rgba(74, 144, 226, 0.8) 20%, rgba(74, 144, 226, 0.8) 80%, rgba(74, 144, 226, 0));
  flex: 1;
  position: relative;
  margin: 0 20rpx;
}

.boundary-value-container {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  border: 1rpx solid rgba(74, 144, 226, 0.3);
}

.boundary-icon {
  font-size: 24rpx;
  color: #4a90e2;
  margin-right: 6rpx;
}

.boundary-value {
  color: #4a90e2;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  min-width: 40rpx;
  margin-right: 6rpx;
}

.boundary-label {
  font-size: 20rpx;
  color: #666;
  letter-spacing: 1rpx;
}

/* 添加卡片式分界线容器 */
.boundary-card {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9); /* 提高不透明度 */
  border-radius: 12rpx;
  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 1;
}

/* 添加边缘装饰 */
.boundary-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #4a90e2, rgba(74, 144, 226, 0.5));
}

/* 添加顶部装饰线 */
.boundary-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 6rpx;
  right: 0;
  height: 2rpx;
  background: linear-gradient(to right, rgba(74, 144, 226, 0.8), rgba(74, 144, 226, 0));
}

/* 区间标题型边界线样式 */
.header-boundary {
  width: 100%;
  margin: 16rpx 0; /* 与卡片间距一致 */
  position: relative;
  height: 68rpx; /* 调整高度，更协调 */
}

.header-boundary-card {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(245, 250, 255, 0.85));
  border-radius: 12rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-boundary-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(to bottom, #4a90e2, #357abd);
  border-radius: 0 2rpx 2rpx 0;
}

.header-boundary-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 24rpx; /* 轻微调整内边距 */
  width: 100%;
  height: 100%;
}

.header-boundary-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.header-boundary-text {
  font-size: 28rpx; /* 调整大小以适应内容 */
  font-weight: bold;
  color: #4a90e2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.header-boundary-text-icon {
  font-size: 30rpx; /* 调整大小 */
  margin-right: 10rpx;
  color: #4a90e2;
}

/* 顶部边界线特殊样式 */
.header-boundary:first-child {
  margin-top: 0;
  margin-bottom: 16rpx;
  height: 72rpx; /* 保持顶部稍大一点 */
}

.header-boundary:first-child .header-boundary-card {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9));
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.header-boundary:first-child .header-boundary-text {
  font-size: 30rpx; /* 保持顶部文字稍大 */
}

.header-boundary:first-child .header-boundary-card::before {
  width: 10rpx;
  background: linear-gradient(to bottom, #4a90e2, #3a7bc9);
}

/* 雷达图样式 */
.radar-chart-container {
  margin: 16rpx 24rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx 16rpx;
  position: relative;
  z-index: 1;
  transition: opacity 0.3s ease;
  overflow: hidden; /* 确保内容不会溢出容器 */
}

/* 确保在弹窗关闭时彻底隐藏雷达图 */
.compare-overlay:not(.show) .radar-chart-container {
  display: none;
  opacity: 0;
}

.radar-chart-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 16rpx;
}

.radar-chart-wrapper {
  position: relative;
  width: 100%;
  height: 460rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  overflow: hidden; /* 确保内容不会溢出容器 */
}

.radar-chart {
  width: 100%;
  height: 100%;
  position: absolute; /* 使Canvas固定在容器内 */
  left: 0;
  top: 0;
}

/* 雷达图静态图片样式 */
.radar-chart-image {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2; /* 确保图片在Canvas上层 */
}

.radar-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 12rpx 0;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 8rpx 16rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.legend-name {
  font-size: 24rpx;
  color: #666;
  max-width: 180rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 对比图例样式 */
.compare-legend {
  width: 100%;
  background-color: transparent;
  padding: 20rpx 24rpx;
  position: relative;
  z-index: 1;
  transition: opacity 0.3s ease;
  overflow: hidden;
  box-sizing: border-box;
}

.compare-legend-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.compare-legend-item {
  display: flex;
  align-items: center;
  margin: 12rpx 0;
}

.compare-legend-item .legend-color {
  width: 28rpx;
  height: 28rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.compare-legend-item .legend-text {
  font-size: 26rpx;
  color: #333;
}

.compare-legend-tip {
  font-size: 26rpx;
  color: #666;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

/* 超级喷属性表格样式 */
.super-nitro-table .comparison-header,
.super-nitro-table .comparison-row {
  display: flex;
  width: 100%;
}

.super-nitro-table .column-title.first-column,
.super-nitro-table .row-label {
  flex: 0 0 240rpx; /* 增加宽度从220rpx到240rpx */
  min-width: 240rpx;
  box-sizing: border-box;
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.super-nitro-table .column-title:not(.first-column),
.super-nitro-table .row-value {
  flex: 1;
}

/* 修改原有行标签样式，添加不换行属性 */
.row-label {
  color: #666;
  font-size: 30rpx;
  padding-left: 24rpx;
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.row-value {
  text-align: center;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.row-value.advance {
  color: #4a90e2;
}

/* 全屏图片预览弹窗样式 */
.full-image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.full-image-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.full-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.full-image {
  width: 100%;
  height: 90%;
  object-fit: contain;
}

.full-image-tips {
  color: #fff;
  font-size: 24rpx;
  margin-top: 20rpx;
  text-align: center;
  opacity: 0.8;
}

.close-full-image-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* 带帮助图标的标签样式 */
.label-with-help {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.help-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 4rpx;
}

/* 属性说明弹窗样式 */
.attribute-help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.attribute-help-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.attribute-help-container {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.attribute-help-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.attribute-help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.attribute-help-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.attribute-help-close-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}