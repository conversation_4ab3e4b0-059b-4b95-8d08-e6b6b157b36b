/* pages/games/game-2048-ranking/game-2048-ranking.wxss */
page {
  --content-width: 94%;
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --content-width: 96%;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --content-width: 92%;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

.container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加一个半透明的遮罩层，使背景变暗，提高内容可读性 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.header {
  text-align: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  position: relative;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* VIP徽章容器样式 */
.vip-badge-container {
  position: absolute;
  top: 0;
  right: 20rpx;
  z-index: 10;
}

/* 标签页样式 */
.tabs-container {
  display: flex;
  justify-content: center;
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 6rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-normal);
  position: relative;
  transition: all 0.3s ease;
  border-radius: 8rpx;
}

.tab.active {
  color: #ffffff;
  background-color: rgba(74, 144, 226, 0.8);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 我的排名样式 */
.my-rank-container {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

.my-rank-title {
  font-size: var(--font-size-small);
  color: #666;
  margin-bottom: 10rpx;
}

.my-rank-content {
  display: flex;
  align-items: center;
}

.rank-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #4a90e2;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-normal);
  font-weight: bold;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: var(--font-size-normal);
  color: #333;
  font-weight: bold;
}

.score-info {
  display: flex;
  align-items: baseline;
}

.score-value {
  font-size: var(--font-size-title);
  color: #4a90e2;
  font-weight: bold;
  margin-right: 4rpx;
}

.score-label {
  font-size: var(--font-size-small);
  color: #666;
}

/* 排行榜列表样式 */
.ranking-list {
  width: var(--content-width);
  max-width: 720rpx;
  height: calc(100vh - 500rpx);
  margin: 0 auto;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: opacity 0.3s ease;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx 10rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item.current-user {
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 8rpx;
}

.rank-cell {
  width: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.rank-number {
  font-size: var(--font-size-normal);
  color: #666;
  font-weight: bold;
}

.rank-number.top-rank {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
}

.ranking-item:nth-child(1) .rank-number.top-rank {
  background-color: #f9d776; /* 金色 */
  box-shadow: 0 2rpx 4rpx rgba(249, 215, 118, 0.5);
}

.ranking-item:nth-child(2) .rank-number.top-rank {
  background-color: #c0c0c0; /* 银色 */
  box-shadow: 0 2rpx 4rpx rgba(192, 192, 192, 0.5);
}

.ranking-item:nth-child(3) .rank-number.top-rank {
  background-color: #cd7f32; /* 铜色 */
  box-shadow: 0 2rpx 4rpx rgba(205, 127, 50, 0.5);
}

.user-cell {
  flex: 1;
  overflow: hidden;
  margin-right: 20rpx;
}

.user-name {
  font-size: var(--font-size-normal);
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200rpx;
}

.score-cell {
  width: 120rpx;
  text-align: right;
  font-size: var(--font-size-normal);
  color: #4a90e2;
  font-weight: bold;
  margin-right: 20rpx;
}

.time-cell {
  width: 180rpx;
  text-align: right;
  font-size: var(--font-size-small);
  color: #999;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid rgba(74, 144, 226, 0.3);
  border-top: 3rpx solid rgba(74, 144, 226, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-more text {
  font-size: var(--font-size-small);
  color: #666;
}

/* 没有更多数据样式 */
.no-more-data {
  text-align: center;
  padding: 20rpx 0;
}

.no-more-data text {
  font-size: var(--font-size-small);
  color: #999;
}

/* 空数据提示样式 */
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.empty-data text {
  font-size: var(--font-size-normal);
  color: #999;
}

/* 底部区域样式 */
.footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.refresh-button, .back-button {
  background-color: rgba(74, 144, 226, 0.9);
  color: white;
  border-radius: 30rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.refresh-button:active, .back-button:active {
  transform: scale(0.95);
  opacity: 0.9;
}
