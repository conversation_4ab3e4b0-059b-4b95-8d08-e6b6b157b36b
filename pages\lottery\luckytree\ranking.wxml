<!-- 模拟夺宝排行榜页面 -->
<view class="ranking-container">
  <!-- 背景图片 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 排行榜头部 -->
  <view class="ranking-header">
    <view class="back-button" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <text class="ranking-title">{{activityName}}排行榜</text>
    <view class="refresh-button" bindtap="refreshRanking">
      <text class="refresh-icon">↻</text>
    </view>
  </view>

  <!-- 排行榜标签切换 -->
  <view class="ranking-tabs">
    <view class="tab {{activeTab === 'lucky' ? 'active' : ''}}" data-tab="lucky" bindtap="switchTab">
      欧皇榜
    </view>
    <view class="tab {{activeTab === 'unlucky' ? 'active' : ''}}" data-tab="unlucky" bindtap="switchTab">
      非酋榜
    </view>
    <view class="tab {{activeTab === 'draws' ? 'active' : ''}}" data-tab="draws" bindtap="switchTab">
      抽奖次数
    </view>
  </view>

  <!-- 用户自己的排名信息 -->
  <view class="user-ranking" wx:if="{{userRank}}">
    <text class="user-rank-text">我的排名: {{userRank.rank}}</text>
    <text class="user-score-text">
      <block wx:if="{{activeTab === 'lucky' || activeTab === 'unlucky'}}">
        得分: {{userRank.rarityScore}}
      </block>
      <block wx:else>
        抽奖次数: {{userRank.totalDraws || userRank.drawCount}}
      </block>
    </text>
  </view>

  <!-- 排行榜内容 -->
  <view class="ranking-content">
    <!-- 表头 -->
    <view class="ranking-header-row">
      <text class="rank-cell">排名</text>
      <text class="user-cell">用户</text>
      <text class="score-cell">
        <block wx:if="{{activeTab === 'lucky' || activeTab === 'unlucky'}}">得分</block>
        <block wx:else>抽奖次数</block>
      </text>
      <text class="time-cell">时间</text>
    </view>

    <!-- 排行榜列表 -->
    <scroll-view class="ranking-list" scroll-y="true" style="opacity: {{listOpacity}};">
      <view class="ranking-item {{item.isCurrentUser ? 'current-user' : ''}}"
            wx:for="{{rankingList}}"
            wx:key="id">
        <!-- 排名 -->
        <view class="rank-cell">
          <view class="rank-number {{index < 3 ? 'top-rank' : ''}}">{{index + 1}}</view>
        </view>

        <!-- 用户信息 -->
        <view class="user-cell">
          <text class="user-name">{{item.nickName || '匿名用户'}}</text>
        </view>

        <!-- 分数 -->
        <view class="score-cell">
          <text wx:if="{{activeTab === 'lucky' || activeTab === 'unlucky'}}">{{item.rarityScore}}</text>
          <text wx:else>{{item.totalDraws || item.drawCount}}</text>
        </view>

        <!-- 时间 -->
        <view class="time-cell">
          <text>{{item.time}}</text>
        </view>
      </view>

      <!-- 没有数据时显示 -->
      <view class="no-data" wx:if="{{rankingList.length === 0}}">
        <text>暂无排行数据</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部提示区域 -->
  <view class="ranking-footer">
    <view class="info-text">
      <text class="info-title">得分计算方式：</text>
      <text>1. 只有永久物品才会计入得分</text>
      <text>2. 物品得分 = 1 / 物品抽取概率</text>
      <text>3. 总物品得分 = 所有永久物品的(物品得分×获得数量)之和</text>
      <text>4. 稀有系数 = 1 / 总抽奖次数</text>
      <text>5. 最终得分 = 总物品得分 × 稀有系数</text>
    </view>

    <!-- 按钮容器 -->
    <view class="footer-buttons">
      <!-- 上传按钮 -->
      <view class="upload-button" bindtap="uploadRecord" wx:if="{{canUpload}}">
        上传我的记录
      </view>

      <!-- 删除按钮 -->
      <view class="delete-button" bindtap="deleteRecord" wx:if="{{hasUploaded}}">
        删除我的记录
      </view>
    </view>
  </view>

  <!-- 用户信息填写弹窗 -->
  <view class="user-info-modal" wx:if="{{showUserInfoModal}}">
    <view class="modal-mask" bindtap="closeUserInfoModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">请选择排行榜昵称</view>
        <view class="modal-close" bindtap="closeUserInfoModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 使用表单方式获取昵称 -->
        <form bindsubmit="formSubmit">
          <view class="nickname-section">
            <!-- 添加类名方便选择器查找 -->
            <input name="nickname" type="nickname" class="nickname-input"
              placeholder="推荐使用微信昵称（点击此处选择）"
              bindinput="onInputNickname"
              bindblur="onNicknameBlur"
              value="{{nickName}}"/>

            <!-- 显示当前选择的昵称，增强用户体验 -->
            <view class="selected-nickname" wx:if="{{nickName}}">
              当前昵称: <text class="nickname-value">{{nickName}}</text>
            </view>

            <view class="wechat-nickname-tip">点击输入框，在键盘上方点击"使用微信昵称"按钮可快速填入</view>
          </view>

          <view class="form-buttons">
            <button class="cancel-btn" bindtap="closeUserInfoModal">取消</button>
            <button class="confirm-btn" form-type="submit">确定</button>
          </view>
        </form>
      </view>
    </view>
  </view>
</view>