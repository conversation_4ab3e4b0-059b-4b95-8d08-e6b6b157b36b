// app.js
const { getConfig } = require('./config.js')

App({
  globalData: {
    userInfo: null,
    openid: null,
    token: null,
    videoAd: null,
    videoAdLoaded: false,
    vipInfo: null,
    // 添加全局价格常量
    VIP_CONSTANTS: {
      // 价格 (单位: 分)
      MONTH_PRICE: 990,   // 月卡价格: 9.9元
      YEAR_PRICE: 5990,    // 年卡价格: 59.9元

      // 天数
      MONTH_DAYS: 31,   // 月卡天数
      YEAR_DAYS: 365,   // 年卡天数
    }
  },

  /**
   * 设置全局错误处理器
   */
  setupErrorHandlers() {
    // 定义需要忽略的错误模式
    const errorPatternsToIgnore = [
      'private_getBackgroundFetchData',
      'miniprogramLog',
      'wxfile://usr/miniprogramLog',
      'FrameworkError',
      'jsapi invalid request data',
      'updateTextView:fail:got',
      'no such file or directory',
      'access wxfile://usr/miniprogramLog'
    ];

    // 检查错误是否应该被忽略的辅助函数
    const shouldIgnoreError = (errMsg) => {
      if (!errMsg || typeof errMsg !== 'string') return false;

      return errorPatternsToIgnore.some(pattern => errMsg.includes(pattern));
    };

    // 1. 捕获全局未处理的错误 - 控制台错误
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // 过滤掉特定的错误
      if (args && args.length > 0) {
        const errorMsg = String(args[0]);
        if (shouldIgnoreError(errorMsg)) {
          // 可以完全静默，或者使用更低级别的日志
          // console.log('忽略特定控制台错误:', errorMsg.substring(0, 100));
          return;
        }
      }

      // 调用原始的console.error
      originalConsoleError.apply(console, args);
    };

    // 2. 拦截微信API错误
    // 保存原始的wx对象方法
    this._originalWxMethods = {};

    // 需要拦截的wx方法列表 - 只拦截真正需要的方法
    const wxMethodsToIntercept = [
      'getBackgroundFetchData',
      'getBackgroundFetchToken',
      'onBackgroundFetchData',
      'setBackgroundFetchToken'
    ];

    // 创建一个标记，防止递归调用
    this._isInterceptingWxMethod = false;

    // 拦截所有指定的wx方法
    wxMethodsToIntercept.forEach(methodName => {
      if (typeof wx[methodName] === 'function') {
        this._originalWxMethods[methodName] = wx[methodName];

        wx[methodName] = (options = {}) => {
          // 防止递归调用
          if (this._isInterceptingWxMethod) {
            return this._originalWxMethods[methodName](options);
          }

          // 设置标记，表示正在拦截
          this._isInterceptingWxMethod = true;

          try {
            // 创建一个新的options对象，避免修改原始对象
            const newOptions = {...options};

            // 保存原始的回调函数
            const originalFail = newOptions.fail;
            const originalComplete = newOptions.complete;

            // 替换fail回调
            newOptions.fail = (res) => {
              // 检查是否是我们要忽略的错误
              if (res && res.errMsg && shouldIgnoreError(res.errMsg)) {
                console.log(`忽略wx.${methodName}错误:`, res.errMsg.substring(0, 50));

                // 如果有complete回调，仍然调用它
                if (originalComplete) {
                  originalComplete(res);
                }

                return;
              }

              // 对于其他错误，调用原始的fail回调
              if (originalFail) {
                originalFail(res);
              }
            };

            // 调用原始方法
            return this._originalWxMethods[methodName](newOptions);
          } finally {
            // 无论成功还是失败，都重置标记
            this._isInterceptingWxMethod = false;
          }
        };
      }
    });

    // 3. 拦截Promise错误
    const originalPromiseThen = Promise.prototype.then;
    Promise.prototype.then = function(onFulfilled, onRejected) {
      // 如果提供了onRejected回调，我们包装它
      if (onRejected) {
        const wrappedOnRejected = (reason) => {
          // 检查是否是我们要忽略的错误
          if (reason) {
            const errMsg = reason.errMsg || reason.message || String(reason);
            if (shouldIgnoreError(errMsg)) {
              // 返回一个已解决的Promise
              return Promise.resolve();
            }
          }

          // 对于其他错误，调用原始的onRejected回调
          return onRejected(reason);
        };

        return originalPromiseThen.call(this, onFulfilled, wrappedOnRejected);
      }

      // 如果没有提供onRejected回调，添加一个默认的错误处理程序
      return originalPromiseThen.call(this, onFulfilled, (reason) => {
        // 检查是否是我们要忽略的错误
        if (reason) {
          const errMsg = reason.errMsg || reason.message || String(reason);
          if (shouldIgnoreError(errMsg)) {
            // 返回一个已解决的Promise
            return Promise.resolve();
          }
        }

        // 对于其他错误，继续传播
        return Promise.reject(reason);
      });
    };

    // 4. 拦截全局错误事件
    wx.onError((error) => {
      if (shouldIgnoreError(error)) {
        console.log('忽略全局错误:', error.substring(0, 50));
        return;
      }
      console.error('全局错误:', error);
    });

    // 5. 拦截未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      if (res && res.reason) {
        const errMsg = res.reason.errMsg || res.reason.message || String(res.reason);
        if (shouldIgnoreError(errMsg)) {
          console.log('忽略未处理的Promise拒绝:', errMsg.substring(0, 50));
          return;
        }
      }
      console.error('未处理的Promise拒绝:', res.reason);
    });

    // 6. 添加全局错误处理函数，而不是拦截Page
    // 这样更安全，不会影响页面的正常初始化
    this.globalHandlePageError = (error) => {
      if (error) {
        const errMsg = error.errMsg || error.message || String(error);
        if (shouldIgnoreError(errMsg)) {
          return true; // 表示错误已处理
        }
      }
      return false; // 表示错误未处理
    };
  },

  /**
   * 恢复所有被修改的原始方法
   */
  restoreOriginalMethods() {
    // 重置拦截标记
    this._isInterceptingWxMethod = false;

    // 恢复Promise.prototype.then
    if (this._originalPromiseThen) {
      Promise.prototype.then = this._originalPromiseThen;
      console.log('已恢复原始的Promise.prototype.then');
    }

    // 恢复wx方法
    if (this._originalWxMethods) {
      Object.keys(this._originalWxMethods).forEach(methodName => {
        if (this._originalWxMethods[methodName]) {
          wx[methodName] = this._originalWxMethods[methodName];
          console.log(`已恢复原始的wx.${methodName}`);
        }
      });
    }

    // 注意：我们不恢复console.error，因为它应该在整个应用生命周期中保持有效
  },

  onLaunch: function() {
    // 保存原始的Promise.prototype.then
    this._originalPromiseThen = Promise.prototype.then;

    // 设置全局错误处理器
    this.setupErrorHandlers();

    // 启动时自动登录获取openid
    this.login()
  },

  onHide: function() {
    console.log('App onHide');
    // 应用进入后台时恢复原始方法
    this.restoreOriginalMethods();
  },

  onShow: function() {
    console.log('App onShow');
    // 应用回到前台时重新设置错误处理器
    if (this._originalPromiseThen && Promise.prototype.then !== this._originalPromiseThen) {
      this.setupErrorHandlers();
    }

    // 初始化云环境
    wx.cloud.init({
      env: 'cloud1-1gpha037e961200e', // 替换为你的环境ID
      traceUser: true, // 记录用户访问日志
    })

    // 初始化激励视频广告
    this.initRewardedVideoAd()

    // 获取VIP信息
    this.getVipInfo()
  },

  // 初始化激励视频广告
  initRewardedVideoAd() {
    if (wx.createRewardedVideoAd) {
      const videoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-919b8b04a2997a01'
      })

      videoAd.onLoad(() => {
        console.log('激励视频广告加载成功')
        this.globalData.videoAdLoaded = true
      })

      videoAd.onError((err) => {
        console.error('激励视频广告加载失败', err)
        this.globalData.videoAdLoaded = false
      })

      this.globalData.videoAd = videoAd
    }
  },

  // 显示激励视频广告
  showRewardedVideoAd() {
    return new Promise((resolve, reject) => {
      if (!this.globalData.videoAd) {
        reject(new Error('广告组件未初始化'))
        return
      }

      this.globalData.videoAd.show().catch(() => {
        // 失败重试
        this.globalData.videoAd.load()
          .then(() => this.globalData.videoAd.show())
          .catch(err => {
            console.error('激励视频广告显示失败', err)
            reject(err)
          })
      })

      // 监听广告关闭事件
      this.globalData.videoAd.onClose(res => {
        if (res && res.isEnded) {
          // 正常播放结束，可以下发游戏奖励
          resolve(true)
        } else {
          // 播放中途退出，不下发游戏奖励
          resolve(false)
        }
      })
    })
  },

  // 登录获取openid
  async login() {
    try {
      console.log('开始登录流程');

      // 先检查是否已经有有效的登录信息
      const cachedToken = wx.getStorageSync('token');
      const cachedOpenid = wx.getStorageSync('openid');

      // 如果缓存中有完整的登录信息，直接使用
      if (cachedToken && cachedOpenid) {
        console.log('检测到缓存的登录信息，跳过登录流程');
        this.globalData.token = cachedToken;
        this.globalData.openid = cachedOpenid;
        return { token: cachedToken, openid: cachedOpenid };
      }

      // 没有有效的缓存登录信息，进行完整登录流程
      console.log('未检测到有效登录信息，开始完整登录流程');

      // 获取登录凭证
      console.log('获取微信登录凭证');
      const loginResult = await wx.login();
      console.log('微信登录结果:', {
        hasCode: !!loginResult.code,
        code: loginResult.code ? loginResult.code.substring(0, 5) + '...' : 'null'
      });

      if (!loginResult.code) {
        throw new Error('登录失败，未获取到code');
      }

      // 发送code到后端换取openid
      console.log('发送code到后端获取openid和token');
      const loginRes = await this.callLoginAPI(loginResult.code);

      if (loginRes && loginRes.success) {
        // 保存登录信息
        const { token, openid } = loginRes;
        console.log('登录成功，获取到身份信息:', {
          hasToken: !!token,
          hasOpenid: !!openid,
          tokenPreview: token ? token.substring(0, 10) + '...' : 'null'
        });

        // 保存到存储和全局变量
        wx.setStorageSync('token', token);
        wx.setStorageSync('openid', openid);
        this.globalData.token = token;
        this.globalData.openid = openid;

        // 触发登录状态变化事件
        if (this.globalEventEmitter) {
          this.globalEventEmitter.emit('loginStatusChanged', { token, openid });
        }

        return { token, openid };
      } else {
        throw new Error(loginRes?.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败详情:', error);
      throw error;
    }
  },

  // 专门调用登录API的方法
  callLoginAPI(code) {
    return new Promise((resolve, reject) => {
      const loginUrl = `${getConfig().baseUrl}/api/login/`;
      console.log('调用登录API:', loginUrl);

      wx.request({
        url: loginUrl,
        method: 'POST',
        data: { code },
        success: (res) => {
          console.log('登录API响应:', {
            statusCode: res.statusCode,
            success: res.data?.success,
            message: res.data?.message
          });

          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            console.error('登录API返回错误状态码:', {
              statusCode: res.statusCode,
              error: res.data
            });
            reject(new Error(`登录请求失败: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          console.error('登录API请求发送失败:', error);
          reject(error);
        }
      });
    });
  },

  // 获取用户信息(仅在需要时调用)
  async getUserProfile() {
    try {
      const userRes = await wx.getUserProfile({
        desc: '用于完善用户资料'
      })

      // 更新用户信息
      await this.updateUserInfo(userRes.userInfo)

      this.globalData.userInfo = userRes.userInfo
      return userRes.userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },

  // 更新用户信息
  async updateUserInfo(userInfo) {
    try {
      // 导入API模块
      const api = require('./utils/api');

      // 调用api模块的方法
      const updateRes = await api.updateUserInfo(userInfo);

      // 更新全局数据
      this.globalData.userInfo = userInfo;

      return updateRes;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  },

  // 发起需要认证的请求
  async makeAuthRequest(url, method = 'GET', data = {}) {
    try {
      const token = wx.getStorageSync('token');

      // 输出详细请求信息
      console.log('发送API请求:', {
        url,
        method,
        hasToken: !!token,
        tokenPreview: token ? token.substring(0, 10) + '...' : '无token',
        data: JSON.stringify(data),
        time: new Date().toISOString()
      });

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url,
          method,
          header: {
            'Authorization': token ? `Bearer ${token}` : '',
            'Content-Type': 'application/json'
          },
          data,
          success: (res) => {
            // 记录响应详情
            console.log('API响应结果:', {
              url,
              statusCode: res.statusCode,
              hasData: !!res.data,
              dataPreview: res.data ? JSON.stringify(res.data).substring(0, 100) + '...' : '无数据'
            });

            if (res.statusCode === 200) {
              resolve(res.data);
            } else if (res.statusCode === 401) {
              console.error('API认证失败(401):', {
                url,
                tokenValid: !!token,
                openidExists: !!wx.getStorageSync('openid'),
                errorData: res.data,
                headers: res.header
              });

              // 401错误，尝试清除登录信息并重新登录
              this.handleAuthError();

              reject(new Error(`请求失败: ${res.statusCode}`));
            } else {
              console.error('API请求失败:', {
                statusCode: res.statusCode,
                data: res.data
              });
              reject(new Error(`请求失败: ${res.statusCode}`));
            }
          },
          fail: (error) => {
            console.error('API请求发送失败:', {
              url,
              method,
              error: error
            });
            reject(error);
          }
        });
      });
      return res;
    } catch (error) {
      console.error('API请求异常:', error);
      throw error;
    }
  },

  /**
   * 处理授权错误
   */
  handleAuthError() {
    console.log('处理授权错误，清除登录状态');
    // 清除登录信息
    wx.removeStorageSync('token');
    wx.removeStorageSync('openid');
    wx.removeStorageSync('vipInfo');

    // 清除全局数据
    this.globalData.token = null;
    this.globalData.openid = null;
    this.globalData.vipInfo = null;
  },

  /**
   * 获取用户VIP信息
   * @param {boolean} forceRefresh - 是否强制刷新，默认为false
   * @param {boolean} useCache - 是否优先使用缓存，默认为true
   * @returns {Promise<Object>} VIP信息
   */
  async getVipInfo(forceRefresh = false, useCache = true) {
    try {
      // 先检查缓存中的VIP信息
      const cachedVipInfo = wx.getStorageSync('vipInfo');
      const cachedToken = wx.getStorageSync('token');
      const cachedOpenid = wx.getStorageSync('openid');

      // 如果不是强制刷新，且优先使用缓存，且缓存中有VIP信息，直接返回缓存
      if (!forceRefresh && useCache && cachedVipInfo) {
        console.log('使用缓存的VIP信息');

        // 检查VIP信息是否过期（超过1小时）
        const now = Date.now();
        const cacheTime = cachedVipInfo.cacheTime || 0;
        const cacheExpired = now - cacheTime > 3600000; // 1小时 = 3600000毫秒

        if (!cacheExpired) {
          // 缓存未过期，直接返回
          return cachedVipInfo;
        } else {
          console.log('VIP信息缓存已过期，将在后台刷新');
          // 在后台刷新，但仍然返回缓存的值
          setTimeout(() => {
            this.getVipInfo(true, false).catch(err => {
              console.error('后台刷新VIP信息失败:', err);
            });
          }, 100);
          return cachedVipInfo;
        }
      }

      // 如果缓存中已有完整的VIP信息，只需要验证一下过期时间是否正确
      if (cachedVipInfo && cachedToken && cachedOpenid) {
        // 重新计算剩余天数，确保最新
        if (cachedVipInfo.is_valid_vip && cachedVipInfo.vip_expire_at) {
          const expireDate = new Date(cachedVipInfo.vip_expire_at);
          const now = new Date();
          cachedVipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));

          // 更新缓存中的剩余天数
          wx.setStorageSync('vipInfo', cachedVipInfo);

          // 更新全局数据
          this.globalData.vipInfo = cachedVipInfo;
          this.globalData.token = cachedToken;
          this.globalData.openid = cachedOpenid;

          console.log('使用缓存的VIP信息，剩余天数:', cachedVipInfo.remainingDays);
          return cachedVipInfo;
        }
      }

      // 确保用户已登录（优先使用缓存的登录信息）
      try {
        // 使用login方法，它已经被修改为优先使用缓存
        await this.login();
      } catch (loginError) {
        console.warn('登录失败，将使用缓存的openid:', loginError);
      }

      const openid = wx.getStorageSync('openid');
      const token = wx.getStorageSync('token');

      if (!openid || !token) {
        console.warn('用户未登录，无法获取VIP信息');
        return null;
      }

      console.log('获取VIP信息，当前凭证:', {
        openid,
        tokenPreview: token ? token.substring(0, 10) + '...' : '无token',
        baseUrl: getConfig().baseUrl
      });

      // 尝试获取最新的VIP信息
      try {
        // 通过URL参数传递openid
        const response = await this.makeAuthRequest(
          `${getConfig().baseUrl}/api/user/vip/info/?openid=${openid}`,
          'GET',
          {} // 空数据，因为openid已在URL中
        );

        if (response && response.success) {
          return this.processVipResponse(response);
        }
      } catch (error) {
        console.error('获取VIP信息失败:', error);

        // 如果获取失败，尝试从缓存获取
        if (cachedVipInfo) {
          console.log('获取VIP信息失败，使用缓存');
          return cachedVipInfo;
        }
      }

      return null;
    } catch (error) {
      console.error('获取VIP信息出错:', error);

      // 如果是网络错误，尝试从缓存获取
      try {
        const cachedVipInfo = wx.getStorageSync('vipInfo');
        if (cachedVipInfo) {
          // 重新计算剩余天数
          if (cachedVipInfo.is_valid_vip && cachedVipInfo.vip_expire_at) {
            const expireDate = new Date(cachedVipInfo.vip_expire_at);
            const now = new Date();
            cachedVipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
          }
          console.log('从缓存获取VIP信息');
          return cachedVipInfo;
        }
      } catch (e) {
        console.error('获取缓存VIP信息失败:', e);
      }

      return null;
    }
  },

  /**
   * 处理VIP响应
   * @param {Object} response - API响应
   * @returns {Object} 处理后的VIP信息
   */
  processVipResponse(response) {
    const vipInfo = response.data;

    // 计算剩余天数
    if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
      const expireDate = new Date(vipInfo.vip_expire_at);
      const now = new Date();
      vipInfo.remainingDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
    }

    // 添加缓存时间戳
    vipInfo.cacheTime = Date.now();

    // 保存到全局数据
    this.globalData.vipInfo = vipInfo;

    // 保存到本地存储，包含剩余天数和缓存时间戳
    wx.setStorageSync('vipInfo', vipInfo);

    // 单独保存VIP状态到全局变量，方便快速访问
    this.globalData.isVip = vipInfo.is_valid_vip || false;
    this.globalData.vipRemainingDays = vipInfo.remainingDays || 0;

    // 触发全局事件
    if (this.globalEventEmitter) {
      this.globalEventEmitter.emit('vipStatusChanged', vipInfo);
    }

    return vipInfo;
  },

  /**
   * 计算VIP到期时间
   * @param {string|null} currentExpireAt - 当前VIP到期时间（ISO 8601格式）
   * @returns {string} 新的到期时间（ISO 8601格式）
   */
  calculateVipExpireTime(currentExpireAt) {
    let baseDate;

    if (currentExpireAt) {
      // 如果有当前到期时间，使用它作为基准
      const currentExpireDate = new Date(currentExpireAt);
      // 确保基准时间不早于当前时间
      baseDate = currentExpireDate > new Date() ? currentExpireDate : new Date();
    } else {
      // 没有当前到期时间，使用当前时间作为基准
      baseDate = new Date();
    }

    // 增加31天（而不是一个月）
    baseDate.setDate(baseDate.getDate() + 31);

    // 返回ISO格式的时间字符串
    return baseDate.toISOString();
  },

  /**
   * 设置VIP状态
   * @param {boolean} isVip - 是否设置为VIP
   * @param {string} expireAt - 过期时间
   * @param {number} days - 天数
   * @returns {Promise<Object>} VIP信息
   */
  async setVipStatus(isVip, expireAt, days = 31) {
    try {
      // 在设置VIP状态前确保登录状态
      await this.login();

      const openid = wx.getStorageSync('openid');
      if (!openid) {
        throw new Error('用户未登录');
      }

      console.log(`设置VIP状态: isVip=${isVip}, 过期时间=${expireAt}, 天数=${days}`);

      // 调用后端接口设置VIP状态
      const response = await this.makeAuthRequest(
        `${getConfig().baseUrl}/api/user/vip/set/`,
        'POST',
        {
          openid,
          is_vip: isVip,
          vip_expire_at: expireAt,
          days: days // 添加天数参数
        }
      );

      if (!response.success) {
        throw new Error(response.message || '设置VIP状态失败');
      }

      // 更新存储
      wx.setStorageSync('is_vip', isVip);
      wx.setStorageSync('is_valid_vip', isVip);
      wx.setStorageSync('vip_expire_at', expireAt);

      // 计算剩余天数
      let remainingDays = 0;
      if (isVip && expireAt) {
        const expireDate = new Date(expireAt);
        const now = new Date();
        remainingDays = Math.max(0, Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24)));
      }

      // 更新VIP信息
      const vipInfo = {
        is_vip: isVip,
        is_valid_vip: isVip,
        vip_expire_at: expireAt,
        remainingDays
      };

      // 更新全局数据和缓存
      this.globalData.vipInfo = vipInfo;
      wx.setStorageSync('vipInfo', vipInfo);

      // 触发全局事件
      if (this.globalEventEmitter) {
        this.globalEventEmitter.emit('vipStatusChanged', vipInfo);
      }

      return vipInfo;
    } catch (error) {
      console.error('设置VIP状态失败:', error);
      throw error;
    }
  },

  /**
   * 刷新VIP信息
   */
  async refreshVipInfo() {
    const vipInfo = await this.getVipInfo();

    // 如果获取成功，触发全局事件
    if (vipInfo && this.globalEventEmitter) {
      this.globalEventEmitter.emit('vipStatusChanged', vipInfo);
    }

    return vipInfo;
  },

  /**
   * 处理VIP购买流程，从VIP弹窗调用
   * @param {Object} planData - 套餐数据，包含plan、amount和days属性
   * @returns {Promise<Object>} 处理结果
   */
  async processVipPurchase(planData) {
    try {
      console.log('App处理VIP购买:', planData);

      // 确保参数正确
      if (!planData) planData = {};

      // 设置年卡/月卡参数
      if (planData.plan === 'year' && (!planData.days || planData.days !== 365)) {
        planData.days = 365; // 年卡固定为365天
        if (!planData.amount) {
          planData.amount = 1; // 年卡59.9元
        }
      } else if (planData.plan === 'month' || !planData.plan) {
        planData.plan = 'month';
        planData.days = planData.days || 31; // 月卡固定为31天
        if (!planData.amount) {
          planData.amount = 1; // 月卡9.9元
        }
      }

      // 保存当前支付计划数据，用于支付成功回调中立即更新VIP状态
      wx.setStorageSync('currentPaymentPlanData', planData);

      wx.showLoading({
        title: '准备支付...',
        mask: true
      });

      // 先刷新登录状态，确保获取最新的openid
      await this.login();

      // 获取当前VIP信息
      const vipInfo = await this.getVipInfo();

      console.log('当前VIP信息:', vipInfo);

      // 构建商品信息
      const goodsInfo = {
        name: vipInfo?.is_valid_vip ? 'VIP会员续费' : 'VIP会员开通',
        price: planData.amount, // 单位：分
        desc: `会员服务${planData.days}天`,
        plan: planData.plan, // 套餐类型：month或year
        days: planData.days // 天数
      };

      wx.showLoading({
        title: '创建订单...',
        mask: true
      });

      // 调用云函数创建订单 - 使用新的wxpayFunctions云函数
      const orderResult = await wx.cloud.callFunction({
        name: 'wxpayFunctions',
        data: {
          type: 'wxpay_order',
          goodsInfo,
          isRenewal: vipInfo?.is_valid_vip || false
        }
      });

      // 记录完整响应结构，便于调试
      console.log('云函数完整响应:', JSON.stringify(orderResult));

      // 检查订单创建结果
      if (!orderResult.result || orderResult.result.code !== 0) {
        throw new Error(orderResult.result?.message || '创建订单失败');
      }

      console.log('订单创建返回数据:', orderResult.result.data);

      // 获取订单号和支付参数
      const { orderNo } = orderResult.result.data;

      // 构建支付参数，确保所有字段类型正确
      const paymentParams = {
        timeStamp: String(orderResult.result.data.timeStamp), // 确保是字符串
        nonceStr: orderResult.result.data.nonceStr,
        package: orderResult.result.data.packageVal, // 使用packageVal作为package参数
        signType: 'RSA',
        paySign: orderResult.result.data.paySign
      };

      // 详细日志，便于调试
      console.log('支付参数详情:', {
        timeStamp: {
          value: paymentParams.timeStamp,
          type: typeof paymentParams.timeStamp
        },
        nonceStr: {
          value: paymentParams.nonceStr,
          type: typeof paymentParams.nonceStr
        },
        package: {
          value: paymentParams.package,
          type: typeof paymentParams.package
        },
        signType: {
          value: paymentParams.signType,
          type: typeof paymentParams.signType
        },
        paySign: {
          value: paymentParams.paySign ? paymentParams.paySign.substring(0, 10) + '...' : null,
          type: typeof paymentParams.paySign
        }
      });

      // 检查支付参数是否完整
      if (!paymentParams.timeStamp || !paymentParams.nonceStr ||
          !paymentParams.package || !paymentParams.paySign) {
        console.error('支付参数不完整:', paymentParams);
        throw new Error('支付参数不完整');
      }

      console.log('订单创建成功，订单号:', orderNo);

      // 发起微信支付
      wx.showLoading({
        title: '打开支付...',
        mask: true
      });

      await this.callWXPay(paymentParams);

      // 支付成功由callWXPay中处理成功提示和VIP状态更新
      // 这里不需要再显示转圈等待，减少用户等待时间

      // 后台轻量级异步查询订单状态 - 只做一次查询，不阻塞UI
      setTimeout(async () => {
        try {
          console.log('后台异步查询订单状态...');

          const queryResult = await wx.cloud.callFunction({
            name: 'wxpayFunctions',
            data: {
              type: 'wxpay_query_order_by_out_trade_no',
              out_trade_no: orderNo
            }
          });

          console.log('后台查询订单结果:', queryResult);

          // 刷新VIP状态但不阻塞UI
          this.refreshVipInfo().catch(e => {
            console.error('后台刷新VIP状态失败:', e);
          });
        } catch (e) {
          console.error('后台查询订单状态出错:', e);
        }
      }, 1000);

      // 清除临时支付计划数据
      wx.removeStorageSync('currentPaymentPlanData');

      // 返回已在上面创建的临时VIP信息，或从缓存获取
      return wx.getStorageSync('vipInfo') || {
        is_vip: true,
        is_valid_vip: true
      };
    } catch (error) {
      console.error('VIP购买处理失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      });

      // 清除临时支付计划数据
      wx.removeStorageSync('currentPaymentPlanData');

      throw error;
    }
  },

  /**
   * 调用微信支付
   * @param {Object} payParams - 支付参数
   * @returns {Promise} 支付结果
   */
  callWXPay(payParams) {
    return new Promise((resolve, reject) => {
      // 检查并确保支付参数格式正确
      if (!payParams) {
        console.error('支付参数为空');
        return reject(new Error('支付参数为空'));
      }

      // 确保必要的支付字段存在
      const requiredFields = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign'];
      const missingFields = requiredFields.filter(field => !payParams[field]);

      if (missingFields.length > 0) {
        console.error('支付参数缺少必要字段:', missingFields, payParams);
        return reject(new Error(`支付参数缺少必要字段: ${missingFields.join(', ')}`));
      }

      // 确保各字段类型正确
      const finalParams = {
        timeStamp: String(payParams.timeStamp), // 确保时间戳是字符串
        nonceStr: String(payParams.nonceStr),
        package: String(payParams.package),
        signType: String(payParams.signType),
        paySign: String(payParams.paySign)
      };

      console.log('发起微信支付, 处理后的参数:', {
        timeStamp: finalParams.timeStamp,
        nonceStr: finalParams.nonceStr,
        package: finalParams.package,
        signType: finalParams.signType,
        paySign: finalParams.paySign.substring(0, 10) + '...' // 显示部分签名
      });

      // 在调用支付前保存当前的计划和参数，用于支付成功后立即更新VIP状态
      const currentPlanData = wx.getStorageSync('currentPaymentPlanData');

      wx.requestPayment({
        ...finalParams,
        success: async (res) => {
          console.log('支付成功', res);

          // 立即隐藏加载提示
          wx.hideLoading();

          try {
            // 支付成功后立即更新本地VIP状态，不等待轮询和服务器响应
            // 这确保了即使后续网络问题，用户也能立即使用VIP功能
            if (currentPlanData) {
              const planData = currentPlanData;
              console.log('支付成功，立即更新本地VIP状态:', planData);

              // 计算VIP过期时间
              let expireAt = new Date();
              const cachedVipInfo = wx.getStorageSync('vipInfo');
              const isRenewal = cachedVipInfo && cachedVipInfo.is_valid_vip;

              // 如果是续费，从原过期时间开始计算
              if (isRenewal && cachedVipInfo.vip_expire_at) {
                const currentExpireDate = new Date(cachedVipInfo.vip_expire_at);
                // 确保基准时间不早于当前时间
                expireAt = currentExpireDate > new Date() ? currentExpireDate : new Date();
              }

              // 增加VIP天数
              const days = planData.days || 31;
              expireAt.setDate(expireAt.getDate() + days);

              // 计算实际剩余天数
              const currentDate = new Date();
              const timeDiff = expireAt.getTime() - currentDate.getTime();
              const actualRemainingDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

              // 创建临时VIP信息
              const tempVipInfo = {
                is_vip: true,
                is_valid_vip: true,
                vip_expire_at: expireAt.toISOString(),
                remainingDays: actualRemainingDays
              };

              // 立即更新本地VIP状态
              this.globalData.vipInfo = tempVipInfo;
              wx.setStorageSync('vipInfo', tempVipInfo);

              // 强制刷新所有页面的VIP状态 - 确保首次开通时界面及时更新
              this.forceRefreshAllPages(tempVipInfo);

              // 立即显示成功提示
              wx.showToast({
                title: isRenewal ? 'VIP续费成功' : 'VIP开通成功',
                icon: 'success',
                duration: 2000
              });

              // 后台异步更新服务器VIP状态，不阻塞用户界面
              this.updateServerVipStatus(true, expireAt.toISOString(), days);
            }

            // 返回支付成功
            resolve(res);
          } catch (error) {
            console.error('支付成功但更新VIP状态失败:', error);
            // 尽管出错，仍然标记为支付成功，后续轮询可能会重试更新VIP状态
            resolve(res);
          }
        },
        fail(err) {
          console.error('支付失败', err);
          wx.hideLoading();
          wx.showToast({
            title: '支付已取消',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  // 更新服务器VIP状态的方法，不阻塞用户界面
  updateServerVipStatus(isVip, expireAt, days) {
    // 直接通过API设置VIP状态
    this.setVipStatus(isVip, expireAt, days).catch(apiError => {
      console.error('API设置VIP状态失败:', apiError);
    });
  },

  /**
   * 查询订单状态
   * @param {string} orderNo - 订单号
   * @returns {Promise<Object>} 订单状态
   */
  async queryOrderStatus(orderNo) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'queryOrderStatus',
        data: { orderNo }
      });

      return result.result;
    } catch (error) {
      console.error('查询订单状态失败:', error);
      return { success: false, message: '查询订单状态失败' };
    }
  },

  /**
   * 强制刷新所有页面的VIP状态
   * @param {Object} vipInfo - VIP信息
   */
  forceRefreshAllPages(vipInfo) {
    if (!vipInfo) return;

    console.log('强制刷新所有页面的VIP状态:', vipInfo);

    // 保存到全局和存储
    this.globalData.vipInfo = vipInfo;
    wx.setStorageSync('vipInfo', vipInfo);

    // 1. 触发全局事件
    if (this.globalEventEmitter) {
      this.globalEventEmitter.emit('vipStatusChanged', vipInfo);
    }

    // 2. 通知所有页面刷新
    try {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        console.log(`当前共有${pages.length}个页面栈`);

        pages.forEach((page, index) => {
          console.log(`尝试刷新页面[${index}]: ${page.route}`);

          // 尝试调用页面的刷新方法
          if (typeof page.refreshVipStatus === 'function') {
            page.refreshVipStatus();
          } else if (typeof page.updateVipStatus === 'function') {
            page.updateVipStatus(vipInfo);
          } else if (typeof page.initVipStatus === 'function') {
            page.initVipStatus();
          } else if (typeof page.onShow === 'function') {
            // 如果没有特定的VIP刷新方法，尝试调用onShow
            page.onShow();
          }

          // 尝试直接设置数据
          try {
            page.setData({
              isVip: vipInfo.is_valid_vip,
              vipRemainingDays: vipInfo.remainingDays || 0
            });
          } catch (e) {
            console.log('直接设置页面数据失败，可以忽略:', e);
          }
        });
      }
    } catch (error) {
      console.error('刷新页面失败:', error);
    }

    // 3. 刷新组件
    setTimeout(() => {
      // 延迟200ms再次触发事件，确保组件已准备好接收
      if (this.globalEventEmitter) {
        this.globalEventEmitter.emit('vipStatusChanged', vipInfo);
      }
    }, 200);
  },
})