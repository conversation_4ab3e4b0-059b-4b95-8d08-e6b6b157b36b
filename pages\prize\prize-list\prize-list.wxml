<!--pages/prize/prize-list/prize-list.wxml-->
<view class="container" bindtap="closeDropdowns">
  <!-- 背景图片 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- 头部区域 -->
  <view class="header">
    <view class="title">道具出货查询</view>
    <view class="subtitle">游戏内部分道具和模式的出货情况</view>

    <!-- VIP徽章 -->
    <view style="position:absolute;top:10rpx;right:-10rpx;z-index:1000;">
      <vip-badge id="vipBadge"
        pageType="lottery"
        pageKey="{{pageKey}}"
        isVip="{{isVip}}"
        freeCount="{{freeCount}}"
        remainingDays="{{vipRemainingDays}}"
        bindtap="onVipBadgeTap"
      />
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="content">

  <!-- 标签页切换 -->
  <view class="tabs-container">
    <view class="tab {{activeTab === 'sources' ? 'active' : ''}}" bindtap="switchTab" data-tab="sources">
      <text>道具查询</text>
      <view class="tab-line" wx:if="{{activeTab === 'sources'}}"></view>
    </view>
    <view class="tab {{activeTab === 'prizes' ? 'active' : ''}}" bindtap="switchTab" data-tab="prizes">
      <text>奖品查询</text>
      <view class="tab-line" wx:if="{{activeTab === 'prizes'}}"></view>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-box">
      <input class="search-input"
        placeholder="{{activeTab === 'sources' ? '搜索道具名称' : '搜索奖品名称'}}"
        confirm-type="search"
        value="{{search}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view class="search-button" bindtap="onSearch">
        <text>查询</text>
      </view>
    </view>
  </view>

  <!-- 筛选区域 -->
  <view class="filter-container" wx:if="{{activeTab === 'sources'}}">
    <scroll-view scroll-x="true" class="filter-scroll">
      <!-- 道具标签页筛选 -->
      <view class="filter-group filter-group-centered">
        <view class="filter-label">来源类型:</view>
        <view class="filter-options filter-options-centered">
          <view class="filter-option {{sourceType === '' ? 'active' : ''}}"
            bindtap="onFilterChange"
            data-type="sourceType"
            data-value="">全部</view>
          <view class="filter-option {{sourceType === item.label ? 'active' : ''}}"
            wx:for="{{sourceTypes}}"
            wx:key="value"
            bindtap="onFilterChange"
            data-type="sourceType"
            data-value="{{item.label}}">{{item.label}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 道具列表 -->
  <view class="prize-list" wx:if="{{activeTab === 'sources'}}">
    <block wx:if="{{sources.length > 0}}">
      <view class="prize-source-card"
        wx:for="{{sources}}"
        wx:key="id"
        bindtap="viewSourceDetail"
        data-source-id="{{item.id}}"
        data-source-name="{{item.name}}"
        hover-class="card-hover"
        hover-stay-time="150">

        <!-- 道具信息 -->
        <view class="source-header">
          <image class="source-image" src="{{item.image_url || '/images/default-prize.png'}}" mode="aspectFit"></image>
          <view class="source-info">
            <view class="source-name">{{item.name || '未分类'}}</view>
            <view class="source-type">{{item.source_type_display || item.source_type || '未知类型'}}</view>

            <!-- 奖励标签预览 -->
            <view class="prize-tags" wx:if="{{item.prizes && item.prizes.length > 0}}">
              <block wx:for="{{item.prizes}}" wx:for-item="prize" wx:key="id" wx:if="{{index < 3}}">
                <view class="prize-tag {{prize.rarity === 'legendary' ? 'legendary' : (prize.rarity === 'epic' ? 'epic' : (prize.rarity === 'rare' ? 'rare' : ''))}}">
                  {{prize.name}}{{prize.quantity === '永久' ? '(永久)' : ''}}
                </view>
              </block>
              <view class="prize-tag-more" wx:if="{{item.prizes.length > 3}}">+{{item.prizes.length - 3}}</view>
            </view>
          </view>
          <view class="prize-count">{{item.source_code || ''}}</view>
        </view>

        <!-- 道具描述 -->
        <view class="source-description" wx:if="{{item.description}}">
          <text>{{item.description}}</text>
        </view>

        <!-- 展开按钮 -->
        <view class="expand-button">
          <text>点击查看详情</text>
          <image src="/images/arrow-down.png" mode="aspectFit" style="transform: rotate(-90deg);"></image>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && sources.length === 0}}">
      <image src="/images/empty.png" mode="aspectFit"></image>
      <text>暂无道具数据</text>
    </view>
  </view>

  <!-- 奖品列表 -->
  <view class="prize-list" wx:if="{{activeTab === 'prizes'}}">
    <block wx:if="{{prizes.length > 0}}">
      <view class="prize-card"
        wx:for="{{prizes}}"
        wx:key="id"
        bindtap="viewPrizeDetail"
        data-prize-id="{{item.id}}"
        data-prize-name="{{item.name}}"
        hover-class="card-hover"
        hover-stay-time="150">

        <!-- 奖品信息 -->
        <view class="prize-header">
          <image class="prize-image-large" src="{{item.image_url || '/images/default-prize.png'}}" mode="aspectFit"></image>
          <view class="prize-info">
            <!-- 奖品类型标签 (放在右上角) -->
            <view class="prize-type-tag">{{item.prize_type || '未知类型'}}</view>

            <!-- 奖品名称和数量 -->
            <view class="prize-name-container">
              <view class="prize-name-large">{{item.name}}</view>
              <view class="prize-quantity" wx:if="{{item.quantity}}">{{item.quantity}}</view>
            </view>

            <!-- 奖品描述 -->
            <view class="prize-description" wx:if="{{item.description}}">{{item.description}}</view>
          </view>
        </view>

        <!-- 来源预览 -->
        <view class="sources-preview" wx:if="{{item.sources_info && item.sources_info.length > 0}}">
          <view class="sources-title">出现于:</view>
          <view class="source-tag" wx:for="{{item.sources_info}}" wx:for-item="source" wx:key="id" wx:if="{{index < 2}}">
            {{source.source_name}}
            <text class="source-probability" wx:if="{{source.probability}}">{{source.probability}}%</text>
          </view>
          <view class="more-sources" wx:if="{{item.sources_info.length > 2}}">+{{item.sources_info.length - 2}}</view>
        </view>

        <!-- 展开按钮 -->
        <view class="expand-button">
          <text>查看详情</text>
          <image src="/images/arrow-down.png" mode="aspectFit" style="width:16rpx;height:16rpx;transform:rotate(-90deg);margin-left:4rpx;"></image>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && prizes.length === 0}}">
      <image src="/images/empty.png" mode="aspectFit"></image>
      <text>暂无奖品数据</text>
    </view>
  </view>

  <!-- 加载状态 - 仅在初始加载时显示 -->
  <view class="loading-state" wx:if="{{loading && (sources.length === 0 && prizes.length === 0)}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 底部加载动画 -->
  <view class="bottom-loading" wx:if="{{loading && (sources.length > 0 || prizes.length > 0)}}">
    <view class="bottom-loading-spinner"></view>
    <text>加载更多...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!loading && !hasMore && (sources.length > 0 || prizes.length > 0)}}">
    <text>没有更多数据了</text>
  </view>

  </view> <!-- 结束content区域 -->

  <!-- 底部区域 -->
  <view class="footer">
    <!-- 广告按钮 -->
    <view class="ad-button-container" wx:if="{{showAdButton}}">
      <button class="ad-button" bindtap="watchAdForFreeCount">
        <image src="/images/video.png" mode="aspectFit"></image>
        <text>观看广告获取{{AD_REWARD_COUNT}}次免费查询</text>
      </button>
    </view>

    <!-- 免责声明卡片 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 以上仅为部分道具出货情况，持续更新完善中</view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" bindtap="goBack">
      <navigator open-type="navigateBack">返回上一页</navigator>
    </view>
  </view>
</view>

<!-- VIP对话框 -->
<vip-dialog
  show="{{showVipDialog}}"
  pageKey="{{pageKey}}"
  isVip="{{isVip}}"
  vipRemainingDays="{{vipRemainingDays}}"
  bindclose="onVipDialogClose"
  bindbuy="onBuyVip">
</vip-dialog>

<!-- 道具详情弹窗 -->
<view wx:if="{{showSourceDetail}}" class="modal-wrapper" catchtouchmove="preventTouchMove">
  <!-- 透明遮罩层，防止背景滑动 -->
  <view class="modal-mask" catchtouchmove="preventTouchMove" bindtap="closeSourceDetail"></view>

  <view class="detail-modal show">
    <view class="modal-content" catchtap>
      <view class="modal-header">
        <view class="modal-title">{{currentSource.name || '道具详情'}}</view>
        <view class="close-button" bindtap="closeSourceDetail"></view>
      </view>
      <scroll-view class="modal-body" scroll-y="true" enhanced="true" bounces="false" show-scrollbar="true" catchtouchmove="modalBodyTouchMove">
        <!-- 道具基本信息 -->
        <view class="detail-section">
          <view class="detail-section-title">基本信息</view>
          <view class="detail-item">
            <view class="detail-label">编号:</view>
            <view class="detail-value">{{currentSource.source_code || '未知'}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">类型:</view>
            <view class="detail-value">{{currentSource.source_type_display || currentSource.source_type || '未知'}}</view>
          </view>
          <view class="detail-item" wx:if="{{currentSource.description}}">
            <view class="detail-label">描述:</view>
            <view class="detail-value">{{currentSource.description}}</view>
          </view>
          <!-- 移除状态字段 -->
          <!-- 隐藏更新时间 -->
          <!-- <view class="detail-item" wx:if="{{currentSource.updated_at}}">
            <view class="detail-label">更新时间:</view>
            <view class="detail-value">{{currentSource.formatted_updated_at || currentSource.updated_at}}</view>
          </view> -->
        </view>

        <!-- 奖品列表 -->
        <view class="detail-section" wx:if="{{currentSource.prizes && currentSource.prizes.length > 0}}">
          <view class="detail-section-title">包含奖品 ({{currentSource.prizes.length}})</view>
          <view class="prize-list-item" wx:for="{{currentSource.prizes}}" wx:key="id">
            <image class="prize-list-image" src="{{item.image_url || '/images/default-prize.png'}}" mode="aspectFit"></image>
            <view class="prize-list-info">
              <view class="prize-list-name">{{item.name}} {{item.quantity ? '(' + item.quantity + ')' : ''}}</view>
              <view class="prize-list-meta">
                <view class="prize-list-rarity {{item.rarity}}">{{item.prize_type || '未知'}}</view>
                <view class="prize-list-probability" wx:if="{{item.probability}}">{{item.probability}}%</view>
              </view>
            </view>
          </view>
        </view>

        <view class="empty-state" wx:if="{{!currentSource.prizes || currentSource.prizes.length === 0}}">
          <text>暂无奖品数据</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>

<!-- 奖品详情弹窗 -->
<view wx:if="{{showPrizeDetail}}" class="modal-wrapper" catchtouchmove="preventTouchMove">
  <!-- 透明遮罩层，防止背景滑动 -->
  <view class="modal-mask" catchtouchmove="preventTouchMove" bindtap="closePrizeDetail"></view>

  <view class="detail-modal show">
    <view class="modal-content" catchtap>
      <view class="modal-header">
        <view class="modal-title">{{currentPrize.name || '奖品详情'}}</view>
        <view class="close-button" bindtap="closePrizeDetail"></view>
      </view>
      <scroll-view class="modal-body" scroll-y="true" enhanced="true" bounces="false" show-scrollbar="true" catchtouchmove="modalBodyTouchMove">
        <!-- 奖品基本信息 -->
        <view class="detail-section">
          <view class="detail-section-title">基本信息</view>
          <view class="detail-item">
            <view class="detail-label">编号:</view>
            <view class="detail-value">{{currentPrize.prize_code || '未知'}}</view>
          </view>
          <view class="detail-item" wx:if="{{currentPrize.quantity}}">
            <view class="detail-label">数量:</view>
            <view class="detail-value">{{currentPrize.quantity}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">类型:</view>
            <view class="detail-value">{{currentPrize.prize_type || '未知'}}</view>
          </view>

          <view class="detail-item" wx:if="{{currentPrize.description}}">
            <view class="detail-label">描述:</view>
            <view class="detail-value">{{currentPrize.description}}</view>
          </view>
          <!-- 移除状态字段 -->
          <!-- 隐藏更新时间 -->
          <!-- <view class="detail-item" wx:if="{{currentPrize.updated_at}}">
            <view class="detail-label">更新时间:</view>
            <view class="detail-value">{{currentPrize.formatted_updated_at || currentPrize.updated_at}}</view>
          </view> -->
        </view>

        <!-- 来源列表 -->
        <view class="detail-section" wx:if="{{currentPrize.sources_info && currentPrize.sources_info.length > 0}}">
          <view class="detail-section-title">出现来源 ({{currentPrize.sources_info.length}})</view>
          <view class="prize-list-item" wx:for="{{currentPrize.sources_info}}" wx:key="id">
            <view class="prize-list-info">
              <view class="prize-list-name">{{item.source_name || '未知来源'}}</view>
              <view class="prize-list-meta">
                <view class="prize-list-rarity">{{item.source_type_display || item.source_type || '未知类型'}}</view>
                <view class="prize-list-probability" wx:if="{{item.probability}}">{{item.probability}}%</view>
              </view>
            </view>
          </view>
        </view>

        <view class="empty-state" wx:if="{{!currentPrize.sources_info || currentPrize.sources_info.length === 0}}">
          <text>暂无来源数据</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>

<!-- 奖品类型筛选弹出层已移除 -->

<!-- 添加全局底部导航栏 -->
<custom-tabbar selected="2"></custom-tabbar>