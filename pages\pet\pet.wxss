/* 容器样式 */
.container {
  padding: 20rpx 30rpx;
  min-height: 100vh;
  position: relative;
}

/* 背景图片样式 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.8;
}

/* 搜索栏样式 */
.search-bar {
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx 20rpx 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrap {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.search-input-wrap input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.search-btn {
  height: 72rpx;
  padding: 0 30rpx;
  background: #4a90e2;
  color: #fff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.search-btn .icon-search {
  color: #fff;
  font-size: 36rpx;
}

/* 筛选条件区域 */
.filter-section {
  display: flex;
  flex-direction: row;
  gap: 12rpx;
  margin: 10rpx 0 12rpx;
  padding: 16rpx 20rpx;
  position: relative;
  width: auto;
  box-sizing: border-box;
}

/* 添加背景装饰 */
.filter-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: -20rpx;
  right: -20rpx;
  bottom: 0;
  background: rgba(246, 249, 252, 0.6);
  border-radius: 16rpx;
  z-index: -1;
}

.filter-item {
  flex: 1;
  min-width: 0;
  transform: translateY(0);
  transition: transform 0.25s ease;
  position: relative;
  padding: 8rpx 6rpx;
}

.filter-item:active {
  transform: translateY(2rpx);
}

/* 添加分隔线 */
.filter-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -6rpx;
  top: 20%;
  height: 60%;
  width: 1px;
  background: rgba(0, 0, 0, 0.06);
}

.filter-picker {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 10rpx;
}

.filter-label {
  font-size: 22rpx;
  color: #5981ae;
  font-weight: 500;
  position: relative;
  padding-left: 12rpx;
  flex-shrink: 0;
  width: 100%;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  line-height: 1.2;
}

.filter-label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 4rpx;
  height: 18rpx;
  background: #5981ae;
  border-radius: 2rpx;
  transform: translateY(-50%);
}

.filter-value {
  height: 48rpx;
  line-height: 48rpx;
  padding: 0 24rpx;
  background: #f5f5f5;
  border-radius: 24rpx;
  font-size: 22rpx;
  color: #666;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  min-width: 80%;
  max-width: 92%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  transition: all 0.3s ease;
  align-self: center;
  position: relative;
}

.filter-value::after {
  content: "";
  position: absolute;
  right: 12rpx;
  top: 50%;
  margin-top: -2rpx;
  width: 0;
  height: 0;
  border-left: 5rpx solid transparent;
  border-right: 5rpx solid transparent;
  border-top: 5rpx solid #999;
  opacity: 0.6;
}

.filter-value.active {
  background: linear-gradient(135deg, #f0f5fa, #e6eef7);
  color: #4a90e2;
  box-shadow: 0 2px 5px rgba(74, 144, 226, 0.15);
  font-weight: 500;
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';
  src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAALcAAsAAAAABpQAAAKPAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCcApcdgE2AiQDCAsGAAQgBYRnBzYbpQXIHpIkBQQKEQEDIwWEYHEI/v1o7n0RUUXbKrKqZhaTGh1NJGiEjmQyIRE6IXq93Xmn/wHbQJhQyqha5QkVSuB0wCGc1EkduAX+lOk3eEIX7IZ+gDzz3P89pDcFkh+oDG1ta9HYgBPcA4qjF9CAXOj+Y+gWcZCXYQAHlNI8kJOnLkCBwgYwzl1ATnBfIJJHVaEcMDcsapK9KqjS66kA7IX/ffJVUkmRVRk049bxLTj6KW+XovXvW4cwCCBAaC5QQR1gkHsLQ5MVjEtWcEzl4FgVweNXCfD4f0HsqgL49+MRayCqy0AClA+Ya/BEo1S8MrjlXl+gB6jA40/dwzZu3/35cPvW7d+rN+/8Wd/5+/L1m7+PHk8IqGX5/i0Ht+/+/nh47+/DezPyJ5ztOZPPi8Hz+eL+ZP58NhPh/PnhZfi/qnN+f9p0xvT/U0CbP+1/KvPfvzYG+Lt0AeBjxy2A72VeGwAbV+pnMApAe0M0TwCgtbVxpRKg8vkzVVDK8HV5SoFyoCt0eYHKUBdkBnVAVVcvVNUNQFnthOGVXRQRpBrUOgYIITxAUcMnqEJ4QWbQN1R18QdVIUJB2Xp0LlnZFRyGJhWZoB6sh/Qck1mZxbxC0rMMRvZJ8RUScwpOZc+7zBNCrGFO7IOjMAyeYYrM0EolJDgkzTCOc7qum0Iy00lFRiQC0QbUBqQOw8QbWczn90+Q6DIZKBZQqF9BhOkUWFpFvYs5gkgr1dplt+x5QUMhYeAxiEImtEoSCAQOknkpQ+JYnTohgRQzJ1WpCFVNX1/5/h0AZZrHkSJHiSp1miwxCQA=') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-search:before {
  content: "\e65c";
}

.icon-pet-form:before {
  content: "\e6d5";
}

.icon-pet-type:before {
  content: "\e6a2";
}

.icon-attribute:before {
  content: "\e686";
}

/* 筛选图标样式 */
.filter-icon {
  margin-right: 8rpx;
  font-size: 22rpx;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
  position: relative;
  top: -1rpx;
}

/* 宠物列表样式 */
.pet-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding-bottom: 40rpx;
}

.pet-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pet-card:active {
  transform: scale(0.98);
}

.pet-image-container {
  width: 100%;
  height: 240rpx;
  position: relative;
  background: #f5f5f5;
}

.pet-image {
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pet-image.loaded {
  opacity: 1;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.image-loading.hide {
  display: none;
}

.loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pet-info {
  padding: 20rpx;
}

.pet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.pet-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.pet-type {
  font-size: 24rpx;
  color: #fff;
  background: #4a90e2;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.pet-skills {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-top: 16rpx;
}

.skill-item {
  position: relative;
  font-size: 24rpx;
  color: #666;
  background: rgba(245, 245, 245, 0.8);
  padding: 12rpx 16rpx 12rpx 32rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.skill-item::before {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
}

.skill-item.basic::before {
  background: #4a90e2;
  box-shadow: 0 0 4rpx rgba(74, 144, 226, 0.4);
}

.skill-item.enhanced::before {
  background: #f5a623;
  box-shadow: 0 0 4rpx rgba(245, 166, 35, 0.4);
}

.skill-item:active {
  transform: scale(0.98);
  background: rgba(245, 245, 245, 0.9);
}

.skill-name {
  font-weight: 500;
  margin-right: 8rpx;
}

.skill-type {
  font-size: 20rpx;
  color: #999;
  background: rgba(0, 0, 0, 0.05);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  margin-left: 8rpx;
}

/* 详情弹窗样式优化 */
.pet-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-detail-overlay.show {
  opacity: 1;
  visibility: visible;
}

.pet-detail-card {
  position: relative;
  width: 92%;
  max-width: 680rpx;
  height: auto;  /* 让高度自适应内容 */
  max-height: 90vh;  /* 最大高度限制 */
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
}

.pet-detail-card.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.close-detail-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  z-index: 1;
  transition: all 0.2s ease;
}

.close-detail-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.4);
}

.detail-image-container {
  width: 100%;
  height: 440rpx;
  background: linear-gradient(to bottom, rgba(0,0,0,0.02), rgba(0,0,0,0.05));
  position: relative;
  overflow: hidden;
}

.detail-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.detail-image:active {
  transform: scale(0.98);
}

.detail-header {
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.detail-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.detail-id {
  font-size: 26rpx;
  color: #666666;
  background: #f5f5f5;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
}

.detail-tags {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.detail-form {
  font-size: 24rpx;
  color: #ffffff;
  background: #4a90e2;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.2);
}

.detail-power {
  font-size: 24rpx;
  color: #ffffff;
  background: #f5a623;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(245, 166, 35, 0.2);
}

.detail-content {
  display: block;  /* 改为block元素 */
  height: 500rpx;  /* 明确固定高度 */
  min-height: 400rpx;  /* 设置最小高度 */
  width: 100%;  /* 设置宽度100% */
  overflow-y: scroll;  /* 设置垂直滚动 */
  -webkit-overflow-scrolling: touch;  /* 增强iOS滚动体验 */
}

/* 内容容器样式优化 */
.detail-sections-container {
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 80rpx;  /* 增加底部内边距 */
}

.detail-section {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  word-break: break-all;
  overflow-wrap: break-word;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 40rpx;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 80rpx;  /* 增加底部高度 */
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 移除滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 6rpx;
  height: 32rpx;
  background: #4a90e2;
  margin-right: 16rpx;
  border-radius: 6rpx;
}

.attribute-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 24rpx;
  background: rgba(245, 245, 245, 0.6);
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.attribute-item:active {
  background: rgba(245, 245, 245, 0.8);
}

.attribute-item .label {
  font-size: 28rpx;
  color: #666666;
}

.attribute-item .value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.skill-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.skill-detail {
  background: rgba(245, 245, 245, 0.6);
  padding: 24rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.skill-detail:active {
  background: rgba(245, 245, 245, 0.8);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.skill-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: flex;
  align-items: center;
}

.skill-title::before {
  content: '';
  width: 12rpx;
  height: 12rpx;
  background: #4a90e2;
  margin-right: 12rpx;
  border-radius: 50%;
}

.skill-type {
  font-size: 24rpx;
  color: #666666;
}

.skill-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  text-align: justify;
}

/* 空状态样式优化 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  color: #999999;
  font-size: 28rpx;
}

.empty-tip::before {
  content: '🐾';
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

/* 加载状态样式优化 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 添加新的样式 */
.pet-id {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.pet-attributes {
  display: flex;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.form-tag {
  font-size: 24rpx;
  color: #fff;
  background: #4a90e2;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.power-tag {
  font-size: 24rpx;
  color: #fff;
  background: #f5a623;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.detail-tags {
  display: flex;
  gap: 12rpx;
}

.detail-form {
  font-size: 28rpx;
  color: #fff;
  background: #4a90e2;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
}

.detail-power {
  font-size: 28rpx;
  color: #fff;
  background: #f5a623;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
}

/* 搜索历史样式 */
.search-history {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  display: none;
}

.search-history.show {
  display: block;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 10rpx;
}

.history-title {
  font-size: 26rpx;
  color: #999;
}

.clear-history {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding-bottom: 10rpx;
}

.history-item {
  background: #f5f5f5;
  padding: 12rpx 24rpx;
  border-radius: 28rpx;
  font-size: 24rpx;
  color: #666;
}

.history-item:active {
  opacity: 0.7;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx;
}

/* 反馈按钮样式 */
.feedback-btn {
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: transform 0.2s ease;
}

.feedback-btn.dragging {
  transform: scale(1.1);
  transition: none;
}

.feedback-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.feedback-btn text {
  font-size: 20rpx;
  color: #666;
}

/* 拖动遮罩层 */
.drag-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  /* 完全透明的背景 */
  background-color: transparent;
  pointer-events: none;
  touch-action: none;
  display: none;
}

.drag-mask.active {
  display: block;
  /* 激活时添加微量不透明度以捕获事件 */
  background-color: rgba(0, 0, 0, 0.01);
  pointer-events: auto;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: -100rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 16rpx 20rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
  z-index: 100;
}

.back-to-top.show {
  transform: translateY(0);
  bottom: 180rpx; /* 在反馈按钮上方 */
}

.back-to-top-icon {
  font-size: 24rpx;
  margin-bottom: 2rpx;
}

.back-to-top text {
  font-size: 20rpx;
  color: #666;
}

/* 添加媒体查询适配小屏幕 */
@media screen and (max-width: 320px) {
  .filter-section {
    gap: 10rpx;
    padding: 10rpx 16rpx;
    margin: 8rpx 0 10rpx;
  }

  .filter-label {
    font-size: 24rpx;
    padding-left: 16rpx;
  }

  .filter-value {
    min-width: 100rpx;
    padding: 0 14rpx;
  }

  .search-bar {
    padding: 16rpx 16rpx 12rpx;
  }

  .search-input-wrap {
    margin-bottom: 12rpx;
  }
}

.group-info {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  padding: 20rpx 32rpx;
  margin: -10rpx 0 10rpx;
  background: rgba(74, 144, 226, 0.05);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 4rpx;
}

.group-info .group-number {
  color: #4a90e2;
  font-weight: 500;
  padding: 4rpx 12rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 4rpx;
  margin: 0 4rpx;
  position: relative;
}

.group-info text:last-child {
  color: #999;
  font-size: 22rpx;
}

.thanks-text {
  text-align: center;
  color: #666;
  font-size: 24rpx;
  margin-top: 24rpx;
  padding-top: 24rpx;
  opacity: 0.8;
  border-top: 2rpx solid rgba(0, 0, 0, 0.05);
}

/* 全屏图片预览弹窗样式 */
.full-image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.full-image-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.full-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.full-image {
  width: 100%;
  height: 90%;
  object-fit: contain;
}

.full-image-tips {
  color: #fff;
  font-size: 24rpx;
  margin-top: 20rpx;
  text-align: center;
  opacity: 0.8;
}

.close-full-image-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}