const { getConfig } = require('../../config')
const app = getApp()
const { checker } = require('../../utils/sensitive-words');
//const { textModeration } = require('../../utils/tencent-text-moderation');
const { contentModeration } = require('../../utils/content-moderation');

// 在 Page 外部定义等级权重映射
const LEVEL_WEIGHTS = {
  'S': 5,
  'A': 4,
  'B': 3,
  'C': 2,
  'D': 1,
  'T3': 0.3,
  'T2': 0.2,
  'T1': 0.1
};

// 在 Page 外部添加辅助函数
function drawRoundRect(ctx, x, y, width, height, radius) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.arcTo(x + width, y, x + width, y + radius, radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius);
  ctx.lineTo(x + radius, y + height);
  ctx.arcTo(x, y + height, x, y + height - radius, radius);
  ctx.lineTo(x, y + radius);
  ctx.arcTo(x, y, x + radius, y, radius);
  ctx.closePath();
}

// 在 Page 外部添加时间格式化辅助函数
function formatDateTime(isoString) {
  const date = new Date(isoString);
  // 转换为本地时间（UTC+8）
  const localDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);

  const year = localDate.getUTCFullYear();
  const month = String(localDate.getUTCMonth() + 1).padStart(2, '0');
  const day = String(localDate.getUTCDate()).padStart(2, '0');
  const hours = String(localDate.getUTCHours()).padStart(2, '0');
  const minutes = String(localDate.getUTCMinutes()).padStart(2, '0');
  const seconds = String(localDate.getUTCSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

Page({
  data: {
    baseUrl: getConfig().baseUrl,
    cars: [],
    filteredCars: [],
    searchKey: '',
    levels: ['全部'],  // 初始只有"全部"选项
    levelOptions: [], // 存储从接口获取的等级选项
    selectedLevel: '全部',
    loading: false,
    expandedCardId: null,
    expandedIndex: -1,
    page: 1,  // 添加页码
    hasMore: true,  // 是否还有更多数据
    isLoading: false,  // 是否正在加载
    totalPages: 1,
    currentPage: 1,
    pageSize: 24,  // 修改为正确的每页数量
    isFirstLoad: true,  // 添加一个标记，用于判断是否是首次加载
    showBackToTop: false, // 是否显示返回顶部按钮
    isCompareMode: false,
    selectedCarIds: [],
    selectedCars: [],
    showCompare: false,
    showShareOptions: false,
    compareGroups: [], // 对比数据分组
    carInfoBarCanScroll: false,
    searchHistory: [], // 添加搜索历史记录
    showSearchHistory: false, // 是否显示搜索历史
    isUserAuthorized: false,  // 改为用户是否授权的标志
    userInfo: null,
    debug: false,  // 添加调试模式标志
    showRatingModal: false,
    currentRating: {
      speed: 5,
      handling: 5,
      value: 5,
      combat: 5,  // 添加对抗评分
      appearance: 5  // 添加颜值评分
    },
    ratingStats: null,
    hasRated: false,
    userRating: null,
    activeTab: 'details', // 添加当前激活的标签页
    comments: [], // 评论列表
    commentContent: '', // 评论内容
    showCommentModal: false, // 是否显示评论弹窗
    hasMoreComments: false, // 是否还有更多评论
    commentPage: 1, // 评论页码
    commentPageSize: 10, // 每页评论数量
    editingCommentId: null, // 当前正在编辑的评论ID
    anonymousUserMap: {}, // 改用普通对象存储 openid 到匿名用户编号的映射
    nextAnonymousId: 1, // 下一个可用的匿名用户编号
    isDragging: false,
    // 将 rpx 转换为 px，90rpx 约等于 45px
    feedbackBtnLeft: wx.getWindowInfo().windowWidth - 45, // 初始位置
    feedbackBtnTop: wx.getWindowInfo().windowHeight - 200,
    touchStartX: 0,
    touchStartY: 0,
    btnStartX: 0,
    btnStartY: 0,
    visibleComments: [], // 当前可见的评论
    commentItemHeight: 200, // 评论项的估计高度
    bufferSize: 5, // 上下缓冲区的数量
    sortBy: '', // 当前排序字段
    sortOrder: 'desc', // 排序方向
    currentSortLabel: '选择排序', // 当前排序显示文本
    sortOptions: [
      { field: '', label: '默认排序' },
      { field: 'angle_normal_speed', label: '夹角平跑' },
      { field: 'angle_nitro_speed', label: '夹角氮气' },
      { field: 'normal_180_acceleration', label: '平跑180提速' },
      { field: 'normal_speed_acceleration_advance40', label: '平跑极速提速' },//特殊处理，使用推进40数据
      { field: 'nitro_250_acceleration', label: '大喷250提速' },
      { field: 'nitro_290_acceleration', label: '大喷290提速' },
      { field: 'high_speed_steering', label: '最小转向' },
      { field: 'low_speed_steering', label: '最大转向' },
      { field: 'drift_factor', label: '漂移速率' },
      { field: 'friction_factor', label: '摩擦系数' },
      { field: 'weight', label: '车重' },
      { field: 'car_id', label: '赛车编号' },
      { field: 'suspension', label: '悬挂' },
      // 添加新增字段排序选项
      { field: 'fuel_duration', label: '满改燃料时长' },
      { field: 'fuel_intensity', label: '满改燃料强度' },
      { field: 'ignition_duration', label: '满改点火时长' },
      { field: 'ignition_intensity', label: '满改点火强度' },
      { field: 'original_intake_coefficient', label: '原装进气系数' },
      { field: 'intake_coefficient', label: '满改进气系数' },
      { field: 'drift_steering', label: '漂移转向' },
      { field: 'drift_swing', label: '漂移摆动' },
      { field: 'drift_reverse', label: '漂移反向' },
      { field: 'drift_correction', label: '漂移回正' },
    ],
    hasPriorityLoading: false, // 添加优先加载权益标志
    sortLabelFontSize: '28rpx', // 添加排序标签字体大小
    radarColors: [],      // 确保雷达图颜色初始为空数组
    showFullImageModal: false, // 是否显示全屏图片预览
    fullImageUrl: '', // 全屏预览的图片URL
    showAttributeHelp: false, // 是否显示属性说明弹窗
    attributeHelpTitle: '', // 属性说明标题
    attributeHelpDescription: '' // 属性说明内容
  },

  /**
   * 设置页面级错误处理器
   */
  setupPageErrorHandlers: function() {
    // 使用应用全局的错误处理函数
    const app = getApp();
    if (app && app.globalHandlePageError) {
      this._handlePageError = app.globalHandlePageError;
    } else {
      // 如果全局处理函数不可用，使用简单的本地处理函数
      this._handlePageError = (error) => {
        if (error) {
          const errMsg = error.errMsg || error.message || String(error);
          if (typeof errMsg === 'string' && (
              errMsg.includes('private_getBackgroundFetchData') ||
              errMsg.includes('miniprogramLog') ||
              errMsg.includes('wxfile://usr/miniprogramLog')
          )) {
            return true; // 表示错误已处理
          }
        }
        return false; // 表示错误未处理
      };
    }
  },

  // 检查优先加载权益
  checkPriorityPrivilege() {
    const privilege = wx.getStorageSync('priorityLoadingPrivilege');
    if (privilege && privilege.hasPrivilege) {
      const now = new Date().getTime();
      if (now < privilege.expireTime) {
        this.setData({ hasPriorityLoading: true });
        return true;
      } else {
        // 权益已过期，清除存储
        wx.removeStorageSync('priorityLoadingPrivilege');
      }
    }
    this.setData({ hasPriorityLoading: false });
    return false;
  },

  async onLoad() {
    // 设置页面级错误处理
    this.setupPageErrorHandlers();

    // 初始化数据
    this.checkPriorityPrivilege();

    // 设置初始字体大小
    const fontSize = this.calculateSortLabelFontSize(this.data.currentSortLabel);
    this.setData({
      sortLabelFontSize: fontSize,  // 修复这里，使用"sortLabelFontSize: fontSize"格式
      showCompare: false,  // 确保初始状态下对比弹窗不显示
      radarColors: []      // 确保雷达图颜色初始为空数组
    });

    // 初始化小程序
    const initializeApp = async () => {
      try {
        wx.showLoading({
          title: '加载中...',
          mask: true
        });

        // 设置较长的超时时间
        const timeout = 15000; // 15秒

        // 使用 Promise.race 来处理超时
        const loginPromise = Promise.race([
          app.login(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('登录超时')), timeout)
          )
        ]);

        // 等待登录完成
        await loginPromise;

        // 并行请求等级列表和赛车数据以提高性能
        await Promise.all([
          this.fetchLevels().catch(err => {
            console.warn('获取等级列表失败:', err);
            // 设置默认等级列表，避免完全失败
            this.setData({
              levels: ['全部', 'S', 'A', 'B', 'C', 'D', 'T'],
              levelOptions: [
                { label: 'S', value: 'S' },
                { label: 'A', value: 'A' },
                { label: 'B', value: 'B' },
                { label: 'C', value: 'C' },
                { label: 'D', value: 'D' },
                { label: 'T', value: 'T' }
              ]
            });
          }),
          this.fetchCars().catch(err => {
            console.warn('获取赛车数据失败:', err);
            // 设置空数据，避免界面崩溃
            this.setData({
              cars: [],
              filteredCars: []
            });
          })
        ]);

        // 获取搜索历史
        this.getSearchHistory();

        // 在开发环境下显示调试信息
        const appBaseInfo = wx.getAppBaseInfo();
        if (appBaseInfo.platform === 'devtools') {
          this.setData({ debug: true });
          this.showDebugInfo();
        }

        wx.hideLoading();
      } catch(err) {
        console.error('初始化失败:', err);

        // 如果还有重试次数，则重试
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`第${retryCount}次重试初始化...`);

          // 延迟一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000));
          return initializeApp();
        }

        wx.hideLoading();

        // 所有重试都失败后，显示错误提示
        wx.showModal({
          title: '提示',
          content: '加载失败，是否重试？',
          success: (res) => {
            if (res.confirm) {
              // 用户点击确定，重新初始化
              retryCount = 0;
              initializeApp();
            }
          }
        });
      }
    };

    // 开始初始化
    initializeApp();
  },

  // 添加调试信息显示方法
  showDebugInfo() {
    const openid = wx.getStorageSync('openid')
    const token = wx.getStorageSync('token')
    console.log('========== Debug Info ==========')
    console.log('OpenID:', openid)
    console.log('Token:', token)
    console.log('Global Data:', app.globalData)
    console.log('===============================')
  },

  // 如果某些功能需要用户信息,可以在使用时获取
  async getRequiredUserInfo() {
    try {
      const userInfo = await app.getUserProfile()
      return userInfo
    } catch(err) {
      console.error('获取用户信息失败:', err)
      wx.showToast({
        title: '需要您授权才能使用该功能',
        icon: 'none'
      })
      throw err
    }
  },

  // 新增获取赛车等级列表的函数
  async fetchLevels() {
    try {
      const timeout = 10000; // 10秒超时
      const res = await Promise.race([
        new Promise((resolve, reject) => {
          wx.request({
            url: `${this.data.baseUrl}/api/cars/levels/`,
            method: 'GET',
            success: res => resolve(res),
            fail: err => reject(err)
          });
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('获取等级列表超时')), timeout)
        )
      ]);

      if (res.data && res.data.success && Array.isArray(res.data.levels)) {
        // 处理等级数据
        const levelOptions = res.data.levels;
        const levels = ['全部', ...levelOptions.map(item => item.label)];

        this.setData({
          levels,
          levelOptions
        });
      }
    } catch (error) {
      console.error('获取等级列表失败:', error);
      throw error;
    }
  },

  // 获取总页数
  async getTotalPages() {
    return new Promise((resolve) => {
      wx.request({
        url: `${this.data.baseUrl}/api/cars/`,
        method: 'GET',
        success: (res) => {
          console.log('获取总数据响应：', res.data); // 添加日志
          if (res.data && typeof res.data.count === 'number') {
            const total = res.data.count;
            const totalPages = Math.ceil(total / 24);  // 使用正确的每页数量
            console.log(`总数据：${total}，总页数：${totalPages}`); // 添加日志
            this.setData({
              totalPages,
              currentPage: totalPages  // 从最后一页开始
            });
            resolve(totalPages);
          } else {
            console.error('获取总数据格式错误：', res.data);
            this.setData({
              totalPages: 1,
              currentPage: 1
            });
            resolve(1);
          }
        },
        fail: (error) => {
          console.error('获取总数据失败：', error);
          this.setData({
            totalPages: 1,
            currentPage: 1
          });
          resolve(1);
        }
      });
    });
  },

  // 修改获取赛车数据的函数
  async fetchCars(isLoadMore = false) {
    if (this.data.isLoading) return;

    try {
      const timeout = 10000; // 10秒超时

      if (!isLoadMore && this.data.isFirstLoad) {
        const response = await Promise.race([
          new Promise(resolve => {
            wx.request({
              url: `${this.data.baseUrl}/api/cars/`,
              method: 'GET',
              success: res => resolve(res),
              fail: () => resolve(null)
            });
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('获取赛车数据超时')), timeout)
          )
        ]);

        if (response && response.data && typeof response.data.count === 'number') {
          const total = response.data.count;
          const totalPages = Math.ceil(total / this.data.pageSize);
          this.setData({
            totalPages,
            currentPage: 1,  // 修改为从第1页开始
            isFirstLoad: false
          });
        }
      }

      // 检查是否还有更多数据
      if (this.data.currentPage < 1) {
        this.setData({ hasMore: false });
        return;
      }

      this.setData({ isLoading: true });

      // 构建请求URL
      let url = `${this.data.baseUrl}/api/cars/?page=${this.data.currentPage}`;
      if (this.data.searchKey) {
        url += `&search=${encodeURIComponent(this.data.searchKey)}`;
      }
      if (this.data.selectedLevel !== '全部') {
        const levelValue = this.data.selectedLevel.replace('级', '');
        url += `&level=${encodeURIComponent(levelValue)}`;
      }
      // 添加排序参数
      if (this.data.sortBy) {
        url += `&sort_by=${encodeURIComponent(this.data.sortBy)}`;
        url += `&order=${encodeURIComponent(this.data.sortOrder)}`;
      }

      console.log(`正在加载，URL: ${url}`);

      const res = await Promise.race([
        new Promise((resolve, reject) => {
          wx.request({
            url: url,
            method: 'GET',
            header: this.data.hasPriorityLoading ? {
              'X-Priority-Loading': 'true'
            } : {},
            success: (res) => resolve(res),
            fail: err => reject(err)
          });
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('获取赛车数据超时')), timeout)
        )
      ]);

      console.log('API响应:', res.data);
      if (!res.data || !Array.isArray(res.data.results)) {
        console.error('接口返回数据格式错误：', res.data);
        return;
      }

      const newCars = res.data.results.map(car => ({
        ...car,
        imageLoading: true,
        imageLoaded: false,
        //cachedImageUrl: `${this.data.baseUrl}/media/car_images/${car.image_id}`,
        cachedImageUrl: `https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/car_images/${car.image_id}`,
        formatted_high_speed_steering: !isNaN(Number(car.high_speed_steering)) ? Number(car.high_speed_steering).toFixed(2) : car.high_speed_steering,
        formatted_low_speed_steering: !isNaN(Number(car.low_speed_steering)) ? Number(car.low_speed_steering).toFixed(2) : car.low_speed_steering,
        // 确保夹角平跑值是有效数字
        angle_normal_speed_advance40_numeric: !isNaN(parseFloat(car.angle_normal_speed_advance40)) ?
          parseFloat(car.angle_normal_speed_advance40) :
          (!isNaN(parseFloat(car.angle_normal_speed)) ? parseFloat(car.angle_normal_speed) : null),
        // 确保平跑极速时间值是有效数字
        normal_speed_acceleration_advance40_numeric: !isNaN(parseFloat(car.normal_speed_acceleration_advance40)) ?
          parseFloat(car.normal_speed_acceleration_advance40) :
          (!isNaN(parseFloat(car.normal_speed_acceleration)) ? parseFloat(car.normal_speed_acceleration) : null),
        // 确保平跑180时间值是有效数字
        normal_180_acceleration_advance40_numeric: !isNaN(parseFloat(car.normal_180_acceleration_advance40)) ?
          parseFloat(car.normal_180_acceleration_advance40) :
          (!isNaN(parseFloat(car.normal_180_acceleration)) ? parseFloat(car.normal_180_acceleration) : null),
        // 确保大喷250时间值是有效数字
        nitro_250_acceleration_advance40_numeric: !isNaN(parseFloat(car.nitro_250_acceleration_advance40)) ?
          parseFloat(car.nitro_250_acceleration_advance40) :
          (!isNaN(parseFloat(car.nitro_250_acceleration)) ? parseFloat(car.nitro_250_acceleration) : null),
        // 确保大喷290时间值是有效数字
        nitro_290_acceleration_advance40_numeric: !isNaN(parseFloat(car.nitro_290_acceleration_advance40)) ?
          parseFloat(car.nitro_290_acceleration_advance40) :
          (!isNaN(parseFloat(car.nitro_290_acceleration)) ? parseFloat(car.nitro_290_acceleration) : null)
      }));

      console.log('处理后的数据:', newCars);

      // 缓存新加载的图片
      newCars.forEach(car => {
        this.cacheImage(car.cachedImageUrl, car.image_id);
      });

      // 添加边界检测逻辑
      let processedCars = [...newCars];

      // 检查是否为时间排序字段，需要显示边界线
      const timeOrderFields = [
        'normal_speed_acceleration_advance40'// 平跑极速时间
        // 'normal_180_acceleration_advance40',   // 平跑180时间
        // 'nitro_250_acceleration_advance40',    // 大喷250时间
        // 'nitro_290_acceleration_advance40'     // 大喷290时间
      ];

      // 各个时间字段对应的中文名称
      const timeFieldNames = {
        'normal_speed_acceleration_advance40': '平跑极速时间'
        // 'normal_180_acceleration_advance40': '平跑180时间',
        // 'nitro_250_acceleration_advance40': '大喷250时间',
        // 'nitro_290_acceleration_advance40': '大喷290时间'
      };

      // 获取当前使用的字段对应的夹角平跑字段名
      const getAngleFieldName = (sortField) => {
        // 所有时间排序都基于夹角平跑(angle_normal_speed_advance40)的整数档位
        return 'angle_normal_speed_advance40';
      };

      // 获取当前字段的中文名称
      const getCurrentFieldLabel = (sortField) => {
        return timeFieldNames[sortField] || '提速时间';
      };

      if (timeOrderFields.includes(this.data.sortBy) && processedCars.length > 0) {
        console.log('检测边界线 - 按时间排序，显示夹角平跑边界');

        // 当前排序字段
        const currentSortField = this.data.sortBy;
        // 用于边界检测的字段名(始终使用夹角平跑值)
        const angleFieldName = getAngleFieldName(currentSortField);
        // 获取当前排序字段的中文名称
        const fieldLabel = getCurrentFieldLabel(currentSortField);

        // 先检查数据格式，打印一些实际值
        console.log('第一辆车数据示例:', processedCars[0]);
        console.log('夹角平跑数值字段示例:', processedCars[0][angleFieldName + '_numeric']);

        // 如果是加载更多（新页面），检查与上一页的边界
        if (isLoadMore && this.data.cars.length > 0) {
          // 获取上一页最后一辆车和新页第一辆车
          const lastCarOfPrevPage = this.data.cars[this.data.cars.length - 1];
          const firstCarOfNewPage = processedCars[0];

          // 确保它们都是真实的车辆对象（不是边界标记）
          if (!lastCarOfPrevPage.isTopBoundary && !lastCarOfPrevPage.isBoundary &&
              !firstCarOfNewPage.isTopBoundary && !firstCarOfNewPage.isBoundary) {

            // 获取两辆车的夹角平跑值
            const lastCarValue = lastCarOfPrevPage[angleFieldName + '_numeric'];
            const firstCarValue = firstCarOfNewPage[angleFieldName + '_numeric'];

            console.log(`跨页边界检查: 上一页最后(${lastCarOfPrevPage.name}:${lastCarValue}) 与 新页第一(${firstCarOfNewPage.name}:${firstCarValue})`);

            if (!isNaN(lastCarValue) && !isNaN(firstCarValue)) {
              // 获取整数部分
              const lastCarInt = Math.floor(lastCarValue);
              const firstCarInt = Math.floor(firstCarValue);

              // 如果整数部分不同，添加边界线到新页面的开头
              if (lastCarInt !== firstCarInt) {
                // 创建边界对象
                const boundaryObj = {
                  isBoundary: true,
                  headerBoundaryValue: firstCarInt,
                  headerFieldLabel: fieldLabel, // 使用对应的中文名称
                  id: 'page-boundary-' + firstCarInt
                };

                // 将边界对象插入到新页面的开头
                processedCars.unshift(boundaryObj);
                console.log(`添加跨页边界: 值=${firstCarInt}`);
              }
            }
          }
        }
        // 如果是第一页，添加顶部边界
        else if (!isLoadMore) {
          // 获取第一辆车的夹角平跑值
          const firstCarAngleValue = processedCars[0][angleFieldName + '_numeric'];

          if (!isNaN(firstCarAngleValue)) {
            const topIntValue = Math.floor(firstCarAngleValue);

            // 创建顶部标题对象并插入到数组的最前面
            const topBoundary = {
              isTopBoundary: true,
              headerBoundaryValue: topIntValue,
              headerFieldLabel: fieldLabel, // 使用对应的中文名称
              id: 'top-boundary-' + topIntValue // 创建唯一ID
            };

            // 将顶部边界添加到数组的最前面
            processedCars.unshift(topBoundary);
            console.log(`添加顶部边界: 值=${topIntValue}`);
          }
        }

        // 然后，按顺序处理数组，查找整数边界变化处
        for (let i = 0; i < processedCars.length - 1; i++) {
          const currentCar = processedCars[i];
          const nextCar = processedCars[i + 1];

          // 跳过边界对象
          if (currentCar.isTopBoundary || currentCar.isBoundary) continue;
          if (nextCar.isTopBoundary || nextCar.isBoundary) continue;

          // 使用夹角平跑值作为边界判断依据
          const currentValue = currentCar[angleFieldName + '_numeric'];
          const nextValue = nextCar[angleFieldName + '_numeric'];

          console.log(`检查边界: ${currentCar.name}(${currentValue}) 和 ${nextCar.name}(${nextValue})`);

          if (!isNaN(currentValue) && !isNaN(nextValue)) {
            // 检查是否跨越整数边界
            const currentInt = Math.floor(currentValue);
            const nextInt = Math.floor(nextValue);

            if (currentInt !== nextInt) {
              // 创建一个边界对象
              const boundaryObj = {
                isBoundary: true,
                headerBoundaryValue: nextInt,
                headerFieldLabel: fieldLabel, // 使用对应的中文名称
                id: 'boundary-' + nextInt + '-' + i // 创建唯一ID
              };

              // 将边界对象插入到当前车辆和下一个车辆之间
              processedCars.splice(i + 1, 0, boundaryObj);

              // 由于我们在数组中插入了一个新元素，需要跳过这个新插入的元素
              i++;

              console.log(`在位置 ${i} 添加边界线: 值=${nextInt}`);
            }
          }
        }
      }
      // 检查夹角平跑排序，显示普通边界线
      else if (this.data.sortBy === 'angle_normal_speed_advance40' && processedCars.length > 0) {
        console.log('检测边界线 - 按夹角平跑排序');

        // 按顺序处理数组
        for (let i = 0; i < processedCars.length - 1; i++) {
          const currentCar = processedCars[i];
          const nextCar = processedCars[i + 1];

          // 使用预处理的数值字段
          const currentValue = currentCar.angle_normal_speed_advance40_numeric;
          const nextValue = nextCar.angle_normal_speed_advance40_numeric;

          console.log(`检查边界: ${currentCar.name}(${currentValue}) 和 ${nextCar.name}(${nextValue})`);

          if (!isNaN(currentValue) && !isNaN(nextValue)) {
            // 根据排序方向确定大小值
            let largerValue, smallerValue, largerCar, smallerCar;

            if (this.data.sortOrder === 'desc') {
              // 降序排列 (从大到小)
              largerValue = currentValue;
              smallerValue = nextValue;
              largerCar = currentCar;
              smallerCar = nextCar;
            } else {
              // 升序排列 (从小到大)
              largerValue = nextValue;
              smallerValue = currentValue;
              largerCar = nextCar;
              smallerCar = currentCar;
            }

            // 检查是否跨越整数边界
            const largerInt = Math.floor(largerValue);
            const smallerInt = Math.floor(smallerValue);

            if (largerInt !== smallerInt) {
              // 在两个不同整数值之间，就需要显示边界线
              // 边界值就是较大数的向下取整值
              const boundaryValue = largerInt;

              if (this.data.sortOrder === 'desc') {
                // 降序时，边界线在后一个卡片(较小值)之前
                smallerCar.hasBoundaryBefore = true;
                smallerCar.boundaryValue = boundaryValue;
                console.log(`设置边界线: ${smallerCar.name}之前, 值=${boundaryValue}`);
              } else {
                // 升序时，边界线在前一个卡片(较小值)之后
                smallerCar.hasBoundaryAfter = true;
                smallerCar.boundaryValue = boundaryValue;
                console.log(`设置边界线: ${smallerCar.name}之后, 值=${boundaryValue}`);
              }
            }
          }
        }
      }

      const nextPage = this.data.currentPage + 1;  // 修改为递增

      // 如果是加载更多，将新数据与原有数据合并；否则直接使用新数据
      const mergedCars = isLoadMore ? [...this.data.cars, ...processedCars] : processedCars;

      this.setData({
        cars: mergedCars,
        filteredCars: mergedCars,
        currentPage: nextPage,
        hasMore: nextPage <= this.data.totalPages && newCars.length > 0  // 修改判断条件
      });
    } catch (error) {
      console.error('获取赛车数据失败：', error);
      throw error;
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 修改图片缓存函数
  cacheImage(imageUrl, imageId) {
    // 先检查缓存是否存在
    const cacheKey = `car_image_${imageId}`
    wx.getStorage({
      key: cacheKey,
      success: () => {
        // 缓存存在，不需要重新下载
      },
      fail: () => {
        // 缓存不存在，下载并缓存图片
        wx.downloadFile({
          url: imageUrl,
          success: res => {
            if (res.statusCode === 200) {
              // 使用文件系统管理器保存文件
              const fs = wx.getFileSystemManager()

              // 处理文件名：从URL提取文件名并处理特殊字符
              let fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
              // 如果文件名中有查询参数，去除
              if (fileName.includes('?')) {
                fileName = fileName.substring(0, fileName.indexOf('?'));
              }

              // 使用MD5或其他方式创建唯一且安全的文件名
              const safeFileName = `img_${new Date().getTime()}_${Math.random().toString(36).substring(2, 10)}.png`;

              // 生成文件保存路径
              const savedPath = `${wx.env.USER_DATA_PATH}/${safeFileName}`

              fs.saveFile({
                tempFilePath: res.tempFilePath,
                filePath: savedPath,
                success: (saveRes) => {
                  // 将永久路径存入缓存
                  wx.setStorage({
                    key: cacheKey,
                    data: savedPath
                  })
                  console.log('图片缓存成功:', savedPath);
                },
                fail: (error) => {
                  console.error('保存文件失败：', error)
                  // 保存失败时仍使用原始URL
                  wx.setStorage({
                    key: cacheKey,
                    data: imageUrl // 使用原始URL作为后备
                  })
                }
              })
            } else {
              // 状态码不是200时使用原始URL
              wx.setStorage({
                key: cacheKey,
                data: imageUrl
              })
            }
          },
          fail: (error) => {
            console.error('下载文件失败：', error)
            // 下载失败时使用原始URL
            wx.setStorage({
              key: cacheKey,
              data: imageUrl
            })
          }
        })
      }
    })
  },

  // 添加获取缓存图片路径函数
  getImageUrl(imageId) {
    const cacheKey = `car_image_${imageId}`
    return new Promise((resolve) => {
      wx.getStorage({
        key: cacheKey,
        success: res => {
          resolve(res.data)  // 返回缓存的本地路径
        },
        fail: () => {
          // 如果没有缓存，返回原始URL
          resolve(`${this.data.baseUrl}/media/car_images/${imageId}`)
        }
      })
    })
  },

  // 图片加载完成处理
  onImageLoad(e) {
    const index = e.currentTarget.dataset.index;

    // 使用 nextTick 延迟更新状态
    wx.nextTick(() => {
      const filteredCars = [...this.data.filteredCars];
      // 添加检查确保索引有效且对象存在且不是边界对象
      if (filteredCars && index >= 0 && index < filteredCars.length &&
          filteredCars[index] && !filteredCars[index].isTopBoundary && !filteredCars[index].isBoundary) {
      filteredCars[index].imageLoading = false;
      filteredCars[index].imageLoaded = true;

      this.setData({
        [`filteredCars[${index}]`]: filteredCars[index]
      });
      }
    });
  },

  // 图片加载错误处理
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    const filteredCars = this.data.filteredCars;

    // 添加检查确保索引有效且对象存在且不是边界对象
    if (filteredCars && index >= 0 && index < filteredCars.length &&
        filteredCars[index] && !filteredCars[index].isTopBoundary && !filteredCars[index].isBoundary) {
    // 更新图片加载状态
      filteredCars[index].imageLoading = false;
      filteredCars[index].imageLoaded = false;

      wx.showToast({
        title: '图片加载失败',
        icon: 'none'
      });

      this.setData({ filteredCars });
    }
  },

  // 修改搜索输入处理
  onSearchInput(e) {
    const searchKey = e.detail.value;
    this.setData({
      searchKey
    });
  },

  // 添加搜索按钮点击处理
  onSearch() {
    // 保存搜索历史
    if (this.data.searchKey.trim()) {
      this.saveSearchHistory(this.data.searchKey);
    }

    this.setData({
      isFirstLoad: true,
      cars: [],
      filteredCars: [],
      showSearchHistory: false
    }, () => {
      // 构建搜索URL
      let url = `${this.data.baseUrl}/api/cars/`;
      const params = [];

      if (this.data.searchKey) {
        params.push(`search=${encodeURIComponent(this.data.searchKey)}`);
      }
      if (this.data.selectedLevel !== '全部') {
        params.push(`level=${encodeURIComponent(this.data.selectedLevel)}`);
      }

      if (params.length > 0) {
        url += '?' + params.join('&');
      }

      // 获取搜索结果的总数，并重置分页
      wx.request({
        url: url,
        method: 'GET',
        success: (res) => {
          if (res.data && typeof res.data.count === 'number') {
            const total = res.data.count;
            const totalPages = Math.ceil(total / this.data.pageSize);
            this.setData({
              totalPages,
              currentPage: 1,
              isFirstLoad: false
            }, () => {
              this.fetchCars();
            });
          }
        },
        fail: (error) => {
          console.error('搜索失败：', error);
          wx.showToast({
            title: '搜索失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    });
  },

  // 修改级别选择处理
  onLevelChange(e) {
    const index = e.detail.value;
    const selectedLabel = this.data.levels[index];
    let selectedValue = ''; // 用于API查询的值

    if (selectedLabel !== '全部') {
      // 根据选中的label找到对应的value
      const selectedOption = this.data.levelOptions.find(
        option => option.label === selectedLabel
      );
      selectedValue = selectedOption ? selectedOption.value : '';
    }

    this.setData({
      selectedLevel: selectedLabel,
      isFirstLoad: true,
      cars: [],
      filteredCars: []
    }, () => {
      // 构建筛选URL
      let url = `${this.data.baseUrl}/api/cars/`;
      const params = [];

      if (this.data.searchKey) {
        params.push(`search=${encodeURIComponent(this.data.searchKey)}`);
      }
      if (selectedValue) { // 使用value而不是label进行查询
        params.push(`level=${encodeURIComponent(selectedValue)}`);
      }

      if (params.length > 0) {
        url += '?' + params.join('&');
      }

      // 获取筛选结果的总数，并重置分页
      wx.request({
        url: url,
        method: 'GET',
        success: (res) => {
          if (res.data && typeof res.data.count === 'number') {
            const total = res.data.count;
            const totalPages = Math.ceil(total / this.data.pageSize);
            this.setData({
              totalPages,
              currentPage: 1,
              isFirstLoad: false
            }, () => {
              this.fetchCars();
            });
          }
        },
        fail: (error) => {
          console.error('筛选失败：', error);
          wx.showToast({
            title: '筛选失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    });
  },

  // 修改筛选函数，只在本地数据中筛选
  filterCars() {
    const { cars, searchKey, selectedLevel } = this.data;
    let filtered = [...cars];

    // 只在没有主动搜索和筛选时使用本地筛选
    if (!this.data.searchKey && this.data.selectedLevel === '全部') {
      this.setData({
        filteredCars: filtered
      });
    }
  },

  // 卡片点击处理
  async onCardTap(e) {
    const { id } = e.currentTarget.dataset;
    const index = this.data.filteredCars.findIndex(car => car.id === id);

    if (index !== -1) {
      // 处理进气系数格式化
      const car = this.data.filteredCars[index];

      // 预处理进气系数数据
      const formattedCars = [...this.data.filteredCars];
      if (formattedCars[index].original_intake_coefficient) {
        formattedCars[index].formatted_original_intake = (formattedCars[index].original_intake_coefficient * 100).toFixed(2) + '%';
      } else {
        formattedCars[index].formatted_original_intake = '暂无';
      }

      if (formattedCars[index].intake_coefficient) {
        formattedCars[index].formatted_intake = (formattedCars[index].intake_coefficient * 100).toFixed(2) + '%';
      } else {
        formattedCars[index].formatted_intake = '暂无';
      }

      // 预处理燃料强度和点火强度数据
      if (formattedCars[index].fuel_intensity) {
        formattedCars[index].formatted_fuel_intensity = (formattedCars[index].fuel_intensity / 1000).toFixed(3);
      } else {
        formattedCars[index].formatted_fuel_intensity = '暂无';
      }

      if (formattedCars[index].ignition_intensity) {
        formattedCars[index].formatted_ignition_intensity = (formattedCars[index].ignition_intensity / 1000).toFixed(3);
      } else {
        formattedCars[index].formatted_ignition_intensity = '暂无';
      }

      // 处理加速数据
      this.processAccelerationData(index);

      // 切换到详情页，记住当前展开的卡片ID
      this.setData({
        filteredCars: formattedCars,
        expandedCardId: id,
        expandedIndex: index,
        activeTab: 'details',
        comments: [],
        commentPage: 1,
        hasMoreComments: false,
        ratingStats: null,
        hasRated: false,
        userRating: null,
      });

      // 获取评分统计
      this.fetchRatingStats(car.car_id);

      // 加载评论数据
      this.loadComments(car.car_id, true);
    }
  },

  // 处理加速数据，检测是否包含数字并标记
  processAccelerationData(index) {
    const car = this.data.filteredCars[index];

    // 辅助函数：检查字符串是否包含数字
    const hasNumber = (value) => {
      if (!value) return false;
      return /\d/.test(String(value));
    };

    // 为加速相关数据添加标记
    car.normal_180_acceleration_hasNumber = hasNumber(car.normal_180_acceleration);
    car.normal_180_acceleration_advance40_hasNumber = hasNumber(car.normal_180_acceleration_advance40);
    car.normal_speed_acceleration_hasNumber = hasNumber(car.normal_speed_acceleration);
    car.normal_speed_acceleration_advance40_hasNumber = hasNumber(car.normal_speed_acceleration_advance40);
    car.nitro_250_acceleration_hasNumber = hasNumber(car.nitro_250_acceleration);
    car.nitro_250_acceleration_advance40_hasNumber = hasNumber(car.nitro_250_acceleration_advance40);
    car.nitro_290_acceleration_hasNumber = hasNumber(car.nitro_290_acceleration);
    car.nitro_290_acceleration_advance40_hasNumber = hasNumber(car.nitro_290_acceleration_advance40);

    // 更新数据
    const filteredCars = [...this.data.filteredCars];
    filteredCars[index] = car;
    this.setData({
      filteredCars
    });
  },

  // 添加重置滚动位置的函数
  resetScrollPosition(tabName) {
    const query = wx.createSelectorQuery();
    query.select(`#scroll-${tabName}-${this.data.expandedCardId}`)
      .node(res => {
        const scrollView = res.node;
        if (scrollView && scrollView.scrollTo) {
          scrollView.scrollTo({
            top: 0,
            behavior: 'instant'  // 立即滚动，不使用动画
          });
        }
      })
      .exec();
  },

  // 修改标签页切换方法
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    }, () => {
      // 切换标签页时也重置滚动位置
      this.resetScrollPosition(tab);
    });
  },

  // 修改关闭卡片的处理
  onOverlayTap() {
    // 清除可能存在的计时器
    if (this.data.scrollTimer) {
      clearTimeout(this.data.scrollTimer);
    }

    wx.nextTick(() => {
      this.setData({
        expandedCardId: null,
        expandedIndex: -1,
        cardScrollTop: 0,
        scrollTimer: null
      });
    });
  },

  // 添加展开卡片点击处理（阻止冒泡）
  onExpandedCardTap(e) {
    // 添加安全检查
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
  },

  // 添加页面卸载处理
  onUnload() {
    // 清理7天前的缓存
    const CACHE_TIME = 7 * 24 * 60 * 60 * 1000  // 7天的毫秒数
    wx.getStorageInfo({
      success: res => {
        const keys = res.keys
        keys.forEach(key => {
          if (key.startsWith('car_image_')) {
            wx.getStorage({
              key: key,
              success: storageRes => {
                const saveTime = storageRes.timestamp
                if (Date.now() - saveTime > CACHE_TIME) {
                  wx.removeStorage({ key })
                }
              }
            })
          }
        })
      }
    })
  },

  // 修改触底加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.fetchCars(true);
    }
  },

  // 添加分享功能
  onShareAppMessage() {
    if (this.data.showCompare) {
      const cars = this.data.selectedCars;
      const carNames = cars.map(car => car.name).join('、');

      return {
        title: `${carNames}赛车数据对比`,
        path: `/pages/index/index?compare=${cars.map(car => car.id).join(',')}`,
        imageUrl: this.data.shareImagePath // 使用生成的对比图片
      };
    }

    return {
      title: '飞车图鉴',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    if (this.data.showCompare) {
      const cars = this.data.selectedCars;
      const carNames = cars.map(car => car.name).join('、');

      return {
        title: `${carNames}赛车数据对比`,
        query: `compare=${cars.map(car => car.id).join(',')}`,
        imageUrl: this.data.shareImagePath
      };
    }

    return {
      title: '飞车图鉴'
    };
  },

  /**
   * 优化后的反馈页面跳转函数
   * 使用无动画跳转替代原有的动画切换
   */
  navigateToFeedback() {
    // 防止频繁点击
    if (this.data.isNavigating) return;

    this.setData({
      isNavigating: true
    });

    // 直接跳转，不使用动画
    wx.navigateTo({
      url: '/pages/feedback/feedback',
      animationType: 'none', // 禁用动画效果
      success: () => {
        console.log('跳转到反馈页面成功');
      },
      fail: (err) => {
        console.error('跳转到反馈页面失败', err);
      },
      complete: () => {
        // 延迟重置状态，避免连续点击
        setTimeout(() => {
          this.setData({
            isNavigating: false
          });
        }, 500);
      }
    });
  },

  // 阻止背景滚动
  onOverlayMove() {
    return false;
  },

  // 允许卡片内容滚动
  catchMove() {
    return;
  },

  // 显示全屏图片
  showFullImage(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) return;

    this.setData({
      showFullImageModal: true,
      fullImageUrl: url
    });
  },

  // 隐藏全屏图片
  hideFullImage() {
    this.setData({
      showFullImageModal: false
    });
  },

  // 显示属性说明
  showAttributeHelp(e) {
    const attribute = e.currentTarget.dataset.attribute;
    let title = '';
    let description = '';

    // 根据不同的属性设置不同的说明内容
    if (attribute === 'max_steering') {
      title = '最大转向';
      description = '赛车低速行驶时的转向，值越大越灵活';
    } else if (attribute === 'min_steering') {
      title = '最小转向';
      description = '赛车高速行驶时的转向，值越大越灵活';
    }

    this.setData({
      showAttributeHelp: true,
      attributeHelpTitle: title,
      attributeHelpDescription: description
    });
  },

  // 隐藏属性说明
  hideAttributeHelp() {
    this.setData({
      showAttributeHelp: false
    });
  },

  // 修改监听页面滚动
  onPageScroll(e) {
    // 使用 wx.getWindowInfo 替代 wx.getSystemInfoSync
    const windowInfo = wx.getWindowInfo();
    const showBackToTop = e.scrollTop > windowInfo.windowHeight;
    if (showBackToTop !== this.data.showBackToTop) {
      this.setData({ showBackToTop });
    }
  },

  // 返回顶部
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  },

  // 切换对比模式
  onCompareModeChange(e) {
    const isCompareMode = e.detail.value;
    this.setData({
      isCompareMode,
      selectedCarIds: [],
      selectedCars: []
    });
  },

  // 选择卡片
  onCardSelect(e) {
    const id = e.currentTarget.dataset.id;
    const { selectedCarIds, selectedCars, filteredCars } = this.data;

    if (selectedCarIds.includes(id)) {
      // 取消选择
      this.setData({
        selectedCarIds: selectedCarIds.filter(carId => carId !== id),
        selectedCars: selectedCars.filter(car => car.id !== id)
      });
    } else if (selectedCarIds.length < 3) {
      // 添加选择
      const car = filteredCars.find(car => car.id === id);
      this.setData({
        selectedCarIds: [...selectedCarIds, id],
        selectedCars: [...selectedCars, car]
      });
    } else {
      wx.showToast({
        title: '最多只能对比3辆车',
        icon: 'none'
      });
    }
  },

  // 复选框点击处理(阻止冒泡)
  onCheckboxTap(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    this.onCardSelect({ currentTarget: { dataset: { id } } });
  },

  // 从对比中移除
  removeFromCompare(e) {
    const id = e.currentTarget.dataset.id;
    const { selectedCarIds, selectedCars } = this.data;

    this.setData({
      selectedCarIds: selectedCarIds.filter(carId => carId !== id),
      selectedCars: selectedCars.filter(car => car.id !== id)
    });
  },

  // 清空对比
  clearCompare() {
    this.setData({
      selectedCarIds: [],
      selectedCars: []
    });
  },

  // 修改开始对比函数
  startCompare() {
    if (this.data.selectedCars.length < 2) {
      wx.showToast({
        title: '请至少选择2辆车',
        icon: 'none'
      });
      return;
    }

    // 检查是否超过最大对比数
    if (this.data.selectedCars.length > 5) {
      wx.showToast({
        title: '最多只能对比5辆车',
        icon: 'none'
      });
      return;
    }

    // 生成对比数据
    const compareGroups = this.generateCompareData();

    // 设置预定义的雷达图颜色
    const radarColors = [
      '#4a90e2',  // 蓝色
      '#ff6b6b',  // 红色
      '#50e3c2',  // 绿色
      '#f5a623',  // 橙色
      '#9013fe'   // 紫色
    ];

    // 先设置状态
    this.setData({
      showCompare: true,
      showCarList: false,
      compareGroups,
      radarColors,
      radarChartImage: '',  // 清空之前的雷达图图片URL
      showRadarCanvas: true // 显示Canvas，准备绘制新的雷达图
    });

      // 检查是否需要滚动
      this.checkScrollable();

    console.log('对比窗口已打开，准备初始化雷达图');

    // 增加延迟时间，确保雷达图组件已正确加载
    setTimeout(() => {
      try {
        // 初始化雷达图
        this.initRadarChart();
      } catch (error) {
        console.error('启动雷达图初始化失败:', error);
      }
    }, 800); // 增加延迟时间到800ms

    // // 添加"点击生成对比图"的提示
    // wx.showToast({
    //   title: '点击下载按钮可生成对比图',
    //   icon: 'none',
    //   duration: 2000
    // });
  },

  // 修改获取高亮状态函数
  getHighlights(values, type = 'higher', fieldName = '') {
    // 过滤出有效的数字值及其索引
    const validValues = values.map((value, index) => {
      const num = parseFloat(value);
      return isNaN(num) ? null : { value: num, index };
    }).filter(item => item !== null);

    // 如果没有有效数值，返回空数组
    if (validValues.length === 0) {
      return values.map(() => '');
    }

    // 检查是否为特殊字段：摩擦系数或车重
    const isSpecialField = fieldName === 'friction_factor' || fieldName === 'weight';

    // 如果是特殊字段，则使用范围判断
    if (isSpecialField) {
      return values.map(value => {
        const num = parseFloat(value);
        if (isNaN(num)) return ''; // 非数字值不高亮

        if (fieldName === 'friction_factor') {
          // 摩擦系数在2.6-2.8之间最佳
          if (num >= 2.6 && num <= 2.8) return 'higher';
          if (num < 2.4 || num > 3.0) return 'lower'; // 过低或过高都不好
          return '';
        } else if (fieldName === 'weight') {
          // 车重在1.3-1.4之间最佳
          if (num >= 1.3 && num <= 1.4) return 'higher';
          if (num <= 1.2 || num >= 1.5) return 'lower'; // 过轻或过重都不好
          return '';
        }

        return '';
      });
    }

    // 找出最大值和最小值
    const max = Math.max(...validValues.map(item => item.value));
    const min = Math.min(...validValues.map(item => item.value));

    // 如果所有值相等，则全部返回空字符串(不高亮)
    if (max === min) {
      return values.map(() => '');
    }

    // 生成高亮数组，考虑类型参数
    return values.map((value, index) => {
      const num = parseFloat(value);
      if (isNaN(num)) return ''; // 非数字值不高亮

      // 根据类型参数判断哪个值是"更好的"
      if (type === 'higher') {
        // 更高的值更好（如极速）
        if (num === max) return 'higher';
        if (num === min) return 'lower';
      } else if (type === 'lower') {
        // 更低的值更好（如加速时间）
        if (num === min) return 'higher';
        if (num === max) return 'lower';
      }

      return '';
    });
  },

  // 修改 generateCompareData 函数中的比较逻辑
  generateCompareData() {
    const { selectedCars } = this.data;

    // 辅助函数：提取数字
    const extractNumber = (value) => {
      if (!value) return NaN; // 处理空值

      if (typeof value === 'string') {
        const match = value.match(/^([-\d.]+)/);
        return match ? parseFloat(match[1]) : NaN; // 非数字返回NaN
      }

      const num = parseFloat(value);
      return isNaN(num) ? NaN : num; // 确保返回数字或NaN
    };

    // 判断每辆车使用哪种推进数据
    const shouldUseZeroAdvance = (car) => {
      return car.level && (car.level.includes('S') || car.level.includes('T3'));
    };

    // 检查是否至少有一辆车拥有超级喷功能
    const hasSuperNitro = selectedCars.some(car =>
      (car.super_nitro_intensity && car.super_nitro_intensity !== '暂无') ||
      (car.super_nitro_duration && car.super_nitro_duration !== '暂无') ||
      (car.super_nitro_trigger_condition && car.super_nitro_trigger_condition !== '暂无') ||
      (car.angle_super_nitro_speed && car.angle_super_nitro_speed !== '暂无') ||
      (car.super_nitro_250_acceleration && car.super_nitro_250_acceleration !== '暂无') ||
      (car.super_nitro_290_acceleration && car.super_nitro_290_acceleration !== '暂无')
    );

    // 定义对比组
    const compareGroups = [
      {
        title: '基础属性',
        items: [
          {
            label: '级别',
            values: selectedCars.map(car => car.level),
            highlights: [] // 移除等级的高亮效果
          },
          {
            label: '车重',
            values: selectedCars.map(car => car.weight),
            highlights: (() => {
              // 直接使用extractNumber函数即可，它已经处理了非数字值
              const extractedWeights = selectedCars.map(car => extractNumber(car.weight));
              return this.getHighlights(extractedWeights, 'higher', 'weight');
            })()
          },
          {
            label: '悬挂',
            values: selectedCars.map(car => car.suspension && car.suspension !== '0' ? `悬挂${car.suspension}` : '无'),
            highlights: []// 不设置高亮效果
          },
          {
            label: '宝石槽',
            values: selectedCars.map(car => car.gem_slots || '无'),
            highlights: [] // 不设置高亮效果
          }
        ]
      },
      {
        title: '燃料/点火/进气',
        items: [
          {
            label: '满改燃料时长',
            values: selectedCars.map(car => car.fuel_duration || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.fuel_duration)), 'higher')
          },
          {
            label: '满改燃料强度',
            values: selectedCars.map(car => {
              const value = car.fuel_intensity;
              return value ? (value / 1000).toFixed(2) : '暂无';
            }),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.fuel_intensity)), 'higher')
          },
          {
            label: '满改点火时长',
            values: selectedCars.map(car => car.ignition_duration || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.ignition_duration)), 'higher')
          },
          {
            label: '满改点火强度',
            values: selectedCars.map(car => {
              const value = car.ignition_intensity;
              return value ? (value / 1000).toFixed(2) : '暂无';
            }),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.ignition_intensity)), 'higher')
          },
          {
            label: '原装进气系数',
            values: selectedCars.map(car => {
              const value = car.original_intake_coefficient;
              return value ? `${(value * 100).toFixed(2)}%` : '暂无';
            }),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.original_intake_coefficient)), 'higher')
          },
          {
            label: '满改进气系数',
            values: selectedCars.map(car => {
              const value = car.intake_coefficient;
              return value ? `${(value * 100).toFixed(2)}%` : '暂无';
            }),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.intake_coefficient)), 'higher')
          }
        ]
      },
      {
        title: '极速数据',
        items: [
          {
            label: '夹角平跑极速',
            values: selectedCars.map(car => shouldUseZeroAdvance(car) ? (car.angle_normal_speed || '暂无') : (car.angle_normal_speed_advance40 || '暂无')),
            highlights: this.getHighlights(selectedCars.map(car =>
              extractNumber(shouldUseZeroAdvance(car) ? car.angle_normal_speed : car.angle_normal_speed_advance40)
            ))
          },
          {
            label: '夹角氮气极速',
            values: selectedCars.map(car => shouldUseZeroAdvance(car) ? (car.angle_nitro_speed || '暂无') : (car.angle_nitro_speed_advance40 || '暂无')),
            highlights: this.getHighlights(selectedCars.map(car =>
              extractNumber(shouldUseZeroAdvance(car) ? car.angle_nitro_speed : car.angle_nitro_speed_advance40)
            ))
          }
        ]
      },
      {
        title: '加速性能',
        items: [
          {
            label: '平跑180提速',
            values: selectedCars.map(car => shouldUseZeroAdvance(car) ? (car.normal_180_acceleration || '暂无') : (car.normal_180_acceleration_advance40 || '暂无')),
            highlights: this.getHighlights(selectedCars.map(car =>
              extractNumber(shouldUseZeroAdvance(car) ? car.normal_180_acceleration : car.normal_180_acceleration_advance40)
            ), 'lower')
          },
          {
            label: '平跑极速提速',
            values: selectedCars.map(car => shouldUseZeroAdvance(car) ? (car.normal_speed_acceleration || '暂无') : (car.normal_speed_acceleration_advance40 || '暂无')),
            highlights: this.getHighlights(selectedCars.map(car =>
              extractNumber(shouldUseZeroAdvance(car) ? car.normal_speed_acceleration : car.normal_speed_acceleration_advance40)
            ), 'lower')
          },
          {
            label: '大喷250提速',
            values: selectedCars.map(car => shouldUseZeroAdvance(car) ? (car.nitro_250_acceleration || '暂无') : (car.nitro_250_acceleration_advance40 || '暂无')),
            highlights: this.getHighlights(selectedCars.map(car =>
              extractNumber(shouldUseZeroAdvance(car) ? car.nitro_250_acceleration : car.nitro_250_acceleration_advance40)
            ), 'lower')
          },
          {
            label: '大喷290提速',
            values: selectedCars.map(car => shouldUseZeroAdvance(car) ? (car.nitro_290_acceleration || '暂无') : (car.nitro_290_acceleration_advance40 || '暂无')),
            highlights: this.getHighlights(selectedCars.map(car =>
              extractNumber(shouldUseZeroAdvance(car) ? car.nitro_290_acceleration : car.nitro_290_acceleration_advance40)
            ), 'lower')
          }
        ]
      },
      {
        title: '操控属性',
        items: [
          {
            label: '最大转向',
            values: selectedCars.map(car => car.formatted_low_speed_steering),
            highlights: this.getHighlights(selectedCars.map(car => Number(car.low_speed_steering)))
          },
          {
            label: '最小转向',
            values: selectedCars.map(car => car.formatted_high_speed_steering),
            highlights: this.getHighlights(selectedCars.map(car => Number(car.high_speed_steering)))
          },
          {
            label: '漂移速率',
            values: selectedCars.map(car => car.drift_factor),
            highlights: this.getHighlights(selectedCars.map(car => Number(car.drift_factor)))
          },
          {
            label: '摩擦系数',
            values: selectedCars.map(car => car.friction_factor),
            highlights: this.getHighlights(selectedCars.map(car => Number(car.friction_factor)), 'higher', 'friction_factor')
          },
          {
            label: '漂移转向',
            values: selectedCars.map(car => car.drift_steering || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.drift_steering)))
          },
          {
            label: '漂移摆动',
            values: selectedCars.map(car => car.drift_swing || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.drift_swing)))
          },
          {
            label: '漂移反向',
            values: selectedCars.map(car => car.drift_reverse || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.drift_reverse)))
          },
          {
            label: '漂移回正',
            values: selectedCars.map(car => car.drift_correction || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.drift_correction)))
          }
        ]
      }
    ];

    // 只有当至少有一辆车拥有超级喷功能时，才添加超级喷属性组
    if (hasSuperNitro) {
      compareGroups.push({
        title: '超级喷属性',
        items: [
          {
            label: '超级喷强度',
            values: selectedCars.map(car => car.super_nitro_intensity || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.super_nitro_intensity)), 'higher')
          },
          {
            label: '超级喷时长',
            values: selectedCars.map(car => car.super_nitro_duration || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.super_nitro_duration)), 'higher')
          },
          {
            label: '超级喷触发条件',
            values: selectedCars.map(car => car.super_nitro_trigger_condition || '暂无'),
            highlights: [] // 非数值不需要高亮
          },
          {
            label: '夹角超级喷极速',
            values: selectedCars.map(car => car.angle_super_nitro_speed || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.angle_super_nitro_speed)), 'higher')
          },
          {
            label: '超级喷250提速',
            values: selectedCars.map(car => car.super_nitro_250_acceleration || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.super_nitro_250_acceleration)), 'lower')
          },
          {
            label: '超级喷290提速',
            values: selectedCars.map(car => car.super_nitro_290_acceleration || '暂无'),
            highlights: this.getHighlights(selectedCars.map(car => extractNumber(car.super_nitro_290_acceleration)), 'lower')
          }
        ]
      });
    }

    return compareGroups;
  },

  // 修改获取高亮颜色函数
  getHighlightColor(highlight) {
    switch (highlight) {
      case 'higher':
        return '#ff4d4f'; // 红色
      case 'lower':
        return '#52c41a'; // 绿色
      default:
        return '#333333';
    }
  },

  // 关闭对比弹窗
  closeCompare() {
    // 清理雷达图相关资源
    this.setData({
      showCompare: false,
      compareGroups: [],
      // 清空已选择的车辆和雷达图颜色
      radarColors: [],
      // 清空雷达图图片
      radarChartImage: '',
      // 重置Canvas显示状态
      showRadarCanvas: false
    });

    // 尝试清理Canvas内容
    try {
      const query = wx.createSelectorQuery();
      query.select('#radarChart')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            if (ctx) {
              // 清空整个画布
              ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
          }
        });
    } catch (error) {
      console.log('清理雷达图Canvas失败:', error);
    }

    // 检查是否需要滚动
    this.checkScrollable();
  },

  // 生成对比图片
  async generateCompareImage() {
    wx.showLoading({ title: '生成预览中...' });
    try {
      const { selectedCars, radarChartImage } = this.data;

      // 确保至少选择了两辆车
      if (selectedCars.length < 2) {
        wx.hideLoading();
        wx.showToast({
          title: '请至少选择两辆车进行对比',
          icon: 'none'
        });
        return;
      }

      // 检查是否有雷达图图片，如果没有则生成
      if (!radarChartImage) {
        console.log('没有找到雷达图图片，先生成雷达图...');
        try {
          await this.generateRadarChartImage();
          console.log('雷达图生成完成，继续生成对比图');
        } catch (error) {
          console.error('生成雷达图失败，继续生成对比图但不包含雷达图:', error);
        }
      }

      // 初始化画布
      const query = wx.createSelectorQuery();
      query.select('#compareCanvas')
        .fields({ node: true, size: true })
        .exec(async (res) => {
          if (!res[0] || !res[0].node) {
            throw new Error('获取画布失败');
          }

          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 获取设备信息
          const systemInfo = wx.getSystemInfoSync();
          const pixelRatio = systemInfo.pixelRatio;
          const screenWidth = systemInfo.screenWidth;

          // 限制最大画布宽度，避免在大屏设备上过宽
          const maxCanvasWidth = Math.min(screenWidth, 750); // 最大宽度限制为750px
          const canvasWidth = maxCanvasWidth;

          // 预估画布高度，确保足够大
          const carsCount = selectedCars.length;
          const dataGroupsCount = this.data.compareGroups ? this.data.compareGroups.length : 3;
          const dataItemsPerGroup = 8; // 每组最多显示8项

          // 1. 头部(标题等): ~100px
          // 2. 车辆图片: ~canvasWidth*0.25
          // 3. 雷达图: ~canvasWidth*0.8 + 图例高度(20px * carsCount)
          // 4. 表头和每组数据的高度: ~30px + (dataItemsPerGroup * 30px) * dataGroupsCount
          // 5. 其他边距和元素: ~200px
          const estimatedHeight = 100 + (canvasWidth * 0.25) + (canvasWidth * 0.8 + 20 * carsCount) + (30 + dataItemsPerGroup * 30) * dataGroupsCount + 200;

          // 确保画布足够大
          const isLargeScreen = screenWidth > 500; // iPad或PC等大屏设备
          // 使用固定的高宽比例，不再使用预估高度
          const canvasHeight = isLargeScreen ? canvasWidth * 5.5 : canvasWidth * 5.5;

          canvas.width = canvasWidth * pixelRatio;
          canvas.height = canvasHeight * pixelRatio;

          // 缩放上下文以适应设备像素比
          ctx.scale(pixelRatio, pixelRatio);

          // 绘制背景
          const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight);
          gradient.addColorStop(0, '#ffffff');
          gradient.addColorStop(1, '#f5f8fa');
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvasWidth, canvasHeight);

          // 绘制标题
          ctx.fillStyle = '#333333';
          ctx.font = 'bold 18px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText('赛车数据对比', canvasWidth / 2, 40);

          // 显示比较的车辆名称
          const carNames = selectedCars.map(car => car.name).join(' vs ');
          ctx.font = '14px sans-serif';
          ctx.fillText(carNames, canvasWidth / 2, 70);

          // 绘制分隔线
          ctx.strokeStyle = '#eeeeee';
          ctx.beginPath();
          ctx.moveTo(20, 90);
          ctx.lineTo(canvasWidth - 20, 90);
          ctx.stroke();

          // 加载赛车图片
          let currentY = 100;
          const carImgHeight = Math.min(canvasWidth * 0.25, 200); // 图片高度为屏幕宽度的1/4，但不超过200px
          const carWidth = canvasWidth / selectedCars.length;

          try {
            // 创建加载图片的Promise数组
            const loadImagesPromises = selectedCars.map(async (car, index) => {
              try {
                // 获取图片信息
                const imgInfo = await new Promise((resolve, reject) => {
                  wx.getImageInfo({
                    src: car.cachedImageUrl,
                    success: resolve,
                    fail: reject
                  });
                });

                // 创建图片对象
                const img = canvas.createImage();
                await new Promise((resolve, reject) => {
                  img.onload = resolve;
                  img.onerror = reject;
                  img.src = imgInfo.path;
                });

                // 计算图片位置，使其居中显示
                const imgWidth = carWidth * 0.8; // 图片宽度为每列宽度的80%
                const imgX = index * carWidth + (carWidth - imgWidth) / 2;
                const imgY = currentY;

                // 为图片绘制白色背景框
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(imgX, imgY, imgWidth, carImgHeight);

                // 绘制图片，保持原比例
                const aspectRatio = imgInfo.width / imgInfo.height;
                let drawWidth = imgWidth;
                let drawHeight = imgWidth / aspectRatio;

                // 如果计算的高度超过了设定的高度，则按高度计算宽度
                if (drawHeight > carImgHeight) {
                  drawHeight = carImgHeight;
                  drawWidth = drawHeight * aspectRatio;
                }

                // 调整位置使图片居中
                const offsetX = (imgWidth - drawWidth) / 2;
                const offsetY = (carImgHeight - drawHeight) / 2;

                // 绘制图片
                ctx.drawImage(img, imgX + offsetX, imgY + offsetY, drawWidth, drawHeight);

                // 绘制车辆级别
                const levelBgWidth = 30;
                const levelBgHeight = 20;
                const levelBgX = imgX + imgWidth - levelBgWidth;
                const levelBgY = imgY + carImgHeight - levelBgHeight;

                // 处理级别文本，提取出实际级别（S、T3等）
                const actualLevel = car.level.split('（')[0].split('(')[0].trim();

                ctx.fillStyle = this.getLevelColor(actualLevel);
                ctx.fillRect(levelBgX, levelBgY, levelBgWidth, levelBgHeight);

                ctx.fillStyle = '#FFFFFF';
                ctx.font = '14px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText(actualLevel, levelBgX + levelBgWidth / 2, levelBgY + 15);

              } catch (error) {
                console.error('加载赛车图片失败:', error);
                // 加载失败时显示占位文本
                const imgWidth = carWidth * 0.8;
                const imgX = index * carWidth + (carWidth - imgWidth) / 2;
                const imgY = currentY;

                ctx.fillStyle = '#F5F5F5';
                ctx.fillRect(imgX, imgY, imgWidth, carImgHeight);

                ctx.fillStyle = '#999999';
                ctx.font = '14px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('图片加载失败', imgX + imgWidth / 2, imgY + carImgHeight / 2);
              }
            });

            // 等待所有图片加载完成
            await Promise.all(loadImagesPromises);

          } catch (err) {
            console.error('加载图片过程出错:', err);
          }

          // 更新当前Y坐标位置
          currentY += carImgHeight + 30;

          // 绘制雷达图标题
          ctx.fillStyle = '#4a90e2';
          ctx.font = 'bold 16px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText('性能雷达图', canvasWidth / 2, currentY);
          currentY += 30;

          // 绘制雷达图
          const radarHeight = canvasWidth * 0.8; // 雷达图高度为屏幕宽度的80%
          const radarWidth = canvasWidth * 0.9; // 雷达图宽度为屏幕宽度的90%
          const radarX = (canvasWidth - radarWidth) / 2;
          const radarY = currentY;

          // 绘制雷达图背景
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(radarX, radarY, radarWidth, radarHeight);

          // 获取当前雷达图
          let radarImage = null;
          if (this.data.radarChartImage) {
            try {
              // 加载雷达图图片
              const radarInfo = await new Promise((resolve, reject) => {
                wx.getImageInfo({
                  src: this.data.radarChartImage,
                  success: resolve,
                  fail: reject
                });
              });

              // 创建图片对象
              const img = canvas.createImage();
              await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
                img.src = radarInfo.path;
              });

              // 绘制雷达图
              ctx.drawImage(img, radarX, radarY, radarWidth, radarHeight);

            } catch (error) {
              console.error('加载雷达图失败:', error);
              // 如果加载失败，使用当前的compareGroups生成新的雷达图
              // 直接绘制一个雷达图的占位图
              ctx.fillStyle = '#f5f5f5';
              ctx.fillRect(radarX, radarY, radarWidth, radarHeight);
              ctx.fillStyle = '#999999';
              ctx.font = '14px sans-serif';
              ctx.textAlign = 'center';
              ctx.fillText('雷达图加载失败', radarX + radarWidth/2, radarY + radarHeight/2);
            }
          } else {
            // 如果没有雷达图图片，直接绘制一个雷达图的占位图
            ctx.fillStyle = '#f5f5f5';
            ctx.fillRect(radarX, radarY, radarWidth, radarHeight);
            ctx.fillStyle = '#999999';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('雷达图未生成', radarX + radarWidth/2, radarY + radarHeight/2);
          }

          // 绘制雷达图图例
          const legendY = radarY + radarHeight + 10;
          ctx.textAlign = 'center';

          selectedCars.forEach((car, index) => {
            const x = canvasWidth / 2;
            const colorBoxSize = 14;
            const colorBoxY = legendY + index * 20;

            // 绘制颜色方块
            const color = ['#4a90e2', '#ff6b6b', '#50e3c2', '#f5a623', '#9013fe'][index % 5];
            ctx.fillStyle = color;
            ctx.fillRect(x - 70, colorBoxY, colorBoxSize, colorBoxSize);

            // 绘制车名
            ctx.fillStyle = '#666666';
            ctx.font = '12px sans-serif';
            ctx.textAlign = 'left';

            // 如果车名过长，截断处理
            let carName = car.name;
            ctx.measureText(carName).width > 120 && (carName = carName.substring(0, 10) + '...');
            ctx.fillText(carName, x - 50, colorBoxY + 11);
          });

          // 更新当前Y坐标位置，为数据表格留出空间
          currentY = legendY + selectedCars.length * 20 + 30;

          // 获取关键对比数据
          const { compareGroups } = this.data;

          // 在顶部绘制车名表头（只绘制一次）
          const colWidth = (canvasWidth - 130) / selectedCars.length;
          ctx.fillStyle = '#333333';
          ctx.font = 'bold 14px sans-serif';
          ctx.textAlign = 'left';
          ctx.fillText('赛车名称', 30, currentY);

          // 处理长车名，避免重叠
          selectedCars.forEach((car, idx) => {
            ctx.fillStyle = '#333333';

            // 获取原始车名
            const originalName = car.name;
            let displayName = originalName;
            let fontSize = 14;

            // 计算文本宽度
            ctx.font = `bold ${fontSize}px sans-serif`;
            let textWidth = ctx.measureText(displayName).width;

            // 如果文本宽度超过列宽的80%，尝试缩小字体
            if (textWidth > colWidth * 0.8) {
              // 先尝试缩小字体到最小10px
              while (textWidth > colWidth * 0.8 && fontSize > 10) {
                fontSize -= 1;
                ctx.font = `bold ${fontSize}px sans-serif`;
                textWidth = ctx.measureText(displayName).width;
              }

              // 如果缩小字体后仍然太长，截断文本
              if (textWidth > colWidth * 0.8) {
                // 计算可以显示的字符数
                let truncateLength = displayName.length;
                while (truncateLength > 3 && ctx.measureText(displayName.substring(0, truncateLength) + '...').width > colWidth * 0.8) {
                  truncateLength--;
                }

                // 如果需要截断，加上省略号
                if (truncateLength < displayName.length) {
                  displayName = displayName.substring(0, truncateLength) + '...';
                }
              }
            }

            ctx.font = `bold ${fontSize}px sans-serif`;
            ctx.textAlign = 'center';
            ctx.fillText(displayName, 130 + colWidth * idx + colWidth / 2, currentY);

            // 如果截断了名称，在下方绘制小序号标记
            if (displayName !== originalName) {
              ctx.font = '10px sans-serif';
              ctx.fillText(`(#${idx+1})`, 130 + colWidth * idx + colWidth / 2, currentY + 15);
            }
          });
          currentY += 30;

          // 记录需要被截断的车名，以便后续添加图例
          let truncatedCars = [];
          selectedCars.forEach((car, idx) => {
            ctx.font = 'bold 14px sans-serif';
            const textWidth = ctx.measureText(car.name).width;
            if (textWidth > colWidth * 0.8) {
              truncatedCars.push({
                index: idx + 1,
                name: car.name
              });
            }
          });

          // 绘制分隔线
          ctx.strokeStyle = '#eeeeee';
          ctx.beginPath();
          ctx.moveTo(20, currentY - 15);
          ctx.lineTo(canvasWidth - 20, currentY - 15);
          ctx.stroke();

          // 绘制所有分组的数据
          for (let i = 0; i < compareGroups.length; i++) {
            const group = compareGroups[i];

            // 绘制组标题
            ctx.fillStyle = '#4a90e2';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(group.title, 30, currentY);
            currentY += 30;

            // 绘制每个数据项（最多显示8个）
            const itemsToShow = group.items.slice(0, 8);

            // 绘制数据行
            itemsToShow.forEach(item => {
              // 绘制属性名 - 统一使用相同样式，不再对第一个属性特殊处理
              ctx.fillStyle = '#666666';
              ctx.font = '14px sans-serif'; // 统一使用常规字体，不加粗
              ctx.textAlign = 'left';
              ctx.fillText(item.label, 30, currentY);

              // 绘制数值
              item.values.forEach((value, valueIdx) => {
                const x = 130 + colWidth * valueIdx + colWidth / 2;

                if (item.highlights && item.highlights[valueIdx]) {
                  ctx.fillStyle = item.highlights[valueIdx] === 'higher' ? '#ff4d4f' : '#52c41a';
                  ctx.font = 'bold 14px sans-serif';
                } else {
                  ctx.fillStyle = '#333333';
                  ctx.font = '14px sans-serif';
                }

                ctx.textAlign = 'center';
                ctx.fillText(value, x, currentY);
              });

              currentY += 30;
            });
          }

          // 更新当前Y坐标
          currentY += 160;

          // 添加浅色水印
          ctx.save();
          ctx.globalAlpha = 0.15; // 设置透明度为15%，确保水印可见
          ctx.font = 'bold 24px sans-serif';
          ctx.fillStyle = '#4a90e2'; // 使用蓝色
          ctx.textAlign = 'center';
          ctx.translate(canvasWidth / 2, canvasHeight / 2);
          ctx.rotate(-Math.PI / 6); // 旋转文字

          // 绘制多行水印
          const rows = Math.ceil(canvasHeight / 200) * 2; // 确保足够覆盖画布
          const cols = Math.ceil(canvasWidth / 300) * 2;

          for (let y = -rows; y <= rows; y++) {
            for (let x = -cols; x <= cols; x++) {
              ctx.fillText('飞车图鉴', x * 300, y * 200);
            }
          }

          ctx.restore();

          // 转换为图片
          wx.canvasToTempFilePath({
            canvas,
            success: res => {
              wx.hideLoading();

              // 使用预览图片功能，让用户可以自行选择保存
              wx.previewImage({
                urls: [res.tempFilePath],
                current: res.tempFilePath,
                showmenu: true, // 显示菜单，用户可以选择保存图片
                success: () => {
                  console.log('预览图片成功');
                },
                fail: err => {
                  console.error('预览图片失败:', err);
                  wx.showToast({
                    title: '预览失败',
                    icon: 'none'
                  });
                }
              });
            },
            fail: err => {
              console.error('生成图片失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '生成图片失败',
                icon: 'none'
              });
            }
          });
        });
    } catch (error) {
      console.error('生成图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '生成图片失败',
        icon: 'none'
      });
    }
  },

  // 获取级别颜色
  getLevelColor(level) {
    // 根据级别返回对应的颜色，与wxss样式保持一致
    const colorMap = {
      'S': 'rgba(147, 112, 219, 0.9)',  // 紫色
      'A': 'rgba(255, 165, 0, 0.9)',    // 橙色
      'B': 'rgba(102, 187, 106, 0.9)',  // 绿色
      'C': 'rgba(30, 144, 255, 0.9)',   // 蓝色
      'D': 'rgba(169, 169, 169, 0.9)',  // 灰色
      'T3': 'rgba(255, 140, 0, 0.9)',   // T系列橙色
      'T2': 'rgba(255, 140, 0, 0.9)',   // T系列橙色
      'T1': 'rgba(255, 140, 0, 0.9)'    // T系列橙色
    };
    return colorMap[level] || 'rgba(169, 169, 169, 0.9)';
  },

  // 生成单个赛车详细数据预览图片
  async generateSingleCarImage() {
    const { expandedIndex, filteredCars } = this.data;
    if (expandedIndex === null) return;

    const car = filteredCars[expandedIndex];

    wx.showLoading({ title: '生成预览中...' });
    try {
      // 初始化画布
      const query = wx.createSelectorQuery();
      query.select('#compareCanvas')
        .fields({ node: true, size: true })
        .exec(async (res) => {
          if (!res[0] || !res[0].node) {
            throw new Error('获取画布失败');
          }

          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 获取设备信息
          const windowInfo = wx.getWindowInfo();
          const pixelRatio = windowInfo.pixelRatio;
          const screenWidth = windowInfo.windowWidth;

          // 限制最大画布宽度，避免在大屏设备上过宽
          const maxCanvasWidth = Math.min(screenWidth, 750); // 最大宽度限制为750px
          const canvasWidth = maxCanvasWidth;

          // 预先估算所需的画布高度
          // 基本固定内容高度 + 各种数据行高度
          const isLargeScreen = screenWidth > 500; // iPad或PC等大屏设备
          // 1.头部(标题等): ~100px
          // 2.车辆图片: ~canvasWidth*0.35
          // 3.基础和操控属性: ~150px
          // 4.极速数据: ~150px
          // 5.加速性能: ~200px
          // 6.超级喷属性: ~200px
          // 7.底部边距: ~100px
          const estimatedHeight = 100 + Math.min(canvasWidth * 0.35, 300) + 150 + 150 + 200 + 200 + 100;
          // 确保画布足够大，提高高宽比例
          const canvasHeight = isLargeScreen ? Math.max(estimatedHeight, canvasWidth * 3.2) : Math.max(estimatedHeight, canvasWidth * 2.8);

          // 使用更高的分辨率，确保清晰度
          const scaleFactor = Math.max(pixelRatio, 2); // 确保至少2倍分辨率

          // 设置画布尺寸
          canvas.width = canvasWidth * scaleFactor;
          canvas.height = canvasHeight * scaleFactor;

          // 缩放上下文以适应设备像素比
          ctx.scale(scaleFactor, scaleFactor);

          // 绘制背景
          const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight);
          gradient.addColorStop(0, '#ffffff');
          gradient.addColorStop(1, '#f5f8fa');
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvasWidth, canvasHeight);

          // 绘制标题
          ctx.fillStyle = '#333333';
          ctx.font = 'bold 18px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText('赛车详细数据', canvasWidth / 2, 40);

          // 提取实际级别（S、T3等）
          const actualLevel = car.level.split('（')[0].split('(')[0].trim();

          // 显示车辆名称和级别
          ctx.font = '16px sans-serif';
          ctx.fillText(`${car.name} (${actualLevel})`, canvasWidth / 2, 70);

          // 绘制分隔线
          ctx.strokeStyle = '#eeeeee';
          ctx.beginPath();
          ctx.moveTo(20, 90);
          ctx.lineTo(canvasWidth - 20, 90);
          ctx.stroke();

          // 加载赛车图片
          let currentY = 100;
          // 根据画布宽度计算图片高度，保持比例
          const carImgHeight = Math.min(canvasWidth * 0.35, 300); // 设置最大高度限制

          try {
            // 获取图片信息
            const imgInfo = await new Promise((resolve, reject) => {
              wx.getImageInfo({
                src: car.cachedImageUrl,
                success: resolve,
                fail: reject
              });
            });

            // 创建图片对象
            const img = canvas.createImage();
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = reject;
              img.src = imgInfo.path;
            });

            // 计算图片位置，使其居中显示
            const imgWidth = canvasWidth * 0.8; // 图片宽度为画布宽度的80%
            const imgX = (canvasWidth - imgWidth) / 2;
            const imgY = currentY;

            // 为图片绘制白色背景框
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(imgX, imgY, imgWidth, carImgHeight);

            // 绘制图片，保持原比例
            const aspectRatio = imgInfo.width / imgInfo.height;
            let drawWidth = imgWidth;
            let drawHeight = imgWidth / aspectRatio;

            // 如果计算的高度超过了设定的高度，则按高度计算宽度
            if (drawHeight > carImgHeight) {
              drawHeight = carImgHeight;
              drawWidth = drawHeight * aspectRatio;
            }

            // 调整位置使图片居中
            const offsetX = (imgWidth - drawWidth) / 2;
            const offsetY = (carImgHeight - drawHeight) / 2;

            // 绘制图片
            ctx.drawImage(img, imgX + offsetX, imgY + offsetY, drawWidth, drawHeight);

            // 绘制车辆级别标签
            const levelBgWidth = 50;
            const levelBgHeight = 30;
            const levelBgX = imgX + imgWidth - levelBgWidth - 10;
            const levelBgY = imgY + 10;

            // 使用getLevelColor函数获取对应级别的颜色
            ctx.fillStyle = this.getLevelColor(actualLevel);
            ctx.fillRect(levelBgX, levelBgY, levelBgWidth, levelBgHeight);

            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(actualLevel, levelBgX + levelBgWidth / 2, levelBgY + 20);

          } catch (error) {
            console.error('加载赛车图片失败:', error);
            // 加载失败时显示占位文本
            currentY += 20;
            ctx.fillStyle = '#999999';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('图片加载失败', canvasWidth / 2, currentY);
            currentY += carImgHeight;
          }

          // 更新当前Y坐标位置
          currentY += carImgHeight + 20;

          // 使用双列布局显示基础属性和操控属性
          const columnWidth = (canvasWidth - 40) / 2;

          // 创建统一的标题绘制函数，确保对齐
          const drawSectionTitle = (title, x, y, align = 'left') => {
            ctx.fillStyle = '#4a90e2';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = align;
            ctx.fillText(title, x, y);
            return y + 25; // 返回下一行的Y坐标
          };

          // 绘制基础属性（左列）
          const leftTitleY = drawSectionTitle('基础属性', 20, currentY);

          // 绘制操控属性（右列）
          const rightTitleY = drawSectionTitle('操控属性', canvasWidth / 2 + 10, currentY);

          // 确保两边的内容从相同的Y坐标开始
          currentY = Math.max(leftTitleY, rightTitleY);

          // 左侧：基础属性内容
          const attrs = [
            { label: '车重', value: car.weight || '未知' },
            { label: '宝石槽', value: car.gem_slots || '无' },
            { label: '悬挂', value: car.suspension && car.suspension !== '0' ? `悬挂${car.suspension}` : '无' }
          ];

          // 右侧：操控属性内容
          const controlAttrs = [
            { label: '最大转向', value: car.formatted_low_speed_steering || '未知' },
            { label: '最小转向', value: car.formatted_high_speed_steering || '未知' },
            { label: '漂移速率', value: car.drift_factor || '未知' },
            { label: '摩擦系数', value: car.friction_factor || '未知' }
          ];

          // 计算需要显示的行数（取两列中的最大值）
          const rows = Math.max(attrs.length, controlAttrs.length);

          // 绘制属性行
          for (let i = 0; i < rows; i++) {
            // 左列：基础属性
            if (i < attrs.length) {
              ctx.fillStyle = '#666666';
              ctx.font = '14px sans-serif';
              ctx.textAlign = 'left';
              ctx.fillText(attrs[i].label, 20, currentY);

              ctx.fillStyle = '#333333';
              ctx.textAlign = 'right';
              ctx.fillText(attrs[i].value, canvasWidth / 2 - 10, currentY);
            }

            // 右列：操控属性
            if (i < controlAttrs.length) {
              ctx.fillStyle = '#666666';
              ctx.font = '14px sans-serif';
              ctx.textAlign = 'left';
              ctx.fillText(controlAttrs[i].label, canvasWidth / 2 + 10, currentY);

              ctx.fillStyle = '#333333';
              ctx.textAlign = 'right';
              ctx.fillText(controlAttrs[i].value, canvasWidth - 20, currentY);
            }

            currentY += 23;
          }

          // 绘制漂移操控和燃料/点火/进气标题，确保在同一行并对齐
          currentY += 10;
          const driftTitleY = drawSectionTitle('漂移操控', 20, currentY);
          const fuelTitleY = drawSectionTitle('燃料/点火/进气', canvasWidth / 2 + 10, currentY);

          // 确保从相同的Y坐标开始
          currentY = Math.max(driftTitleY, fuelTitleY);

          const driftAttrs = [
            { label: '漂移转向', value: car.drift_steering || '未知' },
            { label: '漂移摆动', value: car.drift_swing || '未知' },
            { label: '漂移反向', value: car.drift_reverse || '未知' },
            { label: '漂移回正', value: car.drift_correction || '未知' }
          ];

          // 绘制漂移属性行，单列显示
          driftAttrs.forEach(attr => {
            ctx.fillStyle = '#666666';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(attr.label, 20, currentY);

            ctx.fillStyle = '#333333';
            ctx.textAlign = 'right';
            ctx.fillText(attr.value, canvasWidth / 2 - 10, currentY);

            currentY += 23;
          });

          const fuelAttrs = [
            { label: '满改燃料时长', value: car.fuel_duration || '未知' },
            { label: '满改燃料强度', value: car.fuel_intensity ? (car.fuel_intensity/1000).toFixed(2) : '未知' },
            { label: '满改点火时长', value: car.ignition_duration || '未知' },
            { label: '满改点火强度', value: car.ignition_intensity ? (car.ignition_intensity/1000).toFixed(2) : '未知' },
            { label: '原装进气系数', value: car.original_intake_coefficient ? `${(car.original_intake_coefficient * 100).toFixed(2)}%` : '未知' },
            { label: '满改进气系数', value: car.intake_coefficient ? `${(car.intake_coefficient * 100).toFixed(2)}%` : '未知' }
          ];

          // 燃料属性绘制位置调整
          let fuelY = currentY - 23 * driftAttrs.length + 30;

          // 绘制燃料属性行，在右侧列显示
          fuelAttrs.forEach(attr => {
            ctx.fillStyle = '#666666';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(attr.label, canvasWidth / 2 + 10, fuelY);

            ctx.fillStyle = '#333333';
            ctx.textAlign = 'right';
            ctx.fillText(attr.value, canvasWidth - 20, fuelY);

            fuelY += 23;
          });

          // 确保Y坐标是最大的那一侧
          currentY = Math.max(currentY, fuelY);
          currentY += 20;

          // 绘制分隔线
          ctx.strokeStyle = '#eeeeee';
          ctx.beginPath();
          ctx.moveTo(20, currentY - 10);
          ctx.lineTo(canvasWidth - 20, currentY - 10);
          ctx.stroke();

          // 使用函数绘制标题
          currentY = drawSectionTitle('极速数据', 20, currentY + 10);

          // 绘制表头
          const headerY = currentY;
          ctx.fillStyle = '#666666';
          ctx.font = '14px sans-serif';
          ctx.textAlign = 'left';
          ctx.fillText('属性', 20, currentY);

          ctx.textAlign = 'center';
          ctx.fillText('0推进', canvasWidth * 0.4, currentY);
          ctx.fillText('40推进', canvasWidth * 0.7, currentY);
          currentY += 23;

          // 绘制极速数据内容
          const speedData = [
            {
              label: '夹角平跑极速',
              value0: car.angle_normal_speed || '未知',
              value40: car.angle_normal_speed_advance40 || '未知'
            },
            {
              label: '夹角氮气极速',
              value0: car.angle_nitro_speed || '未知',
              value40: car.angle_nitro_speed_advance40 || '未知'
            }
          ];

          speedData.forEach(data => {
            ctx.fillStyle = '#666666';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(data.label, 20, currentY);

            ctx.fillStyle = '#333333';
            ctx.textAlign = 'center';
            ctx.fillText(data.value0, canvasWidth * 0.4, currentY);

            ctx.fillStyle = '#ff6b6b';
            ctx.fillText(data.value40, canvasWidth * 0.7, currentY);

            currentY += 23;
          });

          currentY += 20;

          // 绘制分隔线
          ctx.strokeStyle = '#eeeeee';
          ctx.beginPath();
          ctx.moveTo(20, currentY - 10);
          ctx.lineTo(canvasWidth - 20, currentY - 10);
          ctx.stroke();

          // 使用函数绘制加速性能标题
          currentY = drawSectionTitle('加速性能', 20, currentY + 10);

          // 绘制表头
          ctx.fillStyle = '#666666';
          ctx.font = '14px sans-serif';
          ctx.textAlign = 'left';
          ctx.fillText('属性', 20, currentY);

          ctx.textAlign = 'center';
          ctx.fillText('0推进', canvasWidth * 0.4, currentY);
          ctx.fillText('40推进', canvasWidth * 0.7, currentY);
          currentY += 23;

          // 绘制加速性能内容
          const accelData = [
            {
              label: '平跑180提速',
              value0: car.normal_180_acceleration || '未知',
              value40: car.normal_180_acceleration_advance40 || '未知',
              hasNumber0: car.normal_180_acceleration_hasNumber,
              hasNumber40: car.normal_180_acceleration_advance40_hasNumber
            },
            {
              label: '平跑极速提速',
              value0: car.normal_speed_acceleration || '未知',
              value40: car.normal_speed_acceleration_advance40 || '未知',
              hasNumber0: car.normal_speed_acceleration_hasNumber,
              hasNumber40: car.normal_speed_acceleration_advance40_hasNumber
            },
            {
              label: '大喷250提速',
              value0: car.nitro_250_acceleration || '未知',
              value40: car.nitro_250_acceleration_advance40 || '未知',
              hasNumber0: car.nitro_250_acceleration_hasNumber,
              hasNumber40: car.nitro_250_acceleration_advance40_hasNumber
            },
            {
              label: '大喷290提速',
              value0: car.nitro_290_acceleration || '未知',
              value40: car.nitro_290_acceleration_advance40 || '未知',
              hasNumber0: car.nitro_290_acceleration_hasNumber,
              hasNumber40: car.nitro_290_acceleration_advance40_hasNumber
            }
          ];

          accelData.forEach(data => {
            ctx.fillStyle = '#666666';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(data.label, 20, currentY);

            ctx.fillStyle = '#333333';
            ctx.textAlign = 'center';
            ctx.fillText(data.value0 + (data.hasNumber0 ? '秒' : ''), canvasWidth * 0.4, currentY);

            ctx.fillStyle = '#ff6b6b';
            ctx.fillText(data.value40 + (data.hasNumber40 ? '秒' : ''), canvasWidth * 0.7, currentY);

            currentY += 23;
          });

          // 添加超级喷属性部分
          currentY += 20;

          // 检查是否有超级喷数据
          const hasSuperNitro =
            (car.super_nitro_intensity && car.super_nitro_intensity !== '未知' && car.super_nitro_intensity !== '暂无') ||
            (car.super_nitro_duration && car.super_nitro_duration !== '未知' && car.super_nitro_duration !== '暂无') ||
            (car.super_nitro_trigger_condition && car.super_nitro_trigger_condition !== '未知' && car.super_nitro_trigger_condition !== '暂无') ||
            (car.angle_super_nitro_speed && car.angle_super_nitro_speed !== '未知' && car.angle_super_nitro_speed !== '暂无') ||
            (car.super_nitro_250_acceleration && car.super_nitro_250_acceleration !== '未知' && car.super_nitro_250_acceleration !== '缺' && !car.super_nitro_250_acceleration.includes('秒')) ||
            (car.super_nitro_290_acceleration && car.super_nitro_290_acceleration !== '未知' && car.super_nitro_290_acceleration !== '缺' && !car.super_nitro_290_acceleration.includes('秒'));

          // 只有当有超级喷数据时才显示超级喷区域
          if (hasSuperNitro) {
            // 绘制分隔线
            ctx.strokeStyle = '#eeeeee';
            ctx.beginPath();
            ctx.moveTo(20, currentY - 10);
            ctx.lineTo(canvasWidth - 20, currentY - 10);
            ctx.stroke();

            // 使用函数绘制超级喷属性标题
            currentY = drawSectionTitle('超级喷属性', 20, currentY + 10);

            // 超级喷基本属性
            const sNitroBasicAttrs = [
              { label: '超级喷强度', value: car.super_nitro_intensity || '未知' },
              { label: '超级喷时长', value: car.super_nitro_duration || '未知' },
              { label: '触发条件', value: car.super_nitro_trigger_condition || '未知' }
            ];

            // 绘制超级喷基本属性（左侧）
            sNitroBasicAttrs.forEach((attr, index) => {
              ctx.fillStyle = '#666666';
              ctx.font = '14px sans-serif';
              ctx.textAlign = 'left';
              ctx.fillText(attr.label, 20, currentY);

              ctx.fillStyle = '#333333';
              ctx.textAlign = 'right';
              ctx.fillText(attr.value, canvasWidth / 2 - 20, currentY);

              currentY += 23;
            });

            // 重置Y坐标，准备绘制右侧
            const sNitroRightStartY = currentY - 23 * sNitroBasicAttrs.length;

            // 超级喷性能数据
            const sNitroPerformData = [
              { label: '夹角超级喷极速', value: car.angle_super_nitro_speed || '未知' },
              { label: '超级喷250提速', value: car.super_nitro_250_acceleration || '未知', hasNumber: car.super_nitro_250_acceleration && car.super_nitro_250_acceleration !== '未知' && car.super_nitro_250_acceleration !== '缺' && !car.super_nitro_250_acceleration.includes('秒') },
              { label: '超级喷290提速', value: car.super_nitro_290_acceleration || '未知', hasNumber: car.super_nitro_290_acceleration && car.super_nitro_290_acceleration !== '未知' && car.super_nitro_290_acceleration !== '缺' && !car.super_nitro_290_acceleration.includes('秒') }
            ];

            // 绘制超级喷性能数据（右侧）
            let sNitroRightY = sNitroRightStartY;
            sNitroPerformData.forEach(attr => {
              ctx.fillStyle = '#666666';
              ctx.font = '14px sans-serif';
              ctx.textAlign = 'left';
              ctx.fillText(attr.label, canvasWidth / 2 + 20, sNitroRightY);

              ctx.fillStyle = '#333333';
              ctx.textAlign = 'right';
              // 添加"秒"单位（如果需要）
              let displayValue = attr.value;
              if (attr.hasNumber) {
                displayValue += '秒';
              }
              ctx.fillText(displayValue, canvasWidth - 20, sNitroRightY);

              sNitroRightY += 23;
            });

            // 确保Y坐标是最大的那一侧
            currentY = Math.max(currentY, sNitroRightY);
          }

          currentY += 20; // 增加额外的间距

          // 检查是否内容超出画布高度，如果超出则从底部预留空间绘制鸣谢
          const finalTextY = currentY + 40 > canvasHeight - 40 ? currentY + 40 : canvasHeight - 40;

          // 添加底部鸣谢文本
          ctx.fillStyle = '#999999';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText('感谢白熊猫、地平线、路歌客心、沉沦的遗迹、RabbitTrap提供数据支持！', canvasWidth / 2, finalTextY);

          // 添加浅色水印
          ctx.save();
          ctx.globalAlpha = 0.08;
          ctx.font = '20px sans-serif';
          ctx.fillStyle = '#666666';
          ctx.textAlign = 'center';
          ctx.translate(canvasWidth / 2, canvasHeight / 2);
          ctx.rotate(-Math.PI / 6);

          // 绘制多行水印以覆盖整个画布
          for (let y = -canvasHeight; y < canvasHeight; y += 100) {
            for (let x = -canvasWidth; x < canvasWidth; x += 200) {
              ctx.fillText('飞车图鉴', x, y);
            }
          }

          ctx.restore();

          // 转换为图片
          wx.canvasToTempFilePath({
            canvas,
            success: res => {
              wx.hideLoading();

              // 使用预览图片功能，让用户可以自行选择保存
              wx.previewImage({
                urls: [res.tempFilePath],
                current: res.tempFilePath,
                showmenu: true, // 显示菜单，用户可以选择保存图片
                success: () => {
                  console.log('预览图片成功');
                },
                fail: err => {
                  console.error('预览图片失败:', err);
                  wx.showToast({
                    title: '预览失败',
                    icon: 'none'
                  });
                }
              });
            },
            fail: err => {
              console.error('生成图片失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '生成图片失败',
                icon: 'none'
              });
            }
          });
        });
    } catch (error) {
      console.error('生成图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '生成图片失败',
        icon: 'none'
      });
    }
  },

  showShareOptions() {
    this.setData({ showShareOptions: true });
  },

  // 关闭分享选项
  closeShareOptions() {
    this.setData({ showShareOptions: false });
  },

  // 防止点击穿透
  preventClose() {
    // 不需要参数，只需要阻止事件冒泡
    return false;
  },

  // 防止滚动穿透
  preventScroll() {
    return false;
  },


  // 修改获取画布上下文
  getCanvas() {
    return new Promise((resolve, reject) => {
      const query = wx.createSelectorQuery();
      query.select('#compareCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            // 获取设备信息
            const appBaseInfo = wx.getAppBaseInfo();
            const windowInfo = wx.getWindowInfo();

            // 设置画布大小（使用实际像素大小）
            const width = 350 * windowInfo.pixelRatio;
            const height = 300 * windowInfo.pixelRatio;

            canvas.width = width;
            canvas.height = height;

            // 缩放绘图上下文以适应设备像素比
            ctx.scale(windowInfo.pixelRatio, windowInfo.pixelRatio);

            resolve({ canvas, ctx });
          } else {
            reject(new Error('获取画布失败'));
          }
        });
    });
  },

  // 加载图片
  async loadImage(src) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: src,
        success: (res) => {
          const img = this.canvas.createImage();
          img.onload = () => resolve(img);
          img.onerror = reject;
          img.src = res.path;
        },
        fail: reject
      });
    });
  },

  // 将画布转换为图片
  canvasToImage() {
    return new Promise((resolve, reject) => {
      wx.canvasToTempFilePath({
        canvas: this.canvas,
        fileType: 'jpg',
        quality: 0.8,
        success: res => {
          this.setData({ shareImagePath: res.tempFilePath });
          resolve(res.tempFilePath);
        },
        fail: err => {
          console.error('生成图片失败:', err);
          reject(err);
        }
      });
    });
  },

  // 添加检查是否可滚动的函数
  checkScrollable() {
    const query = wx.createSelectorQuery();
    query.select('.car-info-content').boundingClientRect();
    query.select('.car-info-bar').boundingClientRect();
    query.exec(([content, container]) => {
      if (content && container) {
        const canScroll = content.width > container.width;
        if (canScroll) {
          // 通过 setData 来添加可滚动标识
          this.setData({
            carInfoBarCanScroll: true
          });
        }
      }
    });
  },

  // 获取搜索历史
  getSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      this.setData({ searchHistory: history });
    } catch (error) {
      console.error('获取搜索历史失败:', error);
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return;

    try {
      let history = wx.getStorageSync('searchHistory') || [];
      // 去重
      history = history.filter(item => item !== keyword);
      // 新搜索添加到开头
      history.unshift(keyword);
      // 最多保存10条
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      // 保存到本地存储
      wx.setStorageSync('searchHistory', history);
      this.setData({ searchHistory: history });
    } catch (error) {
      console.error('保存搜索历史失败:', error);
    }
  },

  // 清空搜索历史
  clearSearchHistory() {
    try {
      wx.removeStorageSync('searchHistory');
      this.setData({
        searchHistory: [],
        showSearchHistory: false
      });
      wx.showToast({
        title: '已清空历史记录',
        icon: 'success'
      });
    } catch (error) {
      console.error('清空搜索历史失败:', error);
    }
  },

  // 点击历史记录项
  onHistoryItemTap(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKey: keyword,
      showSearchHistory: false
    }, () => {
      this.onSearch();
    });
  },

  // 搜索输入框获取焦点事件
  onSearchFocus() {
    if (this.data.searchHistory.length > 0) {
      this.setData({ showSearchHistory: true });
    }
  },

  // 搜索输入框失去焦点事件
  onSearchBlur() {
    // 延迟隐藏,以便可以点击历史记录
    setTimeout(() => {
      this.setData({ showSearchHistory: false });
    }, 200);
  },

  // 发起需要认证的请求示例
  async makeAuthRequest() {
    try {
      const res = await app.makeAuthRequest(
        `${this.data.baseUrl}/api/some/endpoint/`,
        'POST',
        { someData: 'value' }
      )
      return res.data
    } catch(err) {
      console.error('请求失败:', err)
      throw err
    }
  },

  // 检查用户是否已评分
  async checkUserRating(carId, openid) {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.data.baseUrl}/api/cars/ratings/user/`,
          method: 'GET',
          data: {
            car_id: carId,
            openid: openid
          },
          success: res => resolve(res),
          fail: err => reject(err)
        });
      });

      console.log('获取用户评分结果:', res.data);

      if (res.data && (res.data.speed_rating || res.data.handling_rating || res.data.value_rating || res.data.combat_rating)) {
        // 确保所有评分都保留一位小数
        const speed = parseFloat(res.data.speed_rating).toFixed(1);
        const handling = parseFloat(res.data.handling_rating).toFixed(1);
        const value = parseFloat(res.data.value_rating).toFixed(1);
        const combat = parseFloat(res.data.combat_rating).toFixed(1);
        const appearance = parseFloat(res.data.appearance_rating).toFixed(1);

        this.setData({
          hasRated: true,
          userRating: {
            speed,
            handling,
            value,
            combat,
            appearance,
            overall: ((parseFloat(speed) * 0.25 + parseFloat(handling) * 0.25 +
                      parseFloat(value) * 0.15 + parseFloat(combat) * 0.20+parseFloat(appearance) * 0.15)).toFixed(1)
          },
          currentRating: {
            speed,
            handling,
            value,
            combat,
            appearance
          }
        });

        console.log('更新后的评分状态:', {
          hasRated: true,
          userRating: this.data.userRating,
          currentRating: this.data.currentRating
        });
      } else {
        this.setData({
          hasRated: false,
          userRating: null,
          currentRating: {
            speed: '5.0',
            handling: '5.0',
            value: '5.0',
            combat: '5.0',  // 默认值也保留一位小数
            appearance: '5.0'
          }
        });

        console.log('用户未评分，设置默认值');
      }
    } catch (error) {
      console.error('检查用户评分失败:', error);
      this.setData({
        hasRated: false,
        userRating: null,
        currentRating: {
          speed: '5.0',
          handling: '5.0',
          value: '5.0',
          combat: '5.0',  // 默认值也保留一位小数
          appearance: '5.0'
        }
      });
    }
  },

  // 显示评分弹窗
  async showRating() {
    const index = this.data.expandedIndex;
    if (index === -1) return;

    const car = this.data.filteredCars[index];
    if (!car) return;

    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 先获取用户评分
      await this.checkUserRating(car.car_id, openid);

      // 确保数据更新完成后再显示弹窗
      setTimeout(() => {
        this.setData({
          showRatingModal: true
        });
        console.log('显示评分弹窗时的评分状态:', this.data.currentRating);
      }, 100);

    } catch (error) {
      console.error('显示评分弹窗失败:', error);
      wx.showToast({
        title: '获取评分信息失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 星星点击处理函数
  onStarTap(e) {
    const { type, score } = e.currentTarget.dataset;

    console.log(`星星点击 - 类型: ${type}, 分数: ${score}`);

    this.setData({
      [`currentRating.${type}`]: parseFloat(score).toFixed(1)  // 保留一位小数
    }, () => {
      console.log('更新后的评分状态:', this.data.currentRating);
    });
  },

  // 提交评分
  async submitRating() {
    const index = this.data.expandedIndex;
    if (index === -1) return;

    const car = this.data.filteredCars[index];
    if (!car) return;

    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
      return;
    }

    const { speed, handling, value, combat, appearance } = this.data.currentRating;

    try {
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.data.baseUrl}/api/cars/ratings/submit/`,
          method: 'POST',
          data: {
            car_id: car.car_id,
            openid,
            speed_rating: parseFloat(speed).toFixed(1),
            handling_rating: parseFloat(handling).toFixed(1),
            value_rating: parseFloat(value).toFixed(1),
            combat_rating: parseFloat(combat).toFixed(1),  // 保留一位小数
            appearance_rating: parseFloat(appearance).toFixed(1)  // 保留一位小数
          },
          success: res => resolve(res),
          fail: err => reject(err)
        });
      });

      if (res.data && res.data.message) {
        wx.showToast({
          title: '评分成功',
          icon: 'success'
        });

        // 更新评分统计
        await this.fetchRatingStats(car.car_id);

        // 设置用户已评分状态和保存当前评分
        this.setData({
          hasRated: true,
          userRating: {
            speed: parseFloat(speed).toFixed(1),
            handling: parseFloat(handling).toFixed(1),
            value: parseFloat(value).toFixed(1),
            combat: parseFloat(combat).toFixed(1),  // 保留一位小数
            appearance: parseFloat(appearance).toFixed(1),
            overall: ((parseFloat(speed) * 0.25 + parseFloat(handling) * 0.25 +
                      parseFloat(value) * 0.15 + parseFloat(combat) * 0.2+parseFloat(appearance) * 0.15)).toFixed(1)
          }
        });

        // 关闭评分弹窗
        this.closeRating();
      }
    } catch (error) {
      console.error('提交评分失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  // 获取评分统计
  async fetchRatingStats(carId) {
    if (!carId) {
      console.error('缺少赛车ID');
      return;
    }

    try {
      console.log('获取评分统计，赛车ID:', carId);

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.data.baseUrl}/api/cars/ratings/${carId}/`,
          method: 'GET',
          success: res => resolve(res),
          fail: err => reject(err)
        });
      });

      if (res.data) {
        // 格式化评分数据，保留一位小数
        const formatRating = (value) => {
          if (!value || res.data.total_ratings === 0) return '--';
          return Number(value).toFixed(1);
        };

        const ratingStats = {
          ...res.data,
          overall_avg: formatRating(res.data.overall_avg),
          speed_avg: formatRating(res.data.speed_avg),
          handling_avg: formatRating(res.data.handling_avg),
          value_avg: formatRating(res.data.value_avg),
          combat_avg: formatRating(res.data.combat_avg),
          appearance_avg: formatRating(res.data.appearance_avg)  // 添加颜值评分，并保留一位小数
        };

        // 检查用户是否已评分
        const openid = wx.getStorageSync('openid');
        if (openid) {
          this.checkUserRating(carId, openid);
        }

        this.setData({
          ratingStats
        });
      }
    } catch (error) {
      console.error('获取评分统计失败:', error);
      wx.showToast({
        title: '获取评分失败',
        icon: 'none'
      });
    }
  },

  // 关闭评分弹窗
  closeRating() {
    // 不重置currentRating，保持当前的评分值
    this.setData({
      showRatingModal: false
    });
  },

  // 显示评论弹窗
  showCommentModal() {
    // 检查是否已登录
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    this.setData({ showCommentModal: true });
  },

  // 关闭评论弹窗
  closeCommentModal() {
    this.setData({
      showCommentModal: false,
      commentContent: '',
      editingCommentId: null
    });
    // 添加延时，确保过渡动画完成后再重置内容
    setTimeout(() => {
      if (!this.data.showCommentModal) {
        this.setData({
          commentContent: ''
        });
      }
    }, 300);
  },

  // 评论输入处理
  onCommentInput(e) {
    this.setData({
      commentContent: e.detail.value
    });
  },

  // 提交评论
  async submitComment() {
    const content = this.data.commentContent.trim();
    if (!content) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '提交中...' });
    try {
      // 调用内容审核服务
      const checkResult = await contentModeration.checkContent(content);
      if (!checkResult.success) {
        throw new Error(checkResult.error || '内容检测失败，请重试');
      }

      if (this.data.editingCommentId) {
        // 编辑评论
        const editRes = await new Promise((resolve, reject) => {
          wx.request({
            url: `${this.data.baseUrl}/api/comments/${this.data.editingCommentId}/edit/`,
            method: 'PUT',
            header: {
              'Content-Type': 'application/json',
              'X-Openid': openid
            },
            data: {
              content: content
            },
            success: res => resolve(res),
            fail: err => reject(err)
          });
        });

        if (editRes.data && editRes.data.success) {
          // 更新评论列表中的内容
          const comments = this.data.comments.map(item => {
            if (item.id === this.data.editingCommentId) {
              return {
                ...item,
                content: content,
                updated_at: editRes.data.data.updated_at
              };
            }
            return item;
          });

          this.setData({
            comments,
            showCommentModal: false,
            commentContent: '',
            editingCommentId: null
          });

          wx.hideLoading();
          wx.showToast({
            title: '修改成功',
            icon: 'success'
          });
        } else {
          throw new Error(editRes.data.error || '修改失败');
        }
      } else {
        // 新增评论
        const index = this.data.expandedIndex;
        if (index === -1) {
          wx.hideLoading();
          return;
        }

        const car = this.data.filteredCars[index];
        if (!car) {
          wx.hideLoading();
          return;
        }

        const res = await new Promise((resolve, reject) => {
          wx.request({
            url: `${this.data.baseUrl}/api/comments/create/`,
            method: 'POST',
            header: {
              'Content-Type': 'application/json',
              'X-Openid': openid
            },
            data: {
              car_id: car.car_id,
              content: content
            },
            success: res => resolve(res),
            fail: err => reject(err)
          });
        });

        if (res.data && res.data.success) {
          // 添加新评论到列表开头，对created_at进行格式化
          const newComment = {
            id: res.data.data.id,
            content: content,
            created_at: formatDateTime(res.data.data.created_at),  // 使用formatDateTime格式化时间
            nickname: this.getAnonymousName(openid),
            avatar: wx.getStorageSync('userInfo')?.avatarUrl,
            can_edit: true,
            can_delete: true,
            openid: openid
          };

          this.setData({
            comments: [newComment, ...this.data.comments],
            showCommentModal: false,
            commentContent: ''
          });

          wx.hideLoading();
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          });
        } else {
          throw new Error(res.data.error || '评论失败');
        }
      }
    } catch (error) {
      console.error('提交评论失败:', error);
      wx.hideLoading();
      wx.showModal({
        title: '提交失败',
        content: error.message || '操作失败，请重试',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }
  },

  // 加载评论列表
  async loadComments(carId, isRefresh = false) {
    try {
      if (isRefresh) {
        this.setData({
          commentPage: 1,
          comments: []
        });
      }

      if (!this.data.hasMoreComments && !isRefresh) return;

      const openid = wx.getStorageSync('openid');
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.data.baseUrl}/api/cars/${carId}/comments/`,
          method: 'GET',
          header: openid ? {
            'X-Openid': openid
          } : {},
          data: {
            page: this.data.commentPage,
            page_size: this.data.commentPageSize,
            openid: openid // 添加openid参数
          },
          success: res => resolve(res),
          fail: err => reject(err)
        });
      });

      if (res.data && res.data.success) {
        const { list, total, page, page_size } = res.data.data;

        // 处理评论数据
        const formattedComments = list.map(comment => ({
          id: comment.id,
          content: comment.content,
          created_at: formatDateTime(comment.created_at),
          nickname: this.getAnonymousName(comment.openid),
          avatar: comment.avatar || '/images/default-avatar.png',
          can_edit: comment.can_edit,
          can_delete: comment.can_delete,
          openid: comment.openid
        }));

        this.setData({
          comments: isRefresh ? formattedComments : [...this.data.comments, ...formattedComments],
          commentPage: page + 1,
          hasMoreComments: page * page_size < total
        });
      } else {
        throw new Error(res.data.error || '加载评论失败');
      }
    } catch (error) {
      console.error('加载评论失败:', error);
      wx.showToast({
        title: error.message || '加载评论失败',
        icon: 'none'
      });
    }
  },

  // 加载更多评论
  loadMoreComments() {
    const index = this.data.expandedIndex;
    if (index === -1) return;

    const car = this.data.filteredCars[index];
    if (!car) return;

    this.loadComments(car.car_id);
  },

  // 删除评论
  async deleteComment(e) {
    const commentId = e.currentTarget.dataset.id;
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      // 显示确认弹窗
      const res = await wx.showModal({
        title: '提示',
        content: '确定要删除这条评论吗？',
        confirmText: '删除',
        confirmColor: '#ff4d4f'
      });

      if (res.confirm) {
        wx.showLoading({ title: '删除中...' });

        const deleteRes = await new Promise((resolve, reject) => {
          wx.request({
            url: `${this.data.baseUrl}/api/comments/${commentId}/delete/`,
            method: 'DELETE',
            header: {
              'X-Openid': openid
            },
            success: res => resolve(res),
            fail: err => reject(err)
          });
        });

        wx.hideLoading();
        if (deleteRes.data && deleteRes.data.success) {
          const comments = this.data.comments.filter(item => item.id !== commentId);
          this.setData({ comments });

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        } else {
          throw new Error(deleteRes.data.error || '删除失败');
        }
      }
    } catch (error) {
      console.error('删除评论失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  // 显示编辑评论弹窗
  showEditCommentModal(e) {
    const commentId = e.currentTarget.dataset.id;
    const comment = this.data.comments.find(item => item.id === commentId);
    if (comment) {
      this.setData({
        showCommentModal: true,
        commentContent: comment.content,
        editingCommentId: commentId
      });
    }
  },

  // 举报评论
  async reportComment(e) {
    const commentId = e.currentTarget.dataset.id;
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      // 显示举报原因输入弹窗
      const reasonRes = await wx.showModal({
        title: '举报评论',
        content: '请输入举报原因',
        editable: true,
        placeholderText: '请输入举报原因（必填）',
      });

      if (reasonRes.confirm && reasonRes.content) {
        wx.showLoading({ title: '提交中...' });

        const reportRes = await new Promise((resolve, reject) => {
          wx.request({
            url: `${this.data.baseUrl}/api/comments/${commentId}/report/`,
            method: 'POST',
            header: {
              'Content-Type': 'application/json'
            },
            data: {
              reason: reasonRes.content,
              openid: openid  // 添加 openid 到请求参数中
            },
            success: res => resolve(res),
            fail: err => reject(err)
          });
        });

        wx.hideLoading();
        if (reportRes.data && reportRes.data.success) {
          wx.showToast({
            title: '举报成功',
            icon: 'success'
          });
        } else {
          throw new Error(reportRes.data.error || '举报失败');
        }
      }
    } catch (error) {
      console.error('举报评论失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '举报失败，请重试',
        icon: 'none'
      });
    }
  },

  // 获取匿名用户名称
  getAnonymousName(openid) {
    if (!this.data.anonymousUserMap[openid]) {
      this.setData({
        [`anonymousUserMap.${openid}`]: openid.substring(openid.length - 6),
        nextAnonymousId: this.data.nextAnonymousId + 1
      });
    }
    return `匿名用户${this.data.anonymousUserMap[openid]}`;
  },

  // 添加拖动相关方法
  onFeedbackBtnTouchStart(e) {
    if (e.touches.length !== 1) return;

    const touch = e.touches[0];
    this.startX = touch.clientX;
    this.startY = touch.clientY;
    this.offsetX = this.data.feedbackBtnLeft;
    this.offsetY = this.data.feedbackBtnTop;
    this.hasMoved = false;
  },

  onFeedbackBtnTouchMove(e) {
    if (e.touches.length !== 1) return;

    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - this.startX);
    const deltaY = Math.abs(touch.clientY - this.startY);

    // 如果移动距离小于阈值，不处理
    if (deltaX < 5 && deltaY < 5) return;

    this.hasMoved = true;
    const windowInfo = wx.getWindowInfo();

    let newLeft = this.offsetX + touch.clientX - this.startX;
    let newTop = this.offsetY + touch.clientY - this.startY;

    // 限制按钮在屏幕内
    newLeft = Math.max(0, Math.min(newLeft, windowInfo.windowWidth - 60));
    newTop = Math.max(0, Math.min(newTop, windowInfo.windowHeight - 60));

    this.setData({
      feedbackBtnLeft: newLeft,
      feedbackBtnTop: newTop,
      isDragging: true
    });
  },

  onFeedbackBtnTouchEnd() {
    if (this.hasMoved) {
      setTimeout(() => {
        this.setData({ isDragging: false });
        this.hasMoved = false;
      }, 50);
    }
  },

  onFeedbackBtnTap() {
    if (!this.hasMoved) {
      // 先移除权益标记，避免影响页面跳转性能
      const currentPrivilege = this.data.hasPriorityLoading;
      this.setData({ hasPriorityLoading: false }, () => {
        wx.navigateTo({
          url: '/pages/feedback/feedback',
          success: () => {
            // 跳转成功后恢复权益标记
            this.setData({ hasPriorityLoading: currentPrivilege });
          },
          fail: (err) => {
            console.error('导航到反馈页面失败:', err);
            // 恢复权益标记
            this.setData({ hasPriorityLoading: currentPrivilege });
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      });
    }
  },

  // 防止背景滚动
  preventTouchMove() {
    return false;
  },

  // 添加虚拟列表处理
  onCommentsScroll(e) {
    const scrollTop = e.detail.scrollTop;
    const screenHeight = wx.getSystemInfoSync().windowHeight;
    const startIndex = Math.max(0, Math.floor(scrollTop / this.data.commentItemHeight) - this.data.bufferSize);
    const endIndex = Math.min(
      this.data.comments.length,
      Math.ceil((scrollTop + screenHeight) / this.data.commentItemHeight) + this.data.bufferSize
    );

    this.setData({
      visibleComments: this.data.comments.slice(startIndex, endIndex).map(item => ({
        ...item,
        top: startIndex * this.data.commentItemHeight
      }))
    });
  },

  // 修改排序字段
  onSortFieldChange(e) {
    const index = e.detail.value;
    const option = this.data.sortOptions[index];

    // 根据文本长度动态设置字体大小
    const fontSize = this.calculateSortLabelFontSize(option.label);

    // 检查是否是提速时间相关字段，如果是则默认使用升序排序
    const timeOrderFields = [
      'normal_speed_acceleration', // 平跑极速时间
      'normal_180_acceleration',   // 平跑180时间
      'nitro_250_acceleration',    // 大喷250时间
      'nitro_290_acceleration',    // 大喷290时间
      'super_nitro_250_acceleration', // 超喷250提速
      'super_nitro_290_acceleration'  // 超喷290提速
    ];

    // 确定排序方向：如果是提速时间相关字段则默认升序(asc)，否则保持当前方向或默认降序
    const sortOrder = timeOrderFields.includes(option.field) ? 'asc' : this.data.sortOrder;

    this.setData({
      sortBy: option.field,
      currentSortLabel: option.label,
      sortLabelFontSize: fontSize,
      sortOrder: sortOrder, // 设置排序方向
      isFirstLoad: true,
      cars: [],
      filteredCars: []
    }, () => {
      this.fetchCars();
    });
  },

  // 根据文本长度计算合适的字体大小
  calculateSortLabelFontSize(text) {
    if (!text) return '28rpx';

    // 判断文本长度，动态设置字体大小
    if (text.length > 10) {
      return '22rpx';  // 超长文本使用更小的字体
    } else if (text.length > 7) {
      return '24rpx';  // 较长文本使用小字体
    } else {
      return '28rpx';  // 默认字体大小
    }
  },

  // 切换排序方向
  toggleSortDirection() {
    if (!this.data.sortBy) return;

    this.setData({
      sortOrder: this.data.sortOrder === 'desc' ? 'asc' : 'desc',
      isFirstLoad: true,
      cars: [],
      filteredCars: []
    }, () => {
      this.fetchCars();
    });
  },

  // 初始化画布
  async initCanvas() {
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const dpr = systemInfo.pixelRatio;
      const screenWidth = systemInfo.screenWidth;
      const screenHeight = systemInfo.screenHeight;

      // 计算画布实际尺寸(保持 16:9 的比例)
      const canvasWidth = screenWidth;
      const canvasHeight = Math.floor((screenWidth * 16) / 9);

      return new Promise((resolve, reject) => {
        const query = wx.createSelectorQuery();
        query.select('#compareCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0] && res[0].node) {
              const canvas = res[0].node;
              const ctx = canvas.getContext('2d');

              // 设置画布的物理像素大小
              canvas.width = canvasWidth * dpr;
              canvas.height = canvasHeight * dpr;

              // 缩放画布上下文以适应设备像素比
              ctx.scale(dpr, dpr);

              // 保存画布和上下文的引用
              this.canvas = canvas;
              this.ctx = ctx;

              // 保存画布尺寸信息
              this.canvasWidth = canvasWidth;
              this.canvasHeight = canvasHeight;

              resolve({ canvas, ctx });
            } else {
              reject(new Error('获取画布节点失败'));
            }
          });
      });
    } catch (error) {
      console.error('初始化画布失败:', error);
      throw error;
    }
  },

  // 导航到宠物图鉴
  navigateToPetGuide() {
    wx.navigateTo({
      url: '/pages/pet/pet'
    });
  },

  copyGroupNumber() {
    wx.setClipboardData({
      data: '2156036977',
      success: () => {
        wx.showToast({
          title: '群号已复制',
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 初始化雷达图
  async initRadarChart() {
    try {
      console.log('开始初始化雷达图');

      // 确保弹窗处于显示状态
      if (!this.data.showCompare) {
        console.log('对比弹窗未显示，不初始化雷达图');
        return;
      }

      // 检查是否有选中的车辆
      if (!this.data.selectedCars || this.data.selectedCars.length < 2) {
        console.error('选中的车辆不足，无法绘制雷达图');
        return;
      }
      console.log('选中的车辆数量:', this.data.selectedCars.length);

      // 获取雷达图画布和上下文
      const query = wx.createSelectorQuery();
      const canvasNode = await new Promise((resolve) => {
        query.select('#radarChart')
          .fields({ node: true, size: true })
          .exec((res) => {
            console.log('查询雷达图Canvas结果:', res);
            if (res && res[0] && res[0].node) {
              resolve(res[0]);
            } else {
              console.error('获取雷达图画布失败，返回结果:', res);
              resolve(null);
            }
          });
      });

      if (!canvasNode) {
        console.error('获取雷达图画布失败，无法绘制');
        return;
      }

      // 获取设备信息
      const systemInfo = wx.getSystemInfoSync();
      const dpr = systemInfo.pixelRatio;
      const screenWidth = systemInfo.screenWidth;

      // 设置Canvas尺寸
      const canvas = canvasNode.node;
      const ctx = canvas.getContext('2d');

      // 计算雷达图尺寸
      const canvasWidth = screenWidth * 0.9; // 控制雷达图宽度
      const canvasHeight = canvasWidth; // 保持雷达图为正方形

      // 设置Canvas尺寸，考虑设备像素比
      canvas.width = canvasWidth * dpr;
      canvas.height = canvasHeight * dpr;

      // 清空画布并缩放上下文
      ctx.scale(dpr, dpr);
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 绘制雷达图
      this.drawRadarChart(ctx, canvasWidth, canvasHeight);

      // 转换为静态图片
      setTimeout(async () => {
        try {
          const imagePath = await this.convertCanvasToImage(canvas);
          this.setData({
            radarChartImage: imagePath,
            showRadarCanvas: false
          });
          console.log('雷达图已转换为图片:', imagePath);
        } catch (error) {
          console.error('雷达图转换为图片失败:', error);
        }
      }, 300);
    } catch (error) {
      console.error('雷达图初始化失败:', error);
    }
  },

  // 将Canvas转换为静态图片
  convertCanvasToImage(canvas) {
    return new Promise((resolve, reject) => {
      wx.canvasToTempFilePath({
        canvas,
        success: res => {
          resolve(res.tempFilePath);
        },
        fail: err => {
          console.error('转换Canvas到图片失败:', err);
          reject(err);
        }
      });
    });
  },

  // 生成雷达图图片
  async generateRadarChartImage() {
    return new Promise((resolve, reject) => {
      try {
        // 初始化雷达图canvas
        const query = wx.createSelectorQuery();
        query.select('#radarCanvas')
          .fields({ node: true, size: true })
          .exec(async (res) => {
            if (!res[0] || !res[0].node) {
              console.error('获取雷达图画布失败');
              reject(new Error('获取雷达图画布失败'));
              return;
            }

            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            // 获取设备信息
            const systemInfo = wx.getSystemInfoSync();
            const pixelRatio = systemInfo.pixelRatio;
            const screenWidth = systemInfo.screenWidth;

            // 限制最大画布宽度，避免在大屏设备上过宽
            const maxCanvasWidth = Math.min(screenWidth, 750);
            const canvasWidth = maxCanvasWidth;

            // 设置雷达图尺寸
            const size = canvasWidth * 0.9;
            canvas.width = size * pixelRatio;
            canvas.height = size * pixelRatio;

            // 缩放上下文以适应设备像素比
            ctx.scale(pixelRatio, pixelRatio);

            // 清空画布
            ctx.clearRect(0, 0, size, size);

            // 设置背景色
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, size, size);

            // 使用当前的selectedCars数据绘制雷达图
            const width = size;
            const height = size;
            this.drawRadarChart(ctx, width, height);

            // 等待一下确保雷达图绘制完成
            setTimeout(async () => {
              try {
                // 转换为图片并存储
                const imagePath = await this.convertCanvasToImage(canvas);
                this.setData({
                  radarChartImage: imagePath
                });
                console.log('雷达图生成成功:', imagePath);
                resolve(imagePath);
              } catch (err) {
                console.error('雷达图转换为图片失败:', err);
                reject(err);
              }
            }, 300);
          });
      } catch (error) {
        console.error('生成雷达图图片失败:', error);
        reject(error);
      }
    });
  },

  // 绘制雷达图
  drawRadarChart(ctx, width, height) {
    if (!ctx) return;

    const { selectedCars } = this.data;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 提取需要用于雷达图的数据
    const radarData = this.prepareRadarData(selectedCars);

    // 计算雷达图尺寸和位置
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.35; // 雷达图半径

    // 雷达图有8个维度
    const dimensions = 8;
    const angleStep = (Math.PI * 2) / dimensions;

    // 绘制雷达图背景网格
    this.drawRadarGrid(ctx, centerX, centerY, radius, dimensions, angleStep);

    // 绘制雷达图数据
    this.drawRadarDataLines(ctx, centerX, centerY, radius, radarData, angleStep);

    // 绘制雷达图轴标签
    this.drawRadarLabels(ctx, centerX, centerY, radius, angleStep);
  },

  // 准备雷达图数据
  prepareRadarData(cars) {
    // 预定义颜色数组，避免依赖this.data.radarColors
    const defaultColors = [
      '#4a90e2',  // 蓝色
      '#ff6b6b',  // 红色
      '#50e3c2',  // 绿色
      '#f5a623',  // 橙色
      '#9013fe'   // 紫色
    ];

    // 尝试从this.data中获取radarColors，如果不存在则使用默认颜色
    const radarColors = this.data.radarColors || defaultColors;

    // 定义需要展示的8个维度及其固定区间
    const dimensions = [
      {
        field: 'angle_normal_speed',
        advField: 'angle_normal_speed_advance40',
        name: '夹角平跑极速',
        isHigherBetter: true,
        minValue: 214,  // 固定最小值
        maxValue: 227   // 固定最大值
      },
      {
        field: 'normal_180_acceleration',
        advField: 'normal_180_acceleration_advance40',
        name: '平跑180提速',
        isHigherBetter: false,
        minValue: 2.9,    // 固定最小值
        maxValue: 3.9     // 固定最大值
      },
      {
        field: 'angle_nitro_speed',
        advField: 'angle_nitro_speed_advance40',
        name: '夹角氮气极速',
        isHigherBetter: true,
        minValue: 290,  // 固定最小值
        maxValue: 300   // 固定最大值
      },
      {
        field: 'nitro_250_acceleration',
        advField: 'nitro_250_acceleration_advance40',
        name: '大喷250提速',
        isHigherBetter: false,
        minValue: 1.81, // 固定最小值
        maxValue: 2.21  // 固定最大值
      },
      {
        field: 'high_speed_steering',
        advField: null,
        name: '最小转向',
        isHigherBetter: true,
        minValue: 3.35, // 固定最小值
        maxValue: 4.06  // 固定最大值
      },
      {
        field: 'low_speed_steering',
        advField: null,
        name: '最大转向',
        isHigherBetter: true,
        minValue: 6.88, // 固定最小值
        maxValue: 8.12  // 固定最大值
      },
      {
        field: 'drift_factor',
        advField: null,
        name: '漂移速率',
        isHigherBetter: true,
        minValue: 0.8,  // 固定最小值
        maxValue: 1.03  // 固定最大值
      },
      {
        field: 'friction_factor',
        advField: null,
        name: '摩擦系数',
        isHigherBetter: true,
        minValue: 1.6,  // 固定最小值
        maxValue: 3     // 固定最大值
      }
    ];

    // 归一化数据并组织为雷达图所需格式
    return cars.map((car, carIndex) => {
      const points = dimensions.map((dim, dimIndex) => {
        // 判断是否使用0推进或40推进数据
        const useZeroAdvance = car.level && (car.level.includes('S') || car.level.includes('T3'));
        const fieldToUse = dim.advField && !useZeroAdvance ? dim.advField : dim.field;

        // 提取数字部分
        let value = car[fieldToUse];

        // 特殊处理"无法达到"的情况
        if (value === '无法达到' && dim.name === '大喷290提速') {
          value = 0; // 设置为0
        } else if (typeof value === 'string') {
          const match = value.match(/^([-\d.]+)/);
          value = match ? parseFloat(match[1]) : null;
        } else {
          value = parseFloat(value) || null;
        }

        // 如果值为null或undefined，设为最低值
        if (value === null || value === undefined) {
          value = dim.isHigherBetter ? dim.minValue : dim.maxValue;
        }

        // 不再限制值在规定范围内，允许超出

        // 归一化处理 - 使用固定区间作为参考点，但允许超出这个区间
        let normalizedValue;
        if (dim.isHigherBetter) {
          // 值越大越好，直接归一化
          normalizedValue = (value - dim.minValue) / (dim.maxValue - dim.minValue);
        } else {
          // 值越小越好，反向归一化
          normalizedValue = (dim.maxValue - value) / (dim.maxValue - dim.minValue);
        }

        // 不再限制归一化值到1，允许大于1的值存在
        // 但仍然保留最小值0.1，确保即使性能很差也有一定显示
        normalizedValue = Math.max(0.1, normalizedValue);

        return {
          value: normalizedValue,
          label: dim.name,
          originalValue: car[fieldToUse],
          processedValue: value // 保存处理后的数值
        };
      });

      return {
        name: car.name,
        color: radarColors[carIndex] || '#4a90e2',
        points
      };
    });
  },

  // 绘制雷达图网格
  drawRadarGrid(ctx, centerX, centerY, radius, dimensions, angleStep) {
    // 绘制同心圆
    const levelCount = 5; // 网格层级数

    ctx.strokeStyle = '#eeeeee';
    ctx.fillStyle = 'rgba(248, 249, 250, 0.6)';

    // 填充背景
    ctx.beginPath();
    for (let i = 0; i < dimensions; i++) {
      const angle = i * angleStep - Math.PI / 2; // 从12点钟方向开始
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.closePath();
    ctx.fill();

    // 绘制网格线
    ctx.lineWidth = 1;  // 设置线宽
    for (let level = 1; level <= levelCount; level++) {
      const currentRadius = (radius * level) / levelCount;

      ctx.beginPath();
      for (let i = 0; i < dimensions; i++) {
        const angle = i * angleStep - Math.PI / 2;
        const x = centerX + currentRadius * Math.cos(angle);
        const y = centerY + currentRadius * Math.sin(angle);
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.closePath();
      ctx.stroke();
    }

    // 绘制从中心到各个顶点的线
    for (let i = 0; i < dimensions; i++) {
      const angle = i * angleStep - Math.PI / 2;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.stroke();
    }
  },

  // 绘制雷达图数据线条
  drawRadarDataLines(ctx, centerX, centerY, radius, radarData, angleStep) {
    radarData.forEach((car, index) => {
      const points = car.points;
      const color = car.color || '#4a90e2';

      // 设置样式
      ctx.strokeStyle = color;
      ctx.fillStyle = color + '40'; // 添加透明度

      // 绘制雷达图区域
      ctx.beginPath();
      points.forEach((point, i) => {
        const angle = i * angleStep - Math.PI / 2; // 从12点钟方向开始
        // 使用 value 值，允许它超出标准半径 (value > 1)
        const value = point.value;
        const x = centerX + radius * value * Math.cos(angle);
        const y = centerY + radius * value * Math.sin(angle);

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // 绘制数据点
      points.forEach((point, i) => {
        const angle = i * angleStep - Math.PI / 2;
        const value = point.value;
        const x = centerX + radius * value * Math.cos(angle);
        const y = centerY + radius * value * Math.sin(angle);

        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();

        // 可选：为超出标准范围的点添加特殊标记
        if (value > 1) {
          ctx.beginPath();
          ctx.arc(x, y, 5, 0, Math.PI * 2);
          ctx.strokeStyle = color;
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      });
    });
  },

  // 绘制雷达图轴标签
  drawRadarLabels(ctx, centerX, centerY, radius, angleStep) {
    // 雷达图轴标签
    const labels = [
      '夹角平跑极速',
      '平跑180提速',
      '夹角氮气极速',
      '大喷250提速',
      '最小转向',
      '最大转向',
      '漂移速率',
      '摩擦系数'
    ];

    ctx.fillStyle = '#666666';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    labels.forEach((label, i) => {
      const angle = i * angleStep - Math.PI / 2;
      const labelRadius = radius + 15; // 标签位置略微超出雷达图
      const x = centerX + labelRadius * Math.cos(angle);
      const y = centerY + labelRadius * Math.sin(angle);

      // 根据角度调整文本对齐方式
      if (angle === 0) {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
      } else if (angle === Math.PI) {
        ctx.textAlign = 'right';
        ctx.textBaseline = 'middle';
      } else if (angle === Math.PI / 2) {
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
      } else if (angle === -Math.PI / 2) {
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
      } else if (angle > 0 && angle < Math.PI) {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
      } else if (angle > Math.PI && angle < Math.PI * 3 / 2) {
        ctx.textAlign = 'right';
        ctx.textBaseline = 'top';
      } else if (angle > Math.PI * 3 / 2) {
        ctx.textAlign = 'right';
        ctx.textBaseline = 'bottom';
      } else {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'bottom';
      }

      // 为了避免文字过长，根据需要缩短标签
      const shortLabel = this.getShortenedLabel(label);
      ctx.fillText(shortLabel, x, y);
    });
  },

  // 获取缩短的标签文本
  getShortenedLabel(label) {
    // 较长的标签进行缩短
    const shortLabels = {
      '夹角平跑极速': '平跑极速',
      '平跑180提速': '180提速',
      '夹角氮气极速': '氮气极速',
      '大喷250提速': '250提速',
      '最小转向': '最小转向',
      '最大转向': '最大转向',
      '漂移速率': '漂移速率',
      '摩擦系数': '摩擦系数'
    };

    return shortLabels[label] || label;
  },

  // 在页面渲染完成后自动执行
  onReady() {
    // 如果当前是对比状态，则初始化雷达图
    if (this.data.showCompare === true) {
      console.log('页面onReady时检测到showCompare为true，初始化雷达图');
      setTimeout(() => {
        this.initRadarChart();
      }, 500);
    } else {
      console.log('页面onReady时，对比弹窗未显示，不初始化雷达图');
    }
  },

  // 比较选中的赛车
  async compareSelectedCars() {
    const { selectedCars } = this.data;

    if (selectedCars.length < 2) {
      wx.showToast({
        title: '请至少选择2辆车进行对比',
        icon: 'none'
      });
      return;
    }

    if (selectedCars.length > 5) {
      wx.showToast({
        title: '最多只能同时对比5辆车',
        icon: 'none'
      });
      return;
    }

    // 首先更新雷达图
    this.setData({
      showRadarCanvas: true,
      showRadarChart: true
    });

    wx.showLoading({
      title: '生成雷达图...',
      mask: true
    });

    try {
      // 先生成雷达图图片，确保等待完成
      const radarImage = await this.generateRadarChartImage();
      console.log('雷达图生成完成，开始生成对比图');

      // 隐藏雷达图加载提示
      wx.hideLoading();

      // 确保radarImage存在后再生成对比图
      if (radarImage) {
        // 延迟一下确保图片已经渲染完成
        setTimeout(() => {
          this.generateCompareImage();
        }, 200);
      } else {
        wx.showToast({
          title: '雷达图生成失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('生成雷达图失败:', error);
      wx.showToast({
        title: '生成对比图失败',
        icon: 'none'
      });
    }
  },

  // 格式化进气系数为百分比
  formatIntakeCoefficient(value) {
    if (!value) return '暂无';
    return `${(value * 100).toFixed(2)}%`;
  },

  // 页面隐藏时的逻辑
  onHide() {
    console.log('首页隐藏');
  },

  // 页面卸载时的逻辑
  onUnload() {
    console.log('首页卸载');

    // 清理错误处理器
    this._handlePageError = null;

    // 移除未捕获Promise错误处理
    wx.offUnhandledRejection();
  },
})