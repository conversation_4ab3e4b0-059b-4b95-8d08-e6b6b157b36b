// 赛车推进计算与绘图页面逻辑
const app = getApp();

// 引入API工具类
const api = require('../../utils/api');
const { getConfig } = require('../../config');

// 页面KEY标识
const PAGE_KEY = 'car-calculation';

// 免费查询次数
const FREE_QUERY_COUNT = 3;

Page({
  data: {
    // 页面基础数据
    baseUrl: getConfig().baseUrl,
    backgroundImage: '/images/bg.jpg',
    pageKey: PAGE_KEY,

    // VIP相关数据
    isVip: false,
    freeCount: FREE_QUERY_COUNT,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 搜索和筛选相关
    searchKey: '',
    levels: ['全部'],
    levelOptions: [],
    selectedLevel: '全部',

    // 赛车列表数据
    cars: [],
    filteredCars: [],
    selectedCar: null,

    // 分页相关
    loading: false,
    page: 1,
    hasMore: true,
    isLoading: false,
    totalPages: 1,
    currentPage: 1,
    pageSize: 24,
    isFirstLoad: true,

    // 计算状态
    calculating: false,
    generatingChart: false,

    // 结果数据
    showResult: false,
    propulsion40Levels: [],
    calculationResult: null,

    // 曲线相关
    selectedCurveType: '', // 'power_speed' 或 'speed_time'
    propulsionUpgrades: 40, // 推进改装次数，默认40
    curveData: null,

    // 图表数据
    showChart: false,
    chartTitle: '',
    chartSubtitle: '',
    chartDescription: '',
    chartLegend: [],
    chartData: null,

    // 防抖定时器
    searchTimer: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function(options) {
    console.log('赛车推进计算与绘图页面加载');

    // 初始化VIP状态
    this.initVipStatus();

    // 初始化免费次数
    this.initFreeCount();

    // 初始化数据
    try {
      this.setData({ loading: true });

      // 获取等级列表
      await this.fetchLevels();

      // 获取赛车数据
      await this.fetchCars();

    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 刷新VIP状态
    this.refreshVipStatus();

    // 刷新免费次数
    this.refreshFreeCount();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (!this.data.selectedCar && this.data.hasMore && !this.data.isLoading) {
      this.fetchCars(true);
    }
  },

  /**
   * 初始化VIP状态
   */
  async initVipStatus() {
    try {
      // 优先从全局变量获取VIP状态
      if (app && app.globalData) {
        const isVip = app.globalData.isVip;
        const vipRemainingDays = app.globalData.vipRemainingDays;

        if (isVip !== undefined && vipRemainingDays !== undefined) {
          console.log('从全局变量获取VIP状态:', { isVip, vipRemainingDays });
          this.setData({
            isVip: isVip,
            vipRemainingDays: vipRemainingDays
          });
          return;
        }
      }

      // 其次从缓存获取
      const cachedVipInfo = wx.getStorageSync('vipInfo');
      if (cachedVipInfo) {
        console.log('从缓存获取VIP状态');
        this.updateVipStatus(cachedVipInfo);
        return;
      }

      // 最后才从服务器获取
      console.log('从服务器获取VIP状态');
      const vipInfo = await app.getVipInfo(false, true);
      if (vipInfo) {
        this.updateVipStatus(vipInfo);
      }
    } catch (error) {
      console.error('初始化VIP状态失败:', error);
    }
  },

  /**
   * 更新VIP状态
   */
  updateVipStatus(vipInfo) {
    if (!vipInfo) return;

    // 重新计算剩余天数
    if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
      const expireDate = new Date(vipInfo.vip_expire_at);
      const now = new Date();
      const diffTime = expireDate - now;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      vipInfo.remainingDays = Math.max(0, diffDays);
    }

    this.setData({
      isVip: vipInfo.is_valid_vip,
      vipRemainingDays: vipInfo.remainingDays || 0
    });

    // 更新VIP徽章组件
    const vipBadge = this.selectComponent('#vipBadge');
    if (vipBadge) {
      vipBadge.updateVipStatus(vipInfo);
    }
  },

  /**
   * 刷新VIP状态
   */
  async refreshVipStatus() {
    try {
      if (app && app.getVipInfo) {
        const vipInfo = await app.getVipInfo(false, true);
        if (vipInfo) {
          this.updateVipStatus(vipInfo);
        }
      }
    } catch (error) {
      console.error('刷新VIP状态失败:', error);
    }
  },

  /**
   * 初始化免费次数
   */
  initFreeCount() {
    try {
      const freeCount = api.getFreeCount(PAGE_KEY);
      this.setData({ freeCount: freeCount });
      console.log('初始化免费次数:', freeCount);
    } catch (error) {
      console.error('初始化免费次数失败:', error);
    }
  },

  /**
   * 刷新免费次数
   */
  refreshFreeCount() {
    try {
      const freeCount = api.getFreeCount(PAGE_KEY);
      this.setData({ freeCount: freeCount });
    } catch (error) {
      console.error('刷新免费次数失败:', error);
    }
  },

  /**
   * 获取等级列表
   */
  async fetchLevels() {
    try {
      const timeout = 10000; // 10秒超时
      const res = await Promise.race([
        new Promise((resolve, reject) => {
          wx.request({
            url: `${this.data.baseUrl}/api/cars/levels/`,
            method: 'GET',
            success: res => resolve(res),
            fail: err => reject(err)
          });
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('获取等级列表超时')), timeout)
        )
      ]);

      if (res.data && res.data.success && Array.isArray(res.data.levels)) {
        // 处理等级数据
        const levelOptions = res.data.levels;
        const levels = ['全部', ...levelOptions.map(item => item.label)];

        this.setData({
          levels,
          levelOptions
        });
      }
    } catch (error) {
      console.error('获取等级列表失败:', error);
      // 使用默认等级列表
      this.setData({
        levels: ['全部', 'T2级', 'T1级', 'A级', 'B级', 'C级']
      });
    }
  },

  /**
   * 获取赛车数据
   */
  async fetchCars(isLoadMore = false) {
    if (this.data.isLoading) return;

    try {
      const timeout = 10000; // 10秒超时

      if (!isLoadMore && this.data.isFirstLoad) {
        const response = await Promise.race([
          new Promise(resolve => {
            wx.request({
              url: `${this.data.baseUrl}/api/cars/`,
              method: 'GET',
              success: res => resolve(res),
              fail: () => resolve(null)
            });
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('获取赛车数据超时')), timeout)
          )
        ]);

        if (response && response.data && typeof response.data.count === 'number') {
          const total = response.data.count;
          const totalPages = Math.ceil(total / this.data.pageSize);
          this.setData({
            totalPages,
            currentPage: 1,
            isFirstLoad: false
          });
        }
      }

      // 检查是否还有更多数据
      if (this.data.currentPage > this.data.totalPages) {
        this.setData({ hasMore: false });
        return;
      }

      this.setData({ isLoading: true });

      // 构建请求URL
      let url = `${this.data.baseUrl}/api/cars/?page=${this.data.currentPage}`;
      if (this.data.searchKey) {
        url += `&search=${encodeURIComponent(this.data.searchKey)}`;
      }
      if (this.data.selectedLevel !== '全部') {
        const levelValue = this.data.selectedLevel.replace('级', '');
        url += `&level=${encodeURIComponent(levelValue)}`;
      }

      console.log(`正在加载赛车数据，URL: ${url}`);

      const res = await Promise.race([
        new Promise((resolve, reject) => {
          wx.request({
            url: url,
            method: 'GET',
            success: (res) => resolve(res),
            fail: err => reject(err)
          });
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('获取赛车数据超时')), timeout)
        )
      ]);

      console.log('API响应:', res.data);
      if (!res.data || !Array.isArray(res.data.results)) {
        console.error('接口返回数据格式错误：', res.data);
        return;
      }

      const newCars = res.data.results.map(car => ({
        ...car,
        imageLoading: true,
        imageLoaded: false,
        cachedImageUrl: `https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/car_images/${car.image_id}`,
        formatted_high_speed_steering: !isNaN(Number(car.high_speed_steering)) ? Number(car.high_speed_steering).toFixed(2) : car.high_speed_steering,
        formatted_low_speed_steering: !isNaN(Number(car.low_speed_steering)) ? Number(car.low_speed_steering).toFixed(2) : car.low_speed_steering,
        // 处理推进和引擎档位数据
        propulsion_levels: this.extractPropulsionLevels(car),
        engine_levels: this.extractEngineLevels(car),

        // 燃料强度和点火强度数据
        fuel_intensity: car.fuel_intensity || 6290,
        ignition_intensity: car.ignition_intensity || 6290,
        formatted_fuel_intensity_display: car.fuel_intensity ? (car.fuel_intensity / 1000).toFixed(2) : '6.29',
        formatted_ignition_intensity_display: car.ignition_intensity ? (car.ignition_intensity / 1000).toFixed(2) : '6.29'
      }));

      const nextPage = this.data.currentPage + 1;

      // 如果是加载更多，将新数据与原有数据合并；否则直接使用新数据
      const mergedCars = isLoadMore ? [...this.data.cars, ...newCars] : newCars;

      this.setData({
        cars: mergedCars,
        filteredCars: mergedCars,
        currentPage: nextPage,
        hasMore: nextPage <= this.data.totalPages && newCars.length > 0
      });
    } catch (error) {
      console.error('获取赛车数据失败：', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 提取推进档位数据（1-7档）
   */
  extractPropulsionLevels(car) {
    const levels = [];
    for (let i = 1; i <= 7; i++) {
      const fieldName = `original_propulsion_${i}`;
      const value = car[fieldName];
      // 确保返回数值，如果无效则返回0
      const numValue = parseFloat(value);
      levels.push(isNaN(numValue) ? 0 : numValue);
    }
    return levels;
  },

  /**
   * 提取引擎档位数据（1-6档）
   */
  extractEngineLevels(car) {
    const levels = [];
    for (let i = 1; i <= 6; i++) {
      const fieldName = `engine_gear_${i}`;
      const value = car[fieldName];
      // 确保返回数值，如果无效则返回0
      const numValue = parseFloat(value);
      levels.push(isNaN(numValue) ? 0 : numValue);
    }
    return levels;
  },

  /**
   * 搜索输入处理（防抖）
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKey: keyword
    });

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }

    // 设置新的定时器，500ms后执行搜索
    if (keyword.trim()) {
      const timer = setTimeout(() => {
        this.performSearch();
      }, 500);

      this.setData({
        searchTimer: timer
      });
    } else {
      // 重新加载所有数据
      this.resetAndReload();
    }
  },

  /**
   * 搜索确认
   */
  onSearchConfirm(e) {
    const keyword = e.detail.value || this.data.searchKey;
    if (keyword.trim()) {
      this.performSearch();
    }
  },

  /**
   * 执行搜索
   */
  performSearch() {
    this.resetAndReload();
  },

  /**
   * 等级选择 - 下拉框方式
   */
  onLevelChange(e) {
    const index = e.detail.value;
    const selectedLevel = this.data.levels[index];

    this.setData({
      selectedLevel: selectedLevel
    });
    this.resetAndReload();
  },

  /**
   * 等级选择 - 点击方式（保留兼容）
   */
  onLevelSelect(e) {
    const level = e.currentTarget.dataset.level;
    this.setData({
      selectedLevel: level
    });
    this.resetAndReload();
  },

  /**
   * 重置并重新加载数据
   */
  resetAndReload() {
    this.setData({
      isFirstLoad: true,
      cars: [],
      filteredCars: [],
      currentPage: 1,
      hasMore: true
    }, () => {
      this.fetchCars();
    });
  },

  /**
   * 赛车选择
   */
  onCarSelect(e) {
    const car = e.currentTarget.dataset.car;
    console.log('选择赛车:', car);

    this.setData({
      selectedCar: car,
      showResult: false,
      showChart: false,
      propulsion40Levels: [],
      selectedCurveType: '',
      propulsionUpgrades: 40,
      curveData: null
    });

    wx.showToast({
      title: `已选择 ${car.name}`,
      icon: 'success'
    });
  },

  /**
   * 选择曲线类型
   */
  onCurveTypeSelect(e) {
    const curveType = e.currentTarget.dataset.type;
    console.log('选择曲线类型:', curveType);

    this.setData({
      selectedCurveType: curveType,
      showChart: false,
      curveData: null
    });
  },

  /**
   * 改装次数输入
   */
  onUpgradeInput(e) {
    let value = parseInt(e.detail.value) || 0;
    // 限制在0-40范围内
    value = Math.max(0, Math.min(40, value));

    this.setData({
      propulsionUpgrades: value
    });
  },

  /**
   * 图片加载完成
   */
  onImageLoad(e) {
    const index = e.currentTarget.dataset.index;
    const updateKey = `filteredCars[${index}].imageLoaded`;
    this.setData({
      [updateKey]: true
    });
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    console.error('图片加载失败:', e);
    // 可以设置默认图片
  },

  /**
   * 更换赛车
   */
  onChangeCar() {
    this.setData({
      selectedCar: null,
      showResult: false,
      showChart: false,
      propulsion40Levels: [],
      selectedCurveType: '',
      propulsionUpgrades: 40,
      curveData: null
    });
  },

  /**
   * 生成曲线
   */
  async generateCurve() {
    if (!this.canUseFeature()) return;

    if (!this.data.selectedCar) {
      wx.showToast({
        title: '请先选择赛车',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedCurveType) {
      wx.showToast({
        title: '请先选择曲线类型',
        icon: 'none'
      });
      return;
    }

    this.setData({ generatingChart: true });
    wx.showLoading({ title: '绘制曲线中...' });

    try {
      const car = this.data.selectedCar;

      // 准备API请求数据，数据已经在提取时确保了正确的类型
      const requestData = {
        curve_type: this.data.selectedCurveType,
        cars: [{
          name: car.name,
          level: car.level,
          propulsion_levels: car.propulsion_levels, // 已经是数值数组
          engine_levels: car.engine_levels, // 已经是数值数组
          fuel_intensity: parseInt(car.fuel_intensity) || 6290,
          ignition_intensity: parseInt(car.ignition_intensity) || 6290,
          propulsion_upgrades: parseInt(this.data.propulsionUpgrades) || 0
        }]
      };

      console.log('调用曲线绘制API，请求数据:', requestData);
      console.log('数据类型检查:', {
        propulsion_levels_types: requestData.cars[0].propulsion_levels.map(v => typeof v),
        engine_levels_types: requestData.cars[0].engine_levels.map(v => typeof v),
        fuel_intensity_type: typeof requestData.cars[0].fuel_intensity,
        ignition_intensity_type: typeof requestData.cars[0].ignition_intensity,
        propulsion_upgrades_type: typeof requestData.cars[0].propulsion_upgrades
      });

      // 调用新的曲线绘制API
      const result = await api.request(`${this.data.baseUrl}/api/cars/generate-curves/`, {
        method: 'POST',
        data: requestData
      });

      if (result.success) {
        // 设置曲线数据
        this.setData({
          curveData: result.data,
          showChart: true,
          chartTitle: this.getCurveTitle(this.data.selectedCurveType),
          chartSubtitle: this.getCurveSubtitle(this.data.selectedCurveType),
          chartDescription: this.getCurveDescription(this.data.selectedCurveType),
          chartLegend: this.getCurveLegend(this.data.selectedCurveType, result.data)
        });

        // 渲染图表
        this.renderCurveChart(result.data, this.data.selectedCurveType);

        // 消耗免费次数
        this.consumeFreeCount(1);

        wx.showToast({
          title: '曲线绘制完成',
          icon: 'success'
        });
      } else {
        // 对于后端返回的错误，直接显示错误信息
        wx.showToast({
          title: result.message || '曲线绘制失败',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('生成曲线失败:', error);

      // 提取错误信息
      let errorMessage = '曲线绘制失败';
      if (error.message) {
        // 如果是HTTP错误，提取具体的错误信息
        if (error.message.includes('HTTP 500:')) {
          const match = error.message.match(/HTTP 500: (.+)/);
          errorMessage = match ? match[1] : '服务器内部错误';
        } else if (error.message.includes('HTTP')) {
          errorMessage = '网络请求失败';
        } else {
          errorMessage = error.message;
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({ generatingChart: false });
      wx.hideLoading();
    }
  },

  /**
   * 获取曲线标题
   */
  getCurveTitle(curveType) {
    switch (curveType) {
      case 'power_speed':
        return '动力-速度曲线';
      case 'speed_time':
        return '速度-时间曲线';
      default:
        return '性能曲线';
    }
  },

  /**
   * 获取曲线副标题
   */
  getCurveSubtitle(curveType) {
    switch (curveType) {
      case 'power_speed':
        return '基础动力、大喷动力、CWW动力对比';
      case 'speed_time':
        return '平跑、大喷、超级喷加速性能分析';
      default:
        return '赛车性能分析';
    }
  },

  /**
   * 获取曲线描述
   */
  getCurveDescription(curveType) {
    switch (curveType) {
      case 'power_speed':
        return '横轴为速度(km/h)，纵轴为动力值。曲线越高表示在该速度下动力越强。';
      case 'speed_time':
        return '横轴为时间(s)，纵轴为速度(km/h)。曲线越陡表示加速性能越好。';
      default:
        return '';
    }
  },

  /**
   * 获取曲线图例
   */
  getCurveLegend(curveType, curveData) {
    const legend = [];

    if (curveType === 'power_speed') {
      legend.push(
        { name: '平衡线', color: '#999999' },
        { name: '基础动力', color: '#333333' },
        { name: '大喷动力', color: '#666666' },
        { name: 'CWW动力', color: '#000000' }
      );
    } else if (curveType === 'speed_time') {
      legend.push(
        { name: '平跑速度', color: '#333333' },
        { name: '大喷速度', color: '#666666' },
        { name: '超级喷速度', color: '#000000' }
      );
    }

    return legend;
  },

  /**
   * 渲染曲线图表
   */
  renderCurveChart(curveData, curveType) {
    // 获取Canvas上下文
    const ctx = wx.createCanvasContext('carChart', this);

    // 画布尺寸
    const canvasWidth = 350;
    const canvasHeight = 250;
    const padding = 40;
    const chartWidth = canvasWidth - padding * 2;
    const chartHeight = canvasHeight - padding * 2;

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 设置背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    if (curveType === 'power_speed') {
      this.drawPowerSpeedCurve(ctx, curveData, padding, chartWidth, chartHeight);
    } else if (curveType === 'speed_time') {
      this.drawSpeedTimeCurve(ctx, curveData, padding, chartWidth, chartHeight);
    }

    ctx.draw();
    console.log('曲线图表渲染完成:', curveData);
  },

  /**
   * 计算推进40
   */
  async calculatePropulsion() {
    if (!this.canUseFeature()) return;

    if (!this.data.selectedCar) {
      wx.showToast({
        title: '请先选择赛车',
        icon: 'none'
      });
      return;
    }

    this.setData({ calculating: true });
    wx.showLoading({ title: '计算中...' });

    try {
      // 尝试调用API
      let result;
      try {
        result = await api.request('/api/cars/calculate-propulsion/', {
          method: 'POST',
          data: {
            car_id: this.data.selectedCar.car_id
          }
        });
      } catch (apiError) {
        console.warn('API调用失败，使用本地计算:', apiError);
        // 如果API失败，使用本地计算
        result = {
          success: true,
          data: {
            propulsion_40_levels: this.calculatePropulsion40Local(this.data.selectedCar),
            car_info: this.data.selectedCar
          }
        };
      }

      if (result.success) {
        this.setData({
          propulsion40Levels: result.data.propulsion_40_levels,
          calculationResult: result.data,
          showResult: true
        });

        // 消耗免费次数
        this.consumeFreeCount(1);

        wx.showToast({
          title: '计算完成',
          icon: 'success'
        });
      } else {
        throw new Error(result.message || '计算失败');
      }
    } catch (error) {
      console.error('计算推进40失败:', error);
      wx.showToast({
        title: error.message || '计算失败',
        icon: 'none'
      });
    } finally {
      this.setData({ calculating: false });
      wx.hideLoading();
    }
  },

  /**
   * 本地计算推进40（备用方案）
   */
  calculatePropulsion40Local(car) {
    const { level, propulsion_levels } = car;

    // 推进档位计算表（简化版）
    const propulsionTable = {
      'C/M1': { diffs: [327, 342, 400, 407, 400, 362, 392], maxs: [null, null, null, null, null, null, null] },
      'B/M2/L2/R': { diffs: [327, 342, 400, 407, 400, 362, 392], maxs: [null, null, null, null, null, null, null] },
      'T1': { diffs: [327, 342, 400, 407, 400, 362, 392], maxs: [null, null, null, null, null, null, null] },
      'A/M3/L3': { diffs: [327, 342, 400, 407, 400, 362, 392], maxs: [null, null, null, null, null, null, null] },
      'T2': { diffs: [327, 342, 400, 407, 400, 362, 392], maxs: [null, null, null, null, null, null, null] },
      'T2皮肤': { diffs: [327, 342, 400, 407, 400, 362, 392], maxs: [null, null, null, null, null, null, null] }
    };

    // 确定使用的等级
    let lookupLevel = level;
    if (level.startsWith('T2(')) {
      lookupLevel = 'T2皮肤';
    }

    // 获取计算表数据
    const tableData = propulsionTable[lookupLevel] || propulsionTable['A/M3/L3'];

    // 计算推进40
    const propulsion40Levels = [];
    for (let i = 0; i < propulsion_levels.length; i++) {
      const originalValue = propulsion_levels[i];
      const diffValue = tableData.diffs[i] || 327;
      const maxValue = tableData.maxs[i];

      const calculated40 = originalValue + diffValue;
      const final40 = maxValue === null ? calculated40 : Math.min(calculated40, maxValue);

      propulsion40Levels.push(final40);
    }

    return propulsion40Levels;
  },

  /**
   * 生成动力曲线图
   */
  async generatePowerChart() {
    if (!this.canUseFeature()) return;
    if (!this.data.showResult) {
      wx.showToast({
        title: '请先计算推进40',
        icon: 'none'
      });
      return;
    }

    await this.generateChart('power_speed', '📊 动力-速度曲线图', '基础动力、大喷动力、CWW动力对比');
  },

  /**
   * 生成速度曲线图
   */
  async generateSpeedChart() {
    if (!this.canUseFeature()) return;
    if (!this.data.showResult) {
      wx.showToast({
        title: '请先计算推进40',
        icon: 'none'
      });
      return;
    }

    await this.generateChart('speed_time', '📈 速度-时间曲线图', '加速性能分析');
  },

  /**
   * 通用图表生成方法
   */
  async generateChart(chartType, chartTitle, chartSubtitle) {
    this.setData({ generatingChart: true });
    wx.showLoading({ title: '生成图表中...' });

    try {
      // 尝试调用API
      let result;
      try {
        result = await api.request('/api/cars/generate-chart/', {
          method: 'POST',
          data: {
            chart_type: chartType,
            cars: [{
              name: this.data.selectedCar.name,
              car_level: this.data.selectedCar.level,
              propulsion_levels: this.data.propulsion40Levels,
              engine_levels: this.data.selectedCar.engine_levels,
              fuel_intensity: this.data.selectedCar.fuel_intensity || 6290
            }]
          }
        });
      } catch (apiError) {
        console.warn('图表API调用失败，使用本地生成:', apiError);
        // 如果API失败，使用本地生成
        result = {
          success: true,
          data: {
            chart_config: this.generateChartConfigLocal(chartType)
          }
        };
      }

      if (result.success) {
        // 设置图例
        const legend = this.getChartLegend(chartType);

        this.setData({
          showChart: true,
          chartTitle: chartTitle,
          chartSubtitle: chartSubtitle,
          chartLegend: legend,
          chartData: result.data.chart_config
        });

        // 渲染图表
        this.renderChart(result.data.chart_config, chartType);

        // 消耗免费次数
        this.consumeFreeCount(1);

        wx.showToast({
          title: '图表生成完成',
          icon: 'success'
        });
      } else {
        throw new Error(result.message || '图表生成失败');
      }
    } catch (error) {
      console.error('生成图表失败:', error);
      wx.showToast({
        title: error.message || '图表生成失败',
        icon: 'none'
      });
    } finally {
      this.setData({ generatingChart: false });
      wx.hideLoading();
    }
  },

  /**
   * 本地生成图表配置（备用方案）
   */
  generateChartConfigLocal(chartType) {
    const car = this.data.selectedCar;
    const propulsion40 = this.data.propulsion40Levels;

    if (chartType === 'power_speed') {
      return this.generatePowerSpeedConfig(car, propulsion40);
    } else if (chartType === 'speed_time') {
      return this.generateSpeedTimeConfig(car, propulsion40);
    }

    return {};
  },

  /**
   * 生成动力-速度图表配置
   */
  generatePowerSpeedConfig(car, propulsion40) {
    // 速度锚点
    const speedAnchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5];

    // 模拟基础动力计算
    const basePowers = speedAnchors.map((speed, index) => {
      return 1000 + speed * 2 + (propulsion40[Math.min(index, propulsion40.length - 1)] || 5000) * 0.1;
    });

    // 大喷动力
    const fuelIntensity = car.fuel_intensity || 6290;
    const bigJetPower = fuelIntensity / 1.2;
    const fuelPowers = basePowers.map(power => power + bigJetPower);

    // CWW动力
    const boostPowers = basePowers.map(power => power + bigJetPower * 2);

    return {
      type: 'line',
      data: {
        speedAnchors,
        basePowers,
        fuelPowers,
        boostPowers
      }
    };
  },

  /**
   * 生成速度-时间图表配置
   */
  generateSpeedTimeConfig(car, propulsion40) {
    // 时间锚点（秒）
    const timeAnchors = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

    // 模拟速度计算
    const speeds = timeAnchors.map(time => {
      const acceleration = (propulsion40[0] || 5000) / 100;
      return Math.min(time * acceleration, 400); // 最大速度限制
    });

    return {
      type: 'line',
      data: {
        timeAnchors,
        speeds
      }
    };
  },

  /**
   * 获取图表图例
   */
  getChartLegend(chartType) {
    if (chartType === 'power_speed') {
      return [
        { name: '基础动力', color: '#4a90e2' },
        { name: '大喷动力', color: '#ff9800' },
        { name: 'CWW动力', color: '#9c27b0' }
      ];
    } else if (chartType === 'speed_time') {
      return [
        { name: '速度曲线', color: '#4a90e2' }
      ];
    }
    return [];
  },

  /**
   * 渲染图表
   */
  renderChart(chartConfig, chartType) {
    // 获取Canvas上下文
    const ctx = wx.createCanvasContext('carChart', this);

    // 画布尺寸
    const canvasWidth = 350;
    const canvasHeight = 250;
    const padding = 40;
    const chartWidth = canvasWidth - padding * 2;
    const chartHeight = canvasHeight - padding * 2;

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 设置背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    if (chartType === 'power_speed') {
      this.drawPowerSpeedChart(ctx, chartConfig.data, padding, chartWidth, chartHeight);
    } else if (chartType === 'speed_time') {
      this.drawSpeedTimeChart(ctx, chartConfig.data, padding, chartWidth, chartHeight);
    }

    ctx.draw();
    console.log('图表渲染完成:', chartConfig);
  },

  /**
   * 绘制动力-速度图表
   */
  drawPowerSpeedChart(ctx, data, padding, width, height) {
    const { speedAnchors, basePowers, fuelPowers, boostPowers } = data;

    // 计算数据范围
    const maxSpeed = Math.max(...speedAnchors);
    const maxPower = Math.max(...boostPowers);

    // 绘制坐标轴
    this.drawAxes(ctx, padding, width, height, '速度 (km/h)', '动力');

    // 绘制三条曲线
    this.drawLine(ctx, speedAnchors, basePowers, maxSpeed, maxPower, padding, width, height, '#4a90e2');
    this.drawLine(ctx, speedAnchors, fuelPowers, maxSpeed, maxPower, padding, width, height, '#ff9800');
    this.drawLine(ctx, speedAnchors, boostPowers, maxSpeed, maxPower, padding, width, height, '#9c27b0');
  },

  /**
   * 绘制速度-时间图表
   */
  drawSpeedTimeChart(ctx, data, padding, width, height) {
    const { timeAnchors, speeds } = data;

    // 计算数据范围
    const maxTime = Math.max(...timeAnchors);
    const maxSpeed = Math.max(...speeds);

    // 绘制坐标轴
    this.drawAxes(ctx, padding, width, height, '时间 (s)', '速度 (km/h)');

    // 绘制速度曲线
    this.drawLine(ctx, timeAnchors, speeds, maxTime, maxSpeed, padding, width, height, '#4a90e2');
  },

  /**
   * 绘制坐标轴
   */
  drawAxes(ctx, padding, width, height, xLabel, yLabel) {
    ctx.setStrokeStyle('#333333');
    ctx.setLineWidth(2);

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding, padding + height);
    ctx.lineTo(padding + width, padding + height);
    ctx.stroke();

    // Y轴
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, padding + height);
    ctx.stroke();

    // 标签
    ctx.setFillStyle('#666666');
    ctx.setFontSize(12);
    ctx.fillText(xLabel, padding + width - 50, padding + height + 20);
    ctx.fillText(yLabel, 5, padding + 10);
  },

  /**
   * 绘制线条
   */
  drawLine(ctx, xData, yData, maxX, maxY, padding, width, height, color) {
    ctx.setStrokeStyle(color);
    ctx.setLineWidth(2);
    ctx.beginPath();

    for (let i = 0; i < xData.length; i++) {
      const x = padding + (xData[i] / maxX) * width;
      const y = padding + height - (yData[i] / maxY) * height;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }

    ctx.stroke();
  },

  /**
   * 绘制动力-速度曲线（新API数据格式）
   */
  drawPowerSpeedCurve(ctx, curveData, padding, width, height) {
    // 从API响应中提取数据
    const balanceCurve = curveData.balance_curve;
    const carCurves = curveData.car_curves;

    if (!carCurves || carCurves.length === 0) {
      console.error('没有赛车曲线数据');
      return;
    }

    const carCurve = carCurves[0]; // 取第一辆车的数据

    // 提取所有数据点
    const allData = [];

    // 平衡线数据
    if (balanceCurve && balanceCurve.data) {
      allData.push(...balanceCurve.data);
    }

    // 赛车数据
    if (carCurve.base_power) allData.push(...carCurve.base_power);
    if (carCurve.fuel_power) allData.push(...carCurve.fuel_power);
    if (carCurve.boost_power) allData.push(...carCurve.boost_power);

    // 计算数据范围
    const maxSpeed = Math.max(...allData.map(point => point[0]));
    const maxPower = Math.max(...allData.map(point => point[1]));

    // 绘制坐标轴
    this.drawAxes(ctx, padding, width, height, '速度 (km/h)', '动力');

    // 绘制平衡线（灰色虚线）
    if (balanceCurve && balanceCurve.data) {
      this.drawCurveFromData(ctx, balanceCurve.data, maxSpeed, maxPower, padding, width, height, '#999999', true);
    }

    // 绘制赛车曲线
    if (carCurve.base_power) {
      this.drawCurveFromData(ctx, carCurve.base_power, maxSpeed, maxPower, padding, width, height, '#333333');
    }
    if (carCurve.fuel_power) {
      this.drawCurveFromData(ctx, carCurve.fuel_power, maxSpeed, maxPower, padding, width, height, '#666666');
    }
    if (carCurve.boost_power) {
      this.drawCurveFromData(ctx, carCurve.boost_power, maxSpeed, maxPower, padding, width, height, '#000000');
    }
  },

  /**
   * 绘制速度-时间曲线（新API数据格式）
   */
  drawSpeedTimeCurve(ctx, curveData, padding, width, height) {
    const carCurves = curveData.car_curves;

    if (!carCurves || carCurves.length === 0) {
      console.error('没有赛车曲线数据');
      return;
    }

    const carCurve = carCurves[0]; // 取第一辆车的数据

    // 提取所有数据点
    const allData = [];
    if (carCurve.normal_speed) allData.push(...carCurve.normal_speed);
    if (carCurve.fuel_speed) allData.push(...carCurve.fuel_speed);
    if (carCurve.super_speed) allData.push(...carCurve.super_speed);

    // 计算数据范围
    const maxTime = Math.max(...allData.map(point => point[0]));
    const maxSpeed = Math.max(...allData.map(point => point[1]));

    // 绘制坐标轴
    this.drawAxes(ctx, padding, width, height, '时间 (s)', '速度 (km/h)');

    // 绘制速度曲线
    if (carCurve.normal_speed) {
      this.drawCurveFromData(ctx, carCurve.normal_speed, maxTime, maxSpeed, padding, width, height, '#333333');
    }
    if (carCurve.fuel_speed) {
      this.drawCurveFromData(ctx, carCurve.fuel_speed, maxTime, maxSpeed, padding, width, height, '#666666');
    }
    if (carCurve.super_speed) {
      this.drawCurveFromData(ctx, carCurve.super_speed, maxTime, maxSpeed, padding, width, height, '#000000');
    }
  },

  /**
   * 从数据点数组绘制曲线
   */
  drawCurveFromData(ctx, dataPoints, maxX, maxY, padding, width, height, color, isDashed = false) {
    if (!dataPoints || dataPoints.length === 0) return;

    ctx.setStrokeStyle(color);
    ctx.setLineWidth(2);

    if (isDashed) {
      ctx.setLineDash([5, 5]);
    } else {
      ctx.setLineDash([]);
    }

    ctx.beginPath();

    for (let i = 0; i < dataPoints.length; i++) {
      const [xValue, yValue] = dataPoints[i];
      const x = padding + (xValue / maxX) * width;
      const y = padding + height - (yValue / maxY) * height;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }

    ctx.stroke();
    ctx.setLineDash([]); // 重置虚线设置
  },

  /**
   * 检查是否可以使用功能
   */
  canUseFeature() {
    if (this.data.isVip) {
      return true;
    }

    if (this.data.freeCount <= 0) {
      this.setData({ showVipDialog: true });
      return false;
    }

    return true;
  },

  /**
   * 消耗免费次数
   */
  consumeFreeCount(count) {
    if (!this.data.isVip) {
      const newFreeCount = Math.max(0, this.data.freeCount - count);
      this.setData({ freeCount: newFreeCount });
      api.updateFreeCount(PAGE_KEY, -count);

      // 更新VIP徽章显示
      const vipBadge = this.selectComponent('#vipBadge');
      if (vipBadge) {
        vipBadge.refreshFreeCount();
      }
    }
  },

  /**
   * VIP徽章点击
   */
  handleVipBadgeClick() {
    this.setData({ showVipDialog: true });
  },

  /**
   * VIP对话框关闭
   */
  onVipDialogClose() {
    this.setData({ showVipDialog: false });
  },

  /**
   * 购买VIP
   */
  async onBuyVip(e) {
    try {
      wx.showLoading({
        title: '正在处理...',
        mask: true
      });

      const { plan } = e.detail;
      console.log('购买VIP套餐:', plan);

      // 调用全局VIP购买方法
      if (app && app.processVipPurchase) {
        await app.processVipPurchase({ plan });

        // 购买成功后刷新VIP状态
        await this.refreshVipStatus();

        // 关闭对话框
        this.setData({ showVipDialog: false });

        wx.showToast({
          title: '购买成功',
          icon: 'success'
        });
      } else {
        throw new Error('VIP购买功能未初始化');
      }
    } catch (error) {
      console.error('购买VIP失败:', error);
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 反馈按钮点击
   */
  onFeedbackTap() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 页面卸载时的清理工作
    console.log('赛车推进计算与绘图页面卸载');
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'QQ飞车赛车推进计算与绘图 - 精确计算满改装推进40',
      path: '/pages/car-calculation/car-calculation'
    };
  }
});
