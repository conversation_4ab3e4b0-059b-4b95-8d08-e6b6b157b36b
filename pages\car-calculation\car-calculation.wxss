/* 赛车推进计算与绘图页面样式 */
page {
  /* 内容宽度 */
  --content-width: 96%;

  /* 字体大小 - 遵循设计规范 */
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;

  /* 颜色系统 - 遵循设计规范 */
  --primary-color: #4a90e2;
  --primary-gradient: linear-gradient(135deg, #4a90e2, #3670b2);
  --purple-color: #9c27b0;
  --purple-gradient: linear-gradient(135deg, #9c27b0, #7b1fa2);
  --gold-color: #f9d776;
  --bg-color: #f5f7fa;
  --text-main: #333333;
  --text-secondary: #666666;
  --text-hint: #999999;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;

  /* 卡片样式 */
  --card-bg-color: rgba(255, 255, 255, 0.98);
  --card-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  --card-border: 1px solid rgba(74, 144, 226, 0.1);
  --card-radius: 16rpx;

  /* 字体 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --content-width: 98%;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --content-width: 92%;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

/* 页面容器 */
.container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  overflow-x: hidden; /* 防止水平溢出 */
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加半透明遮罩层，提高内容可读性 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

/* VIP徽章容器 */
.vip-badge-container {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  z-index: 100;
}

/* 页面标题 */
.header {
  text-align: center;
  padding: 30rpx 0 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  width: var(--content-width);
  max-width: 720rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 12rpx;
  letter-spacing: 2rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  opacity: 0.9;
}

/* 搜索栏 - 参考首页设计 */
.search-bar {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
  position: relative;
  z-index: 100;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background: rgba(235, 242, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 80rpx;
  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.05);
  max-width: 100%;
  margin: 0 auto;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input-wrap input {
  flex: 1;
  height: 100%;
  color: #333;
  font-size: 28rpx;
  min-width: 200rpx;
}

.placeholder {
  color: #999;
}

.search-btn {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.search-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #357abd, #2868a9);
}

.custom-picker {
  margin: 0 16rpx;
}

.level-picker {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.5);
  height: 60rpx;
  padding: 0 20rpx;
  border-radius: 8rpx;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s;
}

.level-picker.active {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 12rpx;
  opacity: 0.6;
}



/* 搜索结果 */
.search-results {
  background-color: var(--card-bg-color);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.results-title {
  font-size: var(--font-size-normal);
  color: var(--text-secondary);
  padding: 16rpx 20rpx 8rpx;
  font-weight: 500;
  border-bottom: 1px solid rgba(74, 144, 226, 0.1);
}

.results-list {
  max-height: 300rpx;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background-color: rgba(74, 144, 226, 0.05);
  transform: scale(0.98);
}

.car-name {
  font-size: var(--font-size-normal);
  color: var(--text-main);
  font-weight: 500;
}

.car-level {
  font-size: var(--font-size-small);
  padding: 6rpx 14rpx;
  border-radius: 20rpx;
  font-weight: 500;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  border: 1px solid;
}

/* 等级样式 */
.level-t2 {
  color: #e91e63;
  background-color: rgba(233, 30, 99, 0.1);
  border-color: rgba(233, 30, 99, 0.2);
}

.level-t1 {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
  border-color: rgba(255, 152, 0, 0.2);
}

.level-a {
  color: var(--primary-color);
  background-color: rgba(74, 144, 226, 0.1);
  border-color: rgba(74, 144, 226, 0.2);
}

.level-b {
  color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.1);
  border-color: rgba(156, 39, 176, 0.2);
}

.level-c {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.2);
}

.level-default {
  color: var(--text-secondary);
  background-color: rgba(153, 153, 153, 0.1);
  border-color: rgba(153, 153, 153, 0.2);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(74, 144, 226, 0.2);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-normal);
  padding: 60rpx 20rpx;
  background-color: var(--card-bg-color);
  border-radius: var(--card-radius);
  margin: 20rpx auto;
  width: var(--content-width);
  max-width: 720rpx;
}

/* 赛车列表 */
.car-list {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-bottom: 20rpx;
}

/* 赛车卡片 - 同步首页设计 */
.car-card {
  background: rgba(235, 242, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 1px solid rgba(74, 144, 226, 0.1);
}

.car-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.car-card-content {
  display: flex;
  align-items: center;
  padding: 16rpx;
  gap: 16rpx;
  height: 140rpx;
}

.car-image-container {
  position: relative;
  width: 160rpx;
  height: 108rpx;
  flex-shrink: 0;
  border-radius: 12rpx;
  overflow: hidden;
  background: rgba(248, 248, 248, 0.5);
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.car-image.loaded {
  opacity: 1;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.car-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.car-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 等级标签样式 - 完整等级颜色系统 */
.car-level {
  padding: 6rpx 12rpx;
  color: #fff;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* S级 - 紫色 */
.car-level[data-level^="S"] {
  background: rgba(147, 112, 219, 0.9);
}

/* A级 - 橙色 */
.car-level[data-level^="A"] {
  background: rgba(255, 165, 0, 0.9);
}

/* B级 - 绿色 */
.car-level[data-level^="B"] {
  background: rgba(102, 187, 106, 0.9);
}

/* C级 - 蓝色 */
.car-level[data-level^="C"] {
  background: rgba(30, 144, 255, 0.9);
}

/* D级 - 灰色 */
.car-level[data-level^="D"] {
  background: rgba(169, 169, 169, 0.9);
}

/* L0级 - 灰色 */
.car-level[data-level="L0"] {
  background: rgba(169, 169, 169, 0.9);
}

/* L1/L2/L3级 - 浅蓝色 */
.car-level[data-level^="L1"],
.car-level[data-level^="L2"],
.car-level[data-level^="L3"] {
  background: rgba(135, 206, 235, 0.9);
}

/* M1/M2/M3级 - 青色 */
.car-level[data-level^="M1"],
.car-level[data-level^="M2"],
.car-level[data-level^="M3"] {
  background: rgba(72, 209, 204, 0.9);
}

/* R级 - 红色 */
.car-level[data-level^="R"] {
  background: rgba(220, 20, 60, 0.9);
}

/* T系列 - 橙色 */
.car-level[data-level^="T"] {
  background: rgba(255, 140, 0, 0.9);
}

/* 引擎档位区域 - 左右布局 + 小卡片 */
.engine-section {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.engine-title {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  width: 80rpx;
  line-height: 1.4;
}

.engine-gears {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.gear-row {
  display: flex;
  gap: 4rpx;
}

.gear-item {
  font-size: 16rpx;
  color: #666;
  background: rgba(74, 144, 226, 0.08);
  padding: 4rpx 6rpx;
  border-radius: 6rpx;
  text-align: center;
  font-weight: 500;
  border: 1px solid rgba(74, 144, 226, 0.1);
  transition: all 0.2s ease;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gear-item:active {
  background: rgba(74, 144, 226, 0.15);
  transform: scale(0.95);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .engine-title {
    font-size: 18rpx;
    width: 70rpx;
  }

  .gear-item {
    font-size: 14rpx;
    padding: 3rpx 4rpx;
  }

  .gear-row {
    gap: 3rpx;
  }

  .engine-gears {
    gap: 4rpx;
  }

  .engine-section {
    gap: 8rpx;
  }
}

@media (min-width: 414px) {
  .engine-title {
    font-size: 22rpx;
    width: 90rpx;
  }

  .gear-item {
    font-size: 18rpx;
    padding: 6rpx 8rpx;
  }

  .gear-row {
    gap: 6rpx;
  }

  .engine-gears {
    gap: 8rpx;
  }

  .engine-section {
    gap: 16rpx;
  }
}

/* 底部加载提示 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  color: var(--text-secondary);
  font-size: var(--font-size-small);
  gap: 12rpx;
}

.loading-more .loading {
  width: 24rpx;
  height: 24rpx;
  border-width: 2rpx;
}

.no-more {
  text-align: center;
  color: var(--text-hint);
  font-size: var(--font-size-small);
  padding: 20rpx;
}

/* 赛车信息卡片 - 专业设计 */
.car-info-section {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
}

.car-info-card {
  background: rgba(235, 242, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.15);
  position: relative;
  overflow: hidden;
}

.car-info-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd, #9c27b0);
  border-radius: 20rpx 20rpx 0 0;
}

.car-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.car-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.selected-car-image {
  width: 80rpx;
  height: 54rpx;
  object-fit: contain;
  border-radius: 12rpx;
  background: rgba(248, 248, 248, 0.8);
  border: 2px solid rgba(74, 144, 226, 0.1);
}

.car-title-text {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  flex: 1;
}

.car-title-text .car-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.change-car-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.change-car-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.2);
}

.car-data-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20rpx;
}

/* 数据区域 - 专业设计 */
.data-section {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.05), rgba(74, 144, 226, 0.08));
  border-radius: 16rpx;
  padding: 20rpx;
  border: 1px solid rgba(74, 144, 226, 0.15);
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}

.data-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4rpx;
  height: 100%;
  background: linear-gradient(135deg, #4a90e2, #9c27b0);
  border-radius: 0 2rpx 2rpx 0;
}

.data-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 600;
  padding-left: 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.data-title::before {
  content: "⚙️";
  font-size: 24rpx;
}

.data-levels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(90rpx, 1fr));
  gap: 12rpx;
}

.level-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 8rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  border: 1px solid rgba(74, 144, 226, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.level-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2rpx;
  background: linear-gradient(90deg, #4a90e2, #9c27b0);
}

.level-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.level-label {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.level-value {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.fuel-intensity {
  text-align: center;
  font-size: 36rpx;
  color: #ff9800;
  font-weight: bold;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05));
  border-radius: 16rpx;
  border: 2px solid rgba(255, 152, 0, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.fuel-intensity::before {
  content: "⛽";
  position: absolute;
  top: 8rpx;
  right: 12rpx;
  font-size: 20rpx;
  opacity: 0.6;
}

/* 点火强度 */
.ignition-intensity {
  text-align: center;
  font-size: 36rpx;
  color: #e91e63;
  font-weight: bold;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(233, 30, 99, 0.1), rgba(233, 30, 99, 0.05));
  border-radius: 16rpx;
  border: 2px solid rgba(233, 30, 99, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(233, 30, 99, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.ignition-intensity::before {
  content: "🔥";
  position: absolute;
  top: 8rpx;
  right: 12rpx;
  font-size: 20rpx;
  opacity: 0.6;
}

/* 操作按钮区域 */
.action-section {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 曲线类型选择区域 */
.curve-type-section {
  margin: 16rpx 0;
}

.curve-type-title {
  font-size: var(--font-size-normal);
  font-weight: bold;
  color: var(--text-main);
  margin-bottom: 12rpx;
  text-align: center;
}

.curve-type-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.curve-type-option {
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  border: 2px solid rgba(74, 144, 226, 0.2);
  background: rgba(74, 144, 226, 0.05);
  color: var(--text-secondary);
  font-size: var(--font-size-small);
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.curve-type-option:active {
  transform: scale(0.98);
}

.curve-type-option.active {
  border-color: var(--primary-color);
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.primary-button {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: var(--font-size-title);
  font-weight: bold;
  border: none;
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.primary-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.2);
}

.primary-button[disabled] {
  background: linear-gradient(135deg, #ccc, #bbb);
  color: var(--text-hint);
  box-shadow: none;
  transform: none;
  opacity: 0.6;
}

.chart-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
  margin-top: 8rpx;
}

.secondary-button {
  height: 72rpx;
  border-radius: 12rpx;
  font-size: var(--font-size-normal);
  font-weight: bold;
  border: none;
  background: var(--purple-gradient);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(156, 39, 176, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.secondary-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(156, 39, 176, 0.2);
}

.secondary-button[disabled] {
  background: linear-gradient(135deg, #ccc, #bbb);
  color: var(--text-hint);
  box-shadow: none;
  transform: none;
  opacity: 0.6;
}

/* 结果显示区域 */
.result-section {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
}

.result-card {
  background-color: var(--card-bg-color);
  border-radius: var(--card-radius);
  padding: 24rpx;
  box-shadow: var(--card-shadow);
  border: var(--card-border);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: var(--success-color);
  border-radius: var(--card-radius) var(--card-radius) 0 0;
}

.result-header {
  text-align: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid rgba(76, 175, 80, 0.1);
}

.result-title {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: var(--text-main);
  margin-bottom: 8rpx;
}

.result-subtitle {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.result-comparison {
  overflow-x: auto;
}

.comparison-header {
  display: grid;
  grid-template-columns: 60rpx 1fr 1fr 80rpx;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}

.header-item {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  font-weight: bold;
  text-align: center;
}

.comparison-row {
  display: grid;
  grid-template-columns: 60rpx 1fr 1fr 80rpx;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
  background-color: white;
  border-radius: 8rpx;
  border: 1px solid rgba(76, 175, 80, 0.1);
  transition: all 0.2s ease;
}

.comparison-row:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.row-item {
  font-size: var(--font-size-small);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-num {
  font-weight: bold;
  color: var(--text-secondary);
}

.original {
  color: var(--text-main);
}

.propulsion40 {
  color: var(--success-color);
  font-weight: bold;
}

.improvement {
  color: var(--primary-color);
  font-weight: bold;
  font-size: var(--font-size-mini);
}

/* 图表显示区域 */
.chart-section {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 0 auto 20rpx;
}

.chart-card {
  background-color: var(--card-bg-color);
  border-radius: var(--card-radius);
  padding: 24rpx;
  box-shadow: var(--card-shadow);
  border: var(--card-border);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: var(--purple-gradient);
  border-radius: var(--card-radius) var(--card-radius) 0 0;
}

.chart-header {
  text-align: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid rgba(156, 39, 176, 0.1);
}

.chart-title {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: var(--text-main);
  margin-bottom: 8rpx;
}

.chart-subtitle {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.chart-container {
  margin-bottom: 16rpx;
}

.chart-canvas {
  width: 100%;
  height: 300rpx;
  background-color: #fff;
  border-radius: 8rpx;
  border: 1px solid rgba(74, 144, 226, 0.1);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.chart-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-text {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

/* 图表描述 */
.chart-description {
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(74, 144, 226, 0.05);
  border-radius: 8rpx;
  border: 1px solid rgba(74, 144, 226, 0.1);
}

.description-text {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  line-height: 1.4;
  text-align: center;
}

/* 底部区域 */
.footer {
  width: var(--content-width);
  max-width: 720rpx;
  margin: 20rpx auto 0;
  text-align: center;
}

.disclaimer-card {
  background-color: var(--card-bg-color);
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.1);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  margin-bottom: 16rpx;
}

.disclaimer {
  font-size: var(--font-size-small);
  color: var(--text-hint);
  line-height: 1.4;
}

.feedback-section {
  margin-top: 16rpx;
}

.feedback-link {
  display: inline-block;
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  border-radius: 20rpx;
  font-size: var(--font-size-small);
  font-weight: 500;
  text-decoration: none;
  box-shadow: 0 2rpx 6rpx rgba(255, 152, 0, 0.3);
  transition: all 0.2s ease;
}

.feedback-link:active {
  transform: scale(0.95);
  opacity: 0.9;
}
