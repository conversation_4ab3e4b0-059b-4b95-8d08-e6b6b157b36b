Component({
  /**
   * 组件的属性列表
   */
  properties: {
    selected: {
      type: Number,
      value: 2  // 默认选中工具箱（索引为2）
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    list: [
      {
        pagePath: "/pages/index/index",
        text: "赛车",
        iconPath: "/images/car-icon.png",
        selectedIconPath: "/images/car-icon-active.png"
      },
      {
        pagePath: "/pages/pet/pet",
        text: "宠物",
        iconPath: "/images/pet-icon.png",
        selectedIconPath: "/images/pet-icon-active.png"
      },
      {
        pagePath: "/pages/toolbox/toolbox",
        text: "工具箱",
        iconPath: "/images/toolbox-icon.png",
        selectedIconPath: "/images/toolbox-icon-active.png"
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      const index = data.index;
      
      // 如果点击当前已选中的项，不做任何操作
      if (index === this.data.selected) {
        return;
      }
      
      // 使用switchTab进行无动画切换，更接近原生体验
      wx.switchTab({
        url,
        success: () => {
          // 成功切换后更新选中状态
          this.setData({
            selected: index
          });
        }
      });
    }
  }
}) 