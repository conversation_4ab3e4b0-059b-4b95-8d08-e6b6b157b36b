.vip-badge {
  display: inline-block;
}

.vip-badge-content {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 30rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.vip-badge-content.vip {
  background: linear-gradient(135deg, #7b5314, #ffd700);
  border: 1rpx solid rgba(255, 215, 0, 0.5);
}

/* 失败状态的样式 */
.vip-badge-content.failed {
  animation: pulse 1.5s infinite ease-in-out;
  border: 1rpx solid rgba(255, 72, 72, 0.7);
  background-color: rgba(71, 71, 71, 0.85);
}

/* 失败状态动画 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.badge-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-icon image {
  width: 34rpx;
  height: 34rpx;
}

.badge-text {
  display: flex;
  flex-direction: column;
}

.vip-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.count-text {
  font-size: 22rpx;
  color: #fff;
}

.sub-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
} 