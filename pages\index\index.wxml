<view class="container">
  <image class="bg-image" src="/images/bg.jpg" mode="aspectFill"></image>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <image class="search-icon" src="/images/search.png" />
      <input
        type="text"
        placeholder="搜索赛车名称或编号"
        placeholder-class="placeholder"
        value="{{searchKey}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        bindfocus="onSearchFocus"
        bindblur="onSearchBlur"
      />
      <picker
        class="custom-picker"
        mode="selector"
        range="{{levels}}"
        bindchange="onLevelChange"
        header-text="选择赛车级别"
      >
        <view class="level-picker {{selectedLevel !== '全部' ? 'active' : ''}}">
          <text>{{selectedLevel === '全部' ? '级别' : selectedLevel}}</text>
          <image class="picker-arrow" src="/images/arrow-down.png" />
        </view>
      </picker>
      <view class="search-btn" bindtap="onSearch">查询</view>
    </view>

    <!-- 添加搜索历史部分 -->
    <view class="search-history {{showSearchHistory ? 'show' : ''}}">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="clear-history" bindtap="clearSearchHistory">清空</text>
      </view>
      <view class="history-list">
        <view class="history-item"
              wx:for="{{searchHistory}}"
              wx:key="*this"
              bindtap="onHistoryItemTap"
              data-keyword="{{item}}">
          <text>{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="function-bar">
      <view class="compare-mode">
        <text>对比模式</text>
        <switch class="mode-switch" checked="{{isCompareMode}}" bindchange="onCompareModeChange" color="#4a90e2"/>
      </view>

      <view class="sort-group">
        <picker
          class="sort-picker"
          range="{{sortOptions}}"
          range-key="label"
          bindchange="onSortFieldChange"
          header-text="选择排序属性"
        >
          <view class="sort-field {{sortBy ? 'active' : ''}}">
            <text style="font-size: {{sortLabelFontSize}};">{{currentSortLabel}}</text>
            <image class="picker-arrow" src="/images/arrow-down.png" />
          </view>
        </picker>

        <view class="sort-direction-btn {{sortBy ? 'active' : ''}}" bindtap="toggleSortDirection">
          <text class="direction-icon">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !filteredCars.length}}">
    <view class="loading"></view>
  </view>

  <!-- 无数据提示 -->
  <view class="empty-tip" wx:elif="{{filteredCars.length === 0 && !loading}}">
    暂无符合条件的赛车数据
  </view>

  <!-- 赛车列表 -->
  <view class="car-list" wx:else>
    <block wx:for="{{filteredCars}}" wx:key="id">
      <!-- 顶部边界线 -->
      <view class="header-boundary" wx:if="{{item.isTopBoundary}}">
        <view class="header-boundary-card">
          <view class="header-boundary-content">
            <text class="header-boundary-text-icon">🔄</text>
            <text class="header-boundary-text">以下是{{item.headerBoundaryValue}}赛车{{item.headerFieldLabel}}{{sortOrder === 'asc' ? '升序' : '降序'}}排序</text>
          </view>
        </view>
      </view>

      <!-- 中间边界线 -->
      <view class="header-boundary" wx:elif="{{item.isBoundary}}">
        <view class="header-boundary-card">
          <view class="header-boundary-content">
            <text class="header-boundary-text-icon">🔄</text>
            <text class="header-boundary-text">以下是{{item.headerBoundaryValue}}赛车{{item.headerFieldLabel}}{{sortOrder === 'asc' ? '升序' : '降序'}}排序</text>
          </view>
        </view>
      </view>

      <!-- 普通夹角平跑边界线 -->
      <view class="boundary-line" wx:elif="{{item.hasBoundaryBefore}}">
        <view class="boundary-card"></view>
        <view class="boundary-content">
          <view class="boundary-line-inner"></view>
          <view class="boundary-value-container">
            <text class="boundary-icon">⚡</text>
            <text class="boundary-value">{{item.boundaryValue}}</text>
            <text class="boundary-label">平跑</text>
          </view>
          <view class="boundary-line-inner"></view>
        </view>
      </view>

      <!-- 赛车卡片，只对实际赛车对象显示 -->
      <view class="car-card" wx:if="{{!item.isTopBoundary && !item.isBoundary}}" bindtap="{{isCompareMode ? 'onCardSelect' : 'onCardTap'}}" data-id="{{item.id}}">
        <view class="car-image-container">
          <image
            class="car-image {{item.imageLoaded ? 'loaded' : ''}}"
            src="{{item.cachedImageUrl}}"
            mode="aspectFit"
            bindload="onImageLoad"
            binderror="onImageError"
            data-index="{{index}}"
            lazy-load
          />
          <view class="image-loading" wx:if="{{!item.imageLoaded}}">
            <view class="loading"></view>
          </view>
        </view>
        <view class="car-info">
          <view class="car-header">
            <text class="car-name">{{item.name}}</text>
            <!-- <text class="suspension-tag" wx:if="{{item.suspension && item.suspension !== '0'}}">自带悬挂{{item.suspension}}</text> -->
            <text class="car-level" data-level="{{item.level}}">{{item.level}}</text>
          </view>
          <view class="car-details">
            <view class="detail-item">
              <text class="label">转向：</text>
              <text class="value">{{item.formatted_high_speed_steering}}/{{item.formatted_low_speed_steering}}</text>
            </view>
            <view class="detail-item">
              <text class="label">平跑：</text>
              <text class="value">{{item.angle_normal_speed_advance40 === '暂无' ? item.angle_normal_speed : item.angle_normal_speed_advance40}}</text>
            </view>
            <view class="detail-item">
              <text class="label">摩擦系数：</text>
              <text class="value">{{item.friction_factor}}</text>
            </view>
            <view class="detail-item">
              <text class="label">漂移速率：</text>
              <text class="value">{{item.drift_factor}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 添加边界线（在卡片后面） -->
      <view class="boundary-line" wx:if="{{item.hasBoundaryAfter && !item.isTopBoundary && !item.isBoundary}}">
        <view class="boundary-card"></view>
        <view class="boundary-content">
          <view class="boundary-line-inner"></view>
          <view class="boundary-value-container">
            <text class="boundary-icon">⚡</text>
            <text class="boundary-value">{{item.boundaryValue}}</text>
            <text class="boundary-label">平跑</text>
          </view>
          <view class="boundary-line-inner"></view>
        </view>
      </view>
    </block>

    <!-- 修改底部加载提示 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <view class="loading"></view>
      <text>加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && cars.length > 0}}">
      <view class="thanks-text">感谢白熊猫、地平线、路歌客心、沉沦的遗迹、RabbitTrap提供数据支持！</view>
      <view>已加载全部数据</view>
    </view>

    <!-- 添加底部对比栏 -->
    <view class="compare-bar {{isCompareMode && selectedCarIds.length > 0 ? 'show' : ''}}">
      <view class="selected-cars">
        <view class="selected-car" wx:for="{{selectedCars}}" wx:key="id">
          <image src="{{item.cachedImageUrl}}" mode="aspectFit"/>
          <text class="remove-car" catchtap="removeFromCompare" data-id="{{item.id}}">×</text>
        </view>
      </view>
      <view class="compare-actions">
        <text class="selected-count">已选{{selectedCarIds.length}}辆</text>
        <button
          class="compare-btn {{selectedCarIds.length >= 2 ? 'active' : ''}}"
          disabled="{{selectedCarIds.length < 2}}"
          bindtap="startCompare"
        >开始对比</button>
        <text class="clear-btn" bindtap="clearCompare">清空</text>
      </view>
    </view>
  </view>

  <!-- 对比弹窗 -->
  <view class="compare-overlay {{showCompare ? 'show' : ''}}"
        bindtap="closeCompare">
    <view class="compare-dialog" catchtap="preventClose" catchmove="true">
      <view class="compare-header">
        <text class="compare-title">赛车对比</text>
        <view class="header-actions">
          <button class="gen-image-btn" bindtap="generateCompareImage">
            <image src="/images/download.png" mode="aspectFit"/>
          </button>
          <button class="share-btn" open-type="share">
            <image src="/images/share.png" mode="aspectFit"/>
          </button>
          <text class="close-btn" bindtap="closeCompare">×</text>
        </view>
      </view>

      <scroll-view class="car-info-bar {{carInfoBarCanScroll ? 'can-scroll' : ''}}"
                   scroll-x
                   enhanced
                   show-scrollbar="{{false}}"
                   catchmove="true">
        <view class="car-info-content">
          <view class="car-brief" wx:for="{{selectedCars}}" wx:key="id">
            <image src="{{item.cachedImageUrl}}" mode="aspectFit"/>
            <text class="car-name">{{item.name}}</text>
            <text class="car-level" data-level="{{item.level}}">{{item.level}}</text>
          </view>
        </view>
      </scroll-view>

      <scroll-view
        class="compare-content"
        scroll-y
        enhanced
        show-scrollbar="{{false}}"
        bounces="{{true}}"
        enable-flex
        catchmove="true"
      >
        <!-- 雷达图部分，移动到滚动区域内 -->
        <view class="radar-chart-container" wx:if="{{showCompare}}">
          <view class="radar-chart-title">性能雷达图</view>
          <view class="radar-chart-wrapper">
            <!-- 只在需要绘制时显示Canvas -->
            <canvas wx:if="{{showRadarCanvas}}" type="2d" id="radarChart" class="radar-chart"></canvas>
            <!-- 当静态图片生成后，显示图片而非Canvas -->
            <image wx:if="{{radarChartImage}}" src="{{radarChartImage}}" mode="aspectFit" class="radar-chart-image"></image>
          </view>
          <view class="radar-legend">
            <view class="legend-item" wx:for="{{selectedCars}}" wx:key="id">
              <view class="legend-color" style="background-color: {{radarColors[index] || '#4a90e2'}}"></view>
              <text class="legend-name">{{item.name}}</text>
            </view>
          </view>
        </view>

        <view class="compare-group" wx:for="{{compareGroups}}" wx:key="title">
          <view class="group-title">{{item.title}}</view>
          <view class="compare-table">
            <view class="compare-row" wx:for="{{item.items}}" wx:for-item="row" wx:key="label">
              <text class="row-label">{{row.label}}</text>
              <view class="row-values">
                <text wx:for="{{row.values}}"
                      wx:for-item="value"
                      wx:for-index="valueIndex"
                      wx:key="valueIndex"
                      class="value {{row.highlights[valueIndex] ? 'highlight-' + row.highlights[valueIndex] : ''}}"
                      style="color: {{row.highlights[valueIndex] ? (row.highlights[valueIndex] === 'higher' ? '#ff4d4f' : '#52c41a') : '#333333'}}"
                >{{value}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 添加图例说明 -->
        <view class="compare-group">
          <view class="compare-legend">
            <view class="compare-legend-title">数据说明：</view>
            <view class="compare-legend-item">
              <view class="legend-color" style="background-color: #ff4d4f;"></view>
              <text class="legend-text">红色 - 优势数据</text>
            </view>
            <view class="compare-legend-item">
              <view class="legend-color" style="background-color: #52c41a;"></view>
              <text class="legend-text">绿色 - 劣势数据</text>
            </view>
            <view class="compare-legend-tip">一般认为，摩擦系数在2.6-2.8之间，车重1.3t-1.4t之间手感比较舒适</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 分享选项弹窗 -->
  <view class="share-options {{showShareOptions ? 'show' : ''}}"
        bindtap="closeShareOptions">
    <view class="options-content" catchtap="preventClose">
      <button class="option-btn" open-type="share">
        <image src="/images/wechat.png" mode="aspectFit"/>
        <text>分享给好友</text>
      </button>
    </view>
  </view>

  <!-- 修改展开状态的弹出层 -->
  <view
    class="expanded-overlay {{expandedCardId !== null ? 'show' : ''}}"
    bindtap="onOverlayTap"
    catchtouchmove="onOverlayMove">
    <view class="expanded-card {{expandedCardId !== null ? 'show' : ''}}"
        catch:tap="onExpandedCardTap">
      <view class="close-expanded-btn" catchtap="onOverlayTap">×</view>
      <view class="expanded-image-container">
        <image
          class="expanded-image"
          src="{{expandedCardId !== null ? filteredCars[expandedIndex].cachedImageUrl : ''}}"
          mode="aspectFit"
          bindtap="showFullImage"
          data-url="{{expandedCardId !== null ? filteredCars[expandedIndex].cachedImageUrl : ''}}"
        />
      </view>
      <view class="group-info">
        <text>欢迎加入"飞车图鉴"交流群，QQ群号</text>
        <text class="group-number" bindtap="copyGroupNumber">2156036977</text>
        <text>(点击群号复制)</text>
      </view>
      <view class="expanded-header">
        <text class="expanded-name">{{expandedCardId !== null ? filteredCars[expandedIndex].name : ''}}</text>
        <view class="expanded-tags">
          <!-- <text class="expanded-suspension-tag" wx:if="{{expandedCardId !== null && filteredCars[expandedIndex].suspension && filteredCars[expandedIndex].suspension !== '0'}}">自带悬挂{{filteredCars[expandedIndex].suspension}}</text> -->
          <text class="expanded-level" data-level="{{filteredCars[expandedIndex].level}}">{{filteredCars[expandedIndex].level}}</text>
        </view>
      </view>

      <!-- 添加标签页导航 -->
      <view class="tabs">
        <view
          class="tab-item {{activeTab === 'details' ? 'active' : ''}}"
          data-tab="details"
          bindtap="switchTab"
        >
          详细信息
        </view>
        <view
          class="tab-item {{activeTab === 'ratings' ? 'active' : ''}}"
          data-tab="ratings"
          bindtap="switchTab"
        >
          玩家评价
        </view>
      </view>

      <!-- 详细信息标签页内容 -->
      <scroll-view
        class="expanded-content"
        scroll-y
        enhanced
        show-scrollbar="{{false}}"
        bounces="{{true}}"
        enable-flex
        catchmove="true"
        scroll-anchoring
        fast-deceleration
        id="scroll-details-{{expandedCardId}}"
        hidden="{{activeTab !== 'details'}}"
      >
        <view class="tab-content" hidden="{{activeTab !== 'details'}}">
          <!-- 全新设计的详细信息布局 -->
          <view class="details-card">
            <!-- 基础属性部分 -->
            <view class="details-section">
              <view class="section-title">
                基础属性
                <text class="expanded-suspension-tag" wx:if="{{expandedCardId !== null && filteredCars[expandedIndex].suspension && filteredCars[expandedIndex].suspension !== '0'}}">自带悬挂{{filteredCars[expandedIndex].suspension}}</text>
              </view>
              <view class="detail-grid">
                <view class="detail-item">
                  <text class="label">车重：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].weight || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">宝石槽：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].gem_slots || '暂无') : ''}}</text>
                </view>
              </view>
            </view>

            <!-- 燃料/点火/进气属性部分 -->
            <view class="details-section" data-section-type="fuel">
              <view class="section-title">燃料/点火/进气</view>
              <view class="detail-grid">
                <view class="detail-item">
                  <text class="label">满改燃料时长：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].fuel_duration || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">满改燃料强度：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].formatted_fuel_intensity || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">满改点火时长：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].ignition_duration || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">满改点火强度：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].formatted_ignition_intensity || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">原装进气系数：</text>
                  <text class="value">{{expandedCardId !== null ? filteredCars[expandedIndex].formatted_original_intake : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">满改进气系数：</text>
                  <text class="value">{{expandedCardId !== null ? filteredCars[expandedIndex].formatted_intake : ''}}</text>
                </view>
              </view>
            </view>

            <!-- 操控属性部分 -->
            <view class="details-section">
              <view class="section-title">操控属性</view>
              <view class="detail-grid">
                <view class="detail-item">
                  <view class="label-with-help">
                    <view class="help-icon" catchtap="showAttributeHelp" data-attribute="max_steering">?</view>
                    <text class="label">最大转向：</text>
                  </view>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].formatted_low_speed_steering || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <view class="label-with-help">
                    <view class="help-icon" catchtap="showAttributeHelp" data-attribute="min_steering">?</view>
                    <text class="label">最小转向：</text>
                  </view>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].formatted_high_speed_steering || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">漂移速率：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].drift_factor || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">摩擦系数：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].friction_factor || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">漂移转向：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].drift_steering || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">漂移摆动：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].drift_swing || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">漂移反向：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].drift_reverse || '暂无') : ''}}</text>
                </view>
                <view class="detail-item">
                  <text class="label">漂移回正：</text>
                  <text class="value">{{expandedCardId !== null ? (filteredCars[expandedIndex].drift_correction || '暂无') : ''}}</text>
                </view>
              </view>
            </view>

            <!-- 极速数据对比 (0推进 vs 40推进) -->
            <view class="details-section">
              <view class="section-title">极速数据</view>
              <!-- 标题行 -->
              <view class="comparison-header">
                <text class="column-title first-column">属性</text>
                <text class="column-title">0推进</text>
                <text class="column-title">40推进</text>
              </view>
              <!-- 极速数据行 -->
              <view class="comparison-row">
                <text class="row-label">夹角平跑极速</text>
                <text class="row-value">{{expandedCardId !== null ? (filteredCars[expandedIndex].angle_normal_speed || '暂无') : ''}}</text>
                <text class="row-value advance">{{expandedCardId !== null ? (filteredCars[expandedIndex].angle_normal_speed_advance40 || '暂无') : ''}}</text>
              </view>
              <view class="comparison-row">
                <text class="row-label">夹角氮气极速</text>
                <text class="row-value">{{expandedCardId !== null ? (filteredCars[expandedIndex].angle_nitro_speed || '暂无') : ''}}</text>
                <text class="row-value advance">{{expandedCardId !== null ? (filteredCars[expandedIndex].angle_nitro_speed_advance40 || '暂无') : ''}}</text>
              </view>
            </view>

            <!-- 加速数据对比 (0推进 vs 40推进) -->
            <view class="details-section">
              <view class="section-title">加速性能</view>
              <!-- 标题行 -->
              <view class="comparison-header">
                <text class="column-title first-column">属性</text>
                <text class="column-title">0推进</text>
                <text class="column-title">40推进</text>
              </view>
              <!-- 加速数据行 -->
              <view class="comparison-row">
                <text class="row-label">平跑180提速</text>
                <text class="row-value">{{expandedCardId !== null ? (filteredCars[expandedIndex].normal_180_acceleration || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].normal_180_acceleration_hasNumber ? '秒' : ''}}</text>
                <text class="row-value advance">{{expandedCardId !== null ? (filteredCars[expandedIndex].normal_180_acceleration_advance40 || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].normal_180_acceleration_advance40_hasNumber ? '秒' : ''}}</text>
              </view>
              <view class="comparison-row">
                <text class="row-label">平跑极速提速</text>
                <text class="row-value">{{expandedCardId !== null ? (filteredCars[expandedIndex].normal_speed_acceleration || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].normal_speed_acceleration_hasNumber ? '秒' : ''}}</text>
                <text class="row-value advance">{{expandedCardId !== null ? (filteredCars[expandedIndex].normal_speed_acceleration_advance40 || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].normal_speed_acceleration_advance40_hasNumber ? '秒' : ''}}</text>
              </view>
              <view class="comparison-row">
                <text class="row-label">大喷250提速</text>
                <text class="row-value">{{expandedCardId !== null ? (filteredCars[expandedIndex].nitro_250_acceleration || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].nitro_250_acceleration_hasNumber ? '秒' : ''}}</text>
                <text class="row-value advance">{{expandedCardId !== null ? (filteredCars[expandedIndex].nitro_250_acceleration_advance40 || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].nitro_250_acceleration_advance40_hasNumber ? '秒' : ''}}</text>
              </view>
              <view class="comparison-row">
                <text class="row-label">大喷290提速</text>
                <text class="row-value">{{expandedCardId !== null ? (filteredCars[expandedIndex].nitro_290_acceleration || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].nitro_290_acceleration_hasNumber ? '秒' : ''}}</text>
                <text class="row-value advance">{{expandedCardId !== null ? (filteredCars[expandedIndex].nitro_290_acceleration_advance40 || '暂无') : ''}}{{expandedCardId !== null && filteredCars[expandedIndex].nitro_290_acceleration_advance40_hasNumber ? '秒' : ''}}</text>
              </view>
            </view>

            <!-- 超级喷属性部分 -->
            <view class="details-section" wx:if="{{expandedCardId !== null && (filteredCars[expandedIndex].super_nitro_intensity !== '暂无' && filteredCars[expandedIndex].super_nitro_intensity || filteredCars[expandedIndex].super_nitro_duration !== '暂无' && filteredCars[expandedIndex].super_nitro_duration || filteredCars[expandedIndex].super_nitro_trigger_condition !== '暂无' && filteredCars[expandedIndex].super_nitro_trigger_condition || filteredCars[expandedIndex].super_nitro_250_acceleration !== '暂无' && filteredCars[expandedIndex].super_nitro_250_acceleration || filteredCars[expandedIndex].super_nitro_290_acceleration !== '暂无' && filteredCars[expandedIndex].super_nitro_290_acceleration || filteredCars[expandedIndex].angle_super_nitro_speed !== '暂无' && filteredCars[expandedIndex].angle_super_nitro_speed)}}">
              <view class="section-title">超级喷属性</view>
              <view class="detail-grid">
                <view class="detail-item" wx:if="{{filteredCars[expandedIndex].super_nitro_intensity && filteredCars[expandedIndex].super_nitro_intensity !== '暂无'}}">
                  <text class="label">超级喷强度：</text>
                  <text class="value">{{filteredCars[expandedIndex].super_nitro_intensity}}</text>
                </view>
                <view class="detail-item" wx:if="{{filteredCars[expandedIndex].super_nitro_duration && filteredCars[expandedIndex].super_nitro_duration !== '暂无'}}">
                  <text class="label">超级喷时长：</text>
                  <text class="value">{{filteredCars[expandedIndex].super_nitro_duration}}</text>
                </view>
                <view class="detail-item" wx:if="{{filteredCars[expandedIndex].super_nitro_trigger_condition && filteredCars[expandedIndex].super_nitro_trigger_condition !== '暂无'}}">
                  <text class="label">触发条件：</text>
                  <text class="value">{{filteredCars[expandedIndex].super_nitro_trigger_condition}}</text>
                </view>
              </view>

              <!-- 超级喷数据表格 -->
              <view class="super-nitro-table" wx:if="{{filteredCars[expandedIndex].super_nitro_250_acceleration !== '暂无' && filteredCars[expandedIndex].super_nitro_250_acceleration || filteredCars[expandedIndex].super_nitro_290_acceleration !== '暂无' && filteredCars[expandedIndex].super_nitro_290_acceleration || filteredCars[expandedIndex].angle_super_nitro_speed !== '暂无' && filteredCars[expandedIndex].angle_super_nitro_speed}}">
                <view class="comparison-header">
                  <text class="column-title first-column">属性</text>
                  <text class="column-title">数值</text>
                </view>
                <view class="comparison-row" wx:if="{{filteredCars[expandedIndex].super_nitro_250_acceleration && filteredCars[expandedIndex].super_nitro_250_acceleration !== '暂无'}}">
                  <text class="row-label">超级喷250提速</text>
                  <text class="row-value">{{filteredCars[expandedIndex].super_nitro_250_acceleration}}{{filteredCars[expandedIndex].super_nitro_250_acceleration && !filteredCars[expandedIndex].super_nitro_250_acceleration.includes('秒') && filteredCars[expandedIndex].super_nitro_250_acceleration !== '暂无' && filteredCars[expandedIndex].super_nitro_250_acceleration !== '缺' ? '秒' : ''}}</text>
                </view>
                <view class="comparison-row" wx:if="{{filteredCars[expandedIndex].super_nitro_290_acceleration && filteredCars[expandedIndex].super_nitro_290_acceleration !== '暂无'}}">
                  <text class="row-label">超级喷290提速</text>
                  <text class="row-value">{{filteredCars[expandedIndex].super_nitro_290_acceleration}}{{filteredCars[expandedIndex].super_nitro_290_acceleration && !filteredCars[expandedIndex].super_nitro_290_acceleration.includes('秒') && filteredCars[expandedIndex].super_nitro_290_acceleration !== '暂无' && filteredCars[expandedIndex].super_nitro_290_acceleration !== '缺' ? '秒' : ''}}</text>
                </view>
                <view class="comparison-row" wx:if="{{filteredCars[expandedIndex].angle_super_nitro_speed && filteredCars[expandedIndex].angle_super_nitro_speed !== '暂无'}}">
                  <text class="row-label">夹角超级喷极速</text>
                  <text class="row-value">{{filteredCars[expandedIndex].angle_super_nitro_speed}}</text>
                </view>
              </view>
            </view>

            <!-- 数据说明部分 -->
            <view class="details-section">
              <view class="data-description">
                <view class="description-title">数据说明</view>
                <view class="description-item">
                  <text>
                    <text class="highlight">夹角测试位置</text>：高级驾照第一关左拐桥洞下方，这里赛道<text class="highlight">绝对水平</text>，非常适合测试提速极速
                  </text>
                </view>
                <view class="description-item">
                  <text>
                    <text class="highlight">夹角平跑</text>与23区平跑的区别：23区由于赛道不够长经常不能到达平跑极速，而夹角处可以
                  </text>
                </view>
                <view class="description-item">
                  <text>
                    <text class="highlight">夹角氮气</text>与23区氮气的区别：23区由于第2、3圈存在赛道倾斜，测评氮气极速不准确，而夹角处<text class="highlight">绝对水平，测试更加准确</text>
                  </text>
                </view>
              </view>
            </view>

            <!-- 添加预览按钮 -->
            <view class="details-section">
              <button class="preview-data-btn" catchtap="generateSingleCarImage">预览完整数据</button>
              <view class="thanks-text">感谢白熊猫、地平线、路歌客心、沉沦的遗迹、RabbitTrap提供数据支持！</view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 玩家评分标签页内容 -->
      <scroll-view
        class="expanded-content"
        scroll-y
        enhanced
        show-scrollbar="{{false}}"
        bounces="{{true}}"
        enable-flex
        catchmove="true"
        scroll-anchoring
        fast-deceleration
        id="scroll-ratings-{{expandedCardId}}"
        hidden="{{activeTab !== 'ratings'}}"
      >
        <view class="tab-content" hidden="{{activeTab !== 'ratings'}}">
          <view class="rating-stats" wx:if="{{ratingStats}}">
            <view class="rating-header">
              <text class="rating-title">玩家评分</text>
              <text class="rating-count" wx:if="{{ratingStats.total_ratings > 0}}">{{ratingStats.total_ratings}}人参与评分</text>
            </view>

            <block wx:if="{{ratingStats.total_ratings > 0}}">
              <view class="rating-overview">
                <view class="overall-score">
                  <text class="score">{{ratingStats.overall_avg}}</text>
                  <text class="total">/10</text>
                </view>
                <view class="score-details">
                  <view class="score-item">
                    <text class="label">速度：{{ratingStats.speed_avg}}</text>
                  </view>
                  <view class="score-item">
                    <text class="label">手感：{{ratingStats.handling_avg}}</text>
                  </view>
                  <view class="score-item">
                    <text class="label">颜值：{{ratingStats.appearance_avg}}</text>
                  </view>
                  <view class="score-item">
                    <text class="label">对抗：{{ratingStats.combat_avg}}</text>
                  </view>
                  <view class="score-item">
                    <text class="label">性价比：{{ratingStats.value_avg}}</text>
                  </view>
                </view>
              </view>
            </block>
            <block wx:else>
              <view class="no-rating-tip">
                <text>暂无评分</text>
                <text class="sub-text">来为这辆车做第一个评分吧</text>
              </view>
            </block>

            <view class="rating-action {{ratingStats.total_ratings === 0 ? 'no-rating' : ''}}">
              <block wx:if="{{!hasRated}}">
                <button class="rate-btn" bindtap="showRating">{{ratingStats.total_ratings === 0 ? '我要评分' : '立即评分'}}</button>
              </block>
              <block wx:else>
                <view class="user-rating" bindtap="showRating">
                  <text class="label">我的评分：{{userRating.overall}}</text>
                  <button class="rate-btn" style="margin-left: 16rpx;">修改评分</button>
                </view>
              </block>
            </view>
          </view>

          <!-- 评论功能暂时下线提示 -->
          <!--<view class="comments-offline-notice">
            <image class="offline-icon" src="/images/maintenance.png" mode="aspectFit"/>
            <view class="offline-text">
              <text class="main-text">评论功能维护中</text>
              <text class="sub-text">为了给您提供更好的服务，评论功能正在升级维护</text>
            </view>
          </view>-->

          <!-- 评论功能暂时注释 -->
          <view class="comments-list" wx:if="{{comments && comments.length > 0}}">
            <view class="comment-item" wx:for="{{comments}}" wx:key="id">
              <view class="comment-user">
                <image class="comment-avatar" src="{{item.avatar || '/images/default-avatar.png'}}" mode="aspectFill"/>
                <view class="comment-user-info">
                  <text class="comment-nickname">{{item.nickname || '匿名用户'}}</text>
                  <text class="comment-time">{{item.created_at}}</text>
                </view>
                <view class="comment-actions">
                  <block wx:if="{{item.can_edit || item.can_delete}}">
                    <text class="edit-btn" wx:if="{{item.can_edit}}" data-id="{{item.id}}" bindtap="showEditCommentModal">编辑</text>
                    <text class="delete-btn" wx:if="{{item.can_delete}}" data-id="{{item.id}}" bindtap="deleteComment">删除</text>
                  </block>
                  <text class="report-btn" wx:else data-id="{{item.id}}" bindtap="reportComment">举报</text>
                </view>
              </view>
              <view class="comment-content">{{item.content}}</view>
            </view>
          </view>

          <!-- 无评论提示也注释掉 -->
          <view class="no-comments" wx:else>
            <image class="no-comments-icon" src="/images/no-comments.png" mode="aspectFit"/>
            <text>暂无评论</text>
            <text class="sub-text">来发表第一条评论吧</text>
          </view>

          <!-- 添加评论按钮 -->
          <view class="add-comment-btn-container">
            <button class="add-comment-btn" bindtap="showCommentModal">发表评论</button>
          </view>

          <!-- 加载更多 -->
          <view class="load-more" wx:if="{{hasMoreComments}}">
            <text bindtap="loadMoreComments">加载更多</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 评分弹窗 -->
  <view class="rating-modal {{showRatingModal ? 'show' : ''}}" catchtouchmove="preventClose">
    <view class="rating-content">
      <view class="rating-modal-header">
        <text class="title">评分</text>
        <text class="close-btn" bindtap="closeRating">×</text>
      </view>
      <view class="rating-form">
        <view class="rating-item">
          <view class="rating-label">
            <text class="label">速度</text>
            <text class="value">{{currentRating.speed}}分</text>
          </view>
          <view class="star-rating">
            <view class="star-group">
              <view
                class="star {{currentRating.speed >= (index + 1) ? 'active' : ''}}"
                wx:for="{{10}}"
                wx:key="index"
                data-type="speed"
                data-score="{{index + 1}}"
                bindtap="onStarTap"
              >
                <image src="/images/{{currentRating.speed >= (index + 1) ? 'star-active.png' : 'star.png'}}" mode="aspectFit"/>
              </view>
            </view>
          </view>
        </view>
        <view class="rating-item">
          <view class="rating-label">
            <text class="label">对抗</text>
            <text class="value">{{currentRating.combat}}分</text>
          </view>
          <view class="star-rating">
            <view class="star-group">
              <view
                class="star {{currentRating.combat >= (index + 1) ? 'active' : ''}}"
                wx:for="{{10}}"
                wx:key="index"
                data-type="combat"
                data-score="{{index + 1}}"
                bindtap="onStarTap"
              >
                <image src="/images/{{currentRating.combat >= (index + 1) ? 'star-active.png' : 'star.png'}}" mode="aspectFit"/>
              </view>
            </view>
          </view>
        </view>
        <view class="rating-item">
          <view class="rating-label">
            <text class="label">手感</text>
            <text class="value">{{currentRating.handling}}分</text>
          </view>
          <view class="star-rating">
            <view class="star-group">
              <view
                class="star {{currentRating.handling >= (index + 1) ? 'active' : ''}}"
                wx:for="{{10}}"
                wx:key="index"
                data-type="handling"
                data-score="{{index + 1}}"
                bindtap="onStarTap"
              >
                <image src="/images/{{currentRating.handling >= (index + 1) ? 'star-active.png' : 'star.png'}}" mode="aspectFit"/>
              </view>
            </view>
          </view>
        </view>
        <view class="rating-item">
          <view class="rating-label">
            <text class="label">颜值</text>
            <text class="value">{{currentRating.appearance}}分</text>
          </view>
          <view class="star-rating">
            <view class="star-group">
              <view
                class="star {{currentRating.appearance >= (index + 1) ? 'active' : ''}}"
                wx:for="{{10}}"
                wx:key="index"
                data-type="appearance"
                data-score="{{index + 1}}"
                bindtap="onStarTap"
              >
                <image src="/images/{{currentRating.appearance >= (index + 1) ? 'star-active.png' : 'star.png'}}" mode="aspectFit"/>
              </view>
            </view>
          </view>
        </view>
        <view class="rating-item">
          <view class="rating-label">
            <text class="label">性价比</text>
            <text class="value">{{currentRating.value}}分</text>
          </view>
          <view class="star-rating">
            <view class="star-group">
              <view
                class="star {{currentRating.value >= (index + 1) ? 'active' : ''}}"
                wx:for="{{10}}"
                wx:key="index"
                data-type="value"
                data-score="{{index + 1}}"
                bindtap="onStarTap"
              >
                <image src="/images/{{currentRating.value >= (index + 1) ? 'star-active.png' : 'star.png'}}" mode="aspectFit"/>
              </view>
            </view>
          </view>
        </view>

      </view>
      <view class="rating-tips">
        <text>* 综合评分为玩家评分结果，速度占比25%，手感占比25%，对抗占比20%，性价比占比15%，颜值占比15%</text>
      </view>
      <button class="submit-btn" bindtap="submitRating">提交评分</button>
    </view>
  </view>

  <!-- 悬浮反馈按钮 -->
  <view class="feedback-btn {{ isDragging ? 'dragging' : '' }}"
    bindtap="navigateToFeedback"
    bindtouchstart="onFeedbackBtnTouchStart"
    bindtouchmove="onFeedbackBtnTouchMove"
    bindtouchend="onFeedbackBtnTouchEnd"
    style="left: {{feedbackBtnLeft}}px; top: {{feedbackBtnTop}}px;">
    <image class="feedback-icon" src="/images/feedback.png" mode="aspectFit" />
    <text>反馈</text>
  </view>

  <!-- 拖动时的遮罩层 -->
  <view class="drag-mask {{isDragging ? 'show' : ''}}" catchtouchmove="preventTouchMove"></view>

  <!-- 返回顶部按钮 -->
  <view class="back-to-top {{showBackToTop ? 'show' : ''}}" bindtap="scrollToTop">
    <text class="back-to-top-icon">↑</text>
    <text>顶部</text>
  </view>

  <!-- 宠物图鉴入口按钮 -->
  <!--<view class="pet-guide-btn" bindtap="navigateToPetGuide">
    <text>宠物图鉴</text>
  </view>-->

  <!-- 添加评论弹窗 -->
  <view class="comment-modal {{showCommentModal ? 'show' : ''}}" catchtouchmove="preventClose">
    <view class="comment-modal-content">
      <view class="comment-modal-header">
        <text class="title">{{editingCommentId ? '编辑评论' : '写评论'}}</text>
        <text class="close-btn" bindtap="closeCommentModal">×</text>
      </view>
      <view class="comment-form">
        <textarea
          class="comment-textarea"
          placeholder="说说你的想法，请注意文明用语..."
          placeholder-class="comment-textarea-placeholder"
          maxlength="200"
          value="{{commentContent}}"
          bindinput="onCommentInput"
          adjust-position="{{true}}"
          show-confirm-bar="{{false}}"
          auto-height
        ></textarea>
        <view class="textarea-counter">{{commentContent.length}}/200</view>
      </view>
      <button class="submit-comment-btn" bindtap="submitComment">{{editingCommentId ? '保存修改' : '发表评论'}}</button>
    </view>
  </view>

  <!-- 广告按钮 -->
  <!--<ad-button left="20" top="300"></ad-button>-->

  <!-- 添加用于生成图片的canvas -->
  <canvas type="2d" id="compareCanvas" style="position: fixed; left: -9999px; visibility: hidden;"></canvas>

  <!-- 添加用于生成雷达图的canvas -->
  <canvas type="2d" id="radarCanvas" style="position: fixed; left: -9999px; visibility: hidden;"></canvas>

  <!-- 全屏图片预览弹窗 -->
  <view class="full-image-overlay {{showFullImageModal ? 'show' : ''}}" bindtap="hideFullImage">
    <view class="full-image-container" catchtap>
      <image
        class="full-image"
        src="{{fullImageUrl}}"
        mode="aspectFit"
        show-menu-by-longpress="true"
      />
      <view class="full-image-tips">长按图片可保存到手机</view>
      <view class="close-full-image-btn" catchtap="hideFullImage">×</view>
    </view>
  </view>

  <!-- 属性说明弹窗 -->
  <view class="attribute-help-overlay {{showAttributeHelp ? 'show' : ''}}" bindtap="hideAttributeHelp">
    <view class="attribute-help-container" catchtap>
      <view class="attribute-help-content">
        <view class="attribute-help-title">{{attributeHelpTitle}}</view>
        <view class="attribute-help-description">{{attributeHelpDescription}}</view>
      </view>
      <view class="attribute-help-close-btn" catchtap="hideAttributeHelp">×</view>
    </view>
  </view>
</view>