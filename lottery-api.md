# 抽奖活动排行榜 API 接口规范

本文档定义了抽奖小游戏排行榜功能的API接口规范，用于前端与后端的交互，支持多种抽奖活动。

## 基础信息

- **基础URL**: `https://pikario.site/api`
- **认证方式**: 直接传递openid参数 (不再使用Bearer Token)
- **数据格式**: JSON
- **时间格式**: ISO 8601 (例如: `2023-08-01T12:00:00Z`)
- **活动类型**:
  - `treasure-hunting`: 赛车夺宝
  - `supertreasure`: 至尊夺宝
  - `luckytree`: 幸运摇钱树

## 1. 获取排行榜数据

获取特定类型的排行榜数据和用户在排行榜中的排名。

### 请求

```
GET /lottery/rankings
```

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| activityType | string | 是 | 活动类型，可选值：`treasure-hunting`(赛车夺宝)、`supertreasure`(至尊夺宝)、`luckytree`(幸运摇钱树) |
| type | string | 是 | 排行榜类型，可选值：`rarity`(稀有度排行)、`draws`(抽奖次数排行) |
| limit | integer | 否 | 返回排行榜数量，默认为20，最大50 |
| page | integer | 否 | 分页页码，默认为1 |
| openid | string | 否 | 用户openid，提供此参数可获取该用户排名 |
| nickname | string | 否 | 用户昵称，可选，用于更新用户信息 |

### 响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "activityType": "treasure-hunting",
    "rankList": [
      {
        "id": "string",
        "rank": 1,
        "openid": "string", // 加密后的用户ID
        "nickName": "string",
        "rarityScore": 1000, // 稀有度得分，当type=rarity时有值
        "drawCount": 500, // 抽奖次数，当type=draws时有值
        "timestamp": "2023-08-01T12:00:00Z", // 记录更新时间
        "hasLegendaryItems": true, // 是否有传说级物品
        "hasTotalGoldItems": true // 是否有金色物品
      }
    ],
    "userRank": {
      "rank": 10,
      "openid": "string",
      "nickName": "string",
      "rarityScore": 600,
      "drawCount": 300,
      "timestamp": "2023-08-01T12:00:00Z"
    },
    "totalParticipants": 1000, // 总参与人数
    "lastUpdateTime": "2023-08-01T12:00:00Z" // 排行榜最后更新时间
  }
}
```

### 错误码

| 错误码 | 描述 |
|-------|------|
| 1001 | 参数错误 |
| 1002 | 排行榜类型不存在 |
| 1004 | 活动类型不存在 |
| 2001 | 服务器内部错误 |

## 2. 上传抽奖记录

上传用户的抽奖记录到排行榜。

### 请求

```
POST /lottery/records
```

### 请求头

```
Content-Type: application/json
```

### 请求体

```json
{
  "activityType": "treasure-hunting", // 活动类型
  "openid": "wx_openid_12345", // 用户的微信openid
  "nickname": "用户昵称", // 可选，用于更新用户信息
  "statistics": [  // 注意：此字段名为statistics而非items
    {
      "item": {
        "id": 1,
        "name": "物品名称",
        "background": "legendary", // 物品背景色：legendary, gold, purple, normal
        "probability": 0.00002 // 物品抽取概率，用于计算稀有度得分
      },
      "count": 2 // 抽到的数量
    }
  ],
  "totalDraws": 500, // 总抽奖次数，注意：此字段名为totalDraws而非drawCount
  "hasLegendaryItems": true, // 是否有传说级物品
  "hasTotalGoldItems": false // 是否有金色物品
}
```

### 响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "activityType": "treasure-hunting",
    "recordId": "string", // 记录ID
    "rarityScore": 800, // 计算出的稀有度得分
    "timestamp": "2023-08-01T12:00:00Z", // 上传时间
    "rank": {
      "rarityRank": 5, // 稀有度排名
      "drawsRank": 10 // 抽奖次数排名
    }
  }
}
```

### 错误码

| 错误码 | 描述 |
|-------|------|
| 1001 | 缺少openid参数 |
| 1003 | 上传频率限制，请稍后再试 |
| 1004 | 活动类型不存在 |
| 2001 | 服务器内部错误 |

## 3. 删除抽奖记录

删除用户已上传的抽奖记录。

### 请求

```
DELETE /lottery/records
```

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| activityType | string | 是 | 活动类型，可选值：`treasure-hunting`(赛车夺宝)、`supertreasure`(至尊夺宝)、`luckytree`(幸运摇钱树) |
| openid | string | 是 | 用户的微信openid |

### 响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "activityType": "treasure-hunting",
    "deleted": true
  }
}
```

### 错误码

| 错误码 | 描述 |
|-------|------|
| 1001 | 缺少openid参数 |
| 1004 | 活动类型不存在 |
| 4001 | 记录不存在 |
| 2001 | 服务器内部错误 |

## 4. 获取用户抽奖记录详情

获取用户已上传的抽奖记录详情。

### 请求

```
GET /lottery/record-detail
```

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| activityType | string | 是 | 活动类型，可选值：`treasure-hunting`(赛车夺宝)、`supertreasure`(至尊夺宝)、`luckytree`(幸运摇钱树) |
| openid | string | 是 | 用户的微信openid |
| nickname | string | 否 | 用户昵称，可选，用于更新用户信息 |

### 响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "activityType": "treasure-hunting",
    "id": "string",
    "openid": "string",
    "nickName": "string",
    "statistics": [  // 注意：此字段名为statistics而非items
      {
        "item": {
          "id": 1,
          "name": "物品名称",
          "background": "legendary", // legendary, gold, purple, normal
          "probability": 0.00002 // 物品抽取概率，用于计算稀有度得分
        },
        "count": 2
      }
    ],
    "totalDraws": 500,  // 注意：此字段名为totalDraws而非drawCount
    "rarityScore": 800, // 根据物品概率和抽奖次数计算得出
    "hasLegendaryItems": true, // 是否有传说级物品
    "hasTotalGoldItems": true, // 是否有金色物品
    "timestamp": "2023-08-01T12:00:00Z"
  }
}
```

### 错误码

| 错误码 | 描述 |
|-------|------|
| 1001 | 缺少openid参数 |
| 1004 | 活动类型不存在 |
| 4001 | 记录不存在 |
| 2001 | 服务器内部错误 |

## 稀有度得分计算规则

稀有度得分是根据用户抽到的永久奖励（传说级和金色物品）计算的，计算公式如下：

```
物品得分 = 1000 / 物品抽取概率
总物品得分 = 所有传说级和金色物品的 (物品得分 × 获得数量) 之和
稀有率系数 = 1 / 总抽奖次数
稀有度得分 = 总物品得分 × 稀有率系数
```

此计算方式体现了以下原则：
1. 只有传说级和金色物品（永久奖励）才计入得分
2. 抽取概率越低的物品，获得的得分越高，真实反映物品稀有度
3. 总抽奖次数越多，稀有率系数越低，体现了获得稀有物品的难易程度
4. 所有奖励的实际价值由其抽取概率决定，而非简单按颜色分类

举例：
- 抽取概率为0.00002的传说级物品价值为50分
- 抽取概率为0.0003的金色物品价值为3.333分
- 抽取概率为0.0008的金色物品价值为1.25分

此计算在服务端进行，确保计分的公平性和一致性。

## 请求示例

### 微信小程序获取排行榜示例

```javascript
wx.request({
  url: 'https://pikario.site/api/lottery/rankings',
  method: 'GET',
  data: {
    activityType: 'treasure-hunting',
    type: 'rarity',
    limit: 20,
    page: 1,
    openid: wx.getStorageSync('openid'),
    nickname: wx.getStorageSync('userInfo').nickName
  },
  success(res) {
    console.log('排行榜数据:', res.data);
  },
  fail(err) {
    console.error('获取排行榜失败:', err);
  }
});
```

### 上传抽奖记录示例

```javascript
wx.request({
  url: 'https://pikario.site/api/lottery/records',
  method: 'POST',
  data: {
    activityType: 'treasure-hunting',
    openid: wx.getStorageSync('openid'),
    nickname: wx.getStorageSync('userInfo').nickName,
    statistics: [  // 注意：此字段名为statistics而非items
      {
        item: {
          id: 1,
          name: '金色方程式',
          background: 'legendary',
          probability: 0.00002
        },
        count: 1
      }
    ],
    totalDraws: 500,  // 注意：此字段名为totalDraws而非drawCount
    hasLegendaryItems: true,
    hasTotalGoldItems: false
  },
  success(res) {
    console.log('上传成功:', res.data);
  },
  fail(err) {
    console.error('上传失败:', err);
  }
});
```