/* pages/games/game-2048/game-2048.wxss */
page {
  --grid-size: 4;
  --cell-size: 150rpx;
  --cell-margin: 5rpx;
  --grid-width: calc(var(--cell-size) * var(--grid-size) + var(--cell-margin) * (var(--grid-size) + 1));
  --grid-height: var(--grid-width);

  /* 字体大小 */
  --font-size-title: 32rpx;
  --font-size-normal: 26rpx;
  --font-size-small: 22rpx;
  --font-size-mini: 20rpx;

  /* 方块颜色 - 赛博朋克风格 */
  --tile-2-bg: #00FFFF; /* 霓虹青色 */
  --tile-2-color: #000000;
  --tile-4-bg: #FF00FF; /* 霓虹紫色 */
  --tile-4-color: #000000;
  --tile-8-bg: #FF2A6D; /* 霓虹粉色 */
  --tile-8-color: #FFFFFF;
  --tile-16-bg: #05FFA1; /* 霓虹绿色 */
  --tile-16-color: #000000;
  --tile-32-bg: #B967FF; /* 紫色 */
  --tile-32-color: #FFFFFF;
  --tile-64-bg: #01FFC3; /* 青绿色 */
  --tile-64-color: #000000;
  --tile-128-bg: #7700FF; /* 深紫色 */
  --tile-128-color: #FFFFFF;
  --tile-256-bg: #FF9E00; /* 橙色 */
  --tile-256-color: #000000;
  --tile-512-bg: #FF0050; /* 红色 */
  --tile-512-color: #FFFFFF;
  --tile-1024-bg: #0162FF; /* 蓝色 */
  --tile-1024-color: #FFFFFF;
  --tile-2048-bg: #FFFC00; /* 黄色 */
  --tile-2048-color: #000000;
}

/* 小屏幕手机适配 */
@media screen and (max-width: 320px) {
  page {
    --cell-size: 130rpx;
    --cell-margin: 6rpx;
    --font-size-title: 28rpx;
    --font-size-normal: 24rpx;
    --font-size-small: 20rpx;
    --font-size-mini: 18rpx;
  }
}

/* 大屏幕手机适配 */
@media screen and (min-width: 414px) {
  page {
    --cell-size: 170rpx;
    --cell-margin: 10rpx;
    --font-size-title: 36rpx;
    --font-size-normal: 28rpx;
    --font-size-small: 24rpx;
    --font-size-mini: 22rpx;
  }
}

.container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5rpx;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 添加一个半透明的遮罩层，使背景变暗，提高内容可读性 */
.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.header {
  text-align: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  position: relative;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #e0e0e0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* VIP徽章容器样式 */
.vip-badge-container {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  z-index: 100;
}

/* 游戏信息区域 */
.game-info {
  display: flex;
  justify-content: space-between;
  width: var(--grid-width);
  max-width: 98vw; /* 确保不超过视口宽度的98% */
  margin: 0 auto 20rpx;
  box-sizing: border-box;
}

.score-container, .best-container {
  background-color: rgba(10, 10, 30, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.5);
  border-radius: 12rpx;
  padding: 10rpx 20rpx;
  text-align: center;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.4);
  flex: 1;
  margin: 0 5rpx;
  box-sizing: border-box;
}

.score-label {
  font-size: var(--font-size-small);
  color: rgba(0, 255, 255, 0.8);
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.5);
  letter-spacing: 1px;
}

.score-value {
  font-size: var(--font-size-title);
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.7);
  letter-spacing: 1px;
}

/* 连击和最终得分信息 */
.bonus-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: var(--grid-width);
  max-width: 98vw; /* 确保不超过视口宽度的98% */
  margin: 0 auto 20rpx;
  background-color: rgba(10, 10, 30, 0.8);
  border: 1px solid rgba(255, 0, 255, 0.5); /* 霓虹紫色边框 */
  border-radius: 12rpx;
  padding: 10rpx;
  box-shadow: 0 0 10rpx rgba(255, 0, 255, 0.4); /* 紫色霓虹光效 */
  box-sizing: border-box;
}

.bonus-item {
  flex: 1;
  min-width: 30%;
  text-align: center;
  padding: 5rpx;
  border-right: 1px solid rgba(255, 0, 255, 0.2);
}

.bonus-item:last-child {
  border-right: none;
}

.bonus-label {
  font-size: var(--font-size-small);
  color: rgba(255, 0, 255, 0.8); /* 霓虹紫色 */
  text-shadow: 0 0 5rpx rgba(255, 0, 255, 0.5);
  letter-spacing: 1px;
}

.bonus-value {
  font-size: var(--font-size-normal);
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 0 10rpx rgba(255, 0, 255, 0.7);
  letter-spacing: 1px;
}

/* 游戏操作区域 */
.game-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: var(--grid-width);
  max-width: 98vw; /* 确保不超过视口宽度的98% */
  margin: 0 auto 20rpx;
  box-sizing: border-box;
}

.control-button {
  background-color: rgba(10, 10, 30, 0.8);
  color: #00FFFF; /* 霓虹青色 */
  border: 1px solid #00FFFF;
  border-radius: 8rpx;
  padding: 10rpx 15rpx;
  font-size: var(--font-size-small);
  text-align: center;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.5); /* 霓虹光效 */
  flex: 1;
  margin: 5rpx;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.7); /* 文字发光 */
  letter-spacing: 1px;
}

.control-button:active {
  transform: scale(0.95);
  background-color: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15rpx rgba(0, 255, 255, 0.8);
}

/* 游戏网格 */
.game-container {
  width: var(--grid-width);
  height: var(--grid-height);
  margin: 0 auto;
  position: relative;
  touch-action: none;
  max-width: 98vw; /* 确保不超过视口宽度的98% */
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

.game-grid {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: rgba(10, 10, 30, 0.95); /* 深色背景 */
  padding: var(--cell-margin);
  box-sizing: border-box;
  box-shadow: 0 0 20rpx rgba(0, 255, 255, 0.5), 0 0 40rpx rgba(255, 0, 255, 0.3); /* 霓虹光效 */
  border: 2rpx solid rgba(0, 255, 255, 0.5); /* 霓虹边框 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-background {
  display: grid;
  grid-template-columns: repeat(var(--grid-size), 1fr);
  grid-template-rows: repeat(var(--grid-size), 1fr);
  gap: var(--cell-margin);
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  align-items: center;
  justify-items: center;
}

.grid-cell {
  width: var(--cell-size);
  height: var(--cell-size);
  background-color: rgba(30, 30, 50, 0.7); /* 深色格子 */
  border-radius: 6rpx;
  box-shadow: inset 0 0 8rpx rgba(0, 255, 255, 0.2); /* 内发光效果 */
  border: 1rpx solid rgba(0, 255, 255, 0.1); /* 细微边框 */
  box-sizing: border-box;
}

.grid-tiles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(var(--grid-size), 1fr);
  grid-template-rows: repeat(var(--grid-size), 1fr);
  gap: var(--cell-margin);
  padding: var(--cell-margin);
  pointer-events: none;
}

.tile {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.15s ease, opacity 0.15s ease;
  box-sizing: border-box;
  pointer-events: auto;
  grid-column: span 1;
  grid-row: span 1;
}

.tile-inner {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 15rpx rgba(255, 255, 255, 0.5); /* 外发光效果 */
  border: 1rpx solid rgba(255, 255, 255, 0.3); /* 细微边框 */
  backdrop-filter: blur(2px); /* 模糊效果 */
}

.tile-image {
  width: calc(100% - 4rpx);
  height: calc(100% - 4rpx);
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  z-index: 1;
  object-fit: cover; /* 确保图片完全覆盖方块 */
  border-radius: 4rpx; /* 与方块保持一致的圆角，稍微减小以适应间距 */
}

.tile-value {
  font-size: calc(var(--cell-size) * 0.3);
  line-height: 1;
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  z-index: 2;
  color: #FFFFFF;
  -webkit-text-stroke: 1.5px #000000;
  text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.8);
  font-weight: bold;
  font-family: 'Arial', sans-serif;
}

/* 统一所有数字为镂空白底样式 */
.tile-value {
  color: #FFFFFF;
  -webkit-text-stroke: 1.5px #000000;
  text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.8);
}

/* 方块样式 */
.tile-2 .tile-inner {
  background-color: var(--tile-2-bg);
  color: var(--tile-2-color);
}

.tile-4 .tile-inner {
  background-color: var(--tile-4-bg);
  color: var(--tile-4-color);
}

.tile-8 .tile-inner {
  background-color: var(--tile-8-bg);
  color: var(--tile-8-color);
}

.tile-16 .tile-inner {
  background-color: var(--tile-16-bg);
  color: var(--tile-16-color);
}

.tile-32 .tile-inner {
  background-color: var(--tile-32-bg);
  color: var(--tile-32-color);
}

.tile-64 .tile-inner {
  background-color: var(--tile-64-bg);
  color: var(--tile-64-color);
}

.tile-128 .tile-inner {
  background-color: var(--tile-128-bg);
  color: var(--tile-128-color);
}

.tile-256 .tile-inner {
  background-color: var(--tile-256-bg);
  color: var(--tile-256-color);
}

.tile-512 .tile-inner {
  background-color: var(--tile-512-bg);
  color: var(--tile-512-color);
}

.tile-1024 .tile-inner {
  background-color: var(--tile-1024-bg);
  color: var(--tile-1024-color);
}

.tile-2048 .tile-inner {
  background-color: var(--tile-2048-bg);
  color: var(--tile-2048-color);
}

/* 调整大数字的字体大小 */
.tile-128 .tile-value, .tile-256 .tile-value, .tile-512 .tile-value {
  font-size: calc(var(--cell-size) * 0.25);
}

.tile-1024 .tile-value, .tile-2048 .tile-value {
  font-size: calc(var(--cell-size) * 0.2);
}

/* 动画效果 - 赛博朋克风格 */
.tile-new {
  animation: tile-appear 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.tile-merged {
  animation: tile-merge 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

@keyframes tile-appear {
  0% {
    opacity: 0;
    transform: scale(0);
    filter: brightness(2) hue-rotate(90deg);
  }
  50% {
    filter: brightness(1.5) hue-rotate(45deg);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1) hue-rotate(0);
  }
}

@keyframes tile-merge {
  0% {
    transform: scale(0.8);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.2);
    filter: brightness(1.8) hue-rotate(30deg);
  }
  75% {
    filter: brightness(1.4) hue-rotate(15deg);
  }
  100% {
    transform: scale(1);
    filter: brightness(1) hue-rotate(0);
  }
}

/* 添加持续的脉冲发光效果 */
.tile-inner {
  animation: pulse-glow 2s infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 15rpx rgba(255, 255, 255, 0.7), 0 0 30rpx rgba(0, 255, 255, 0.4);
  }
}

/* 游戏结束提示 */
.game-over {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.game-over.show {
  opacity: 1;
  visibility: visible;
}

.game-over-message {
  background-color: rgba(10, 10, 30, 0.95);
  border: 2px solid rgba(0, 255, 255, 0.5);
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 0 30rpx rgba(0, 255, 255, 0.5), 0 0 60rpx rgba(0, 255, 255, 0.3);
  max-width: 80%;
}

.game-over-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #00FFFF; /* 霓虹青色 */
  margin-bottom: 20rpx;
  text-shadow: 0 0 10rpx rgba(0, 255, 255, 0.7), 0 0 20rpx rgba(0, 255, 255, 0.4);
  letter-spacing: 2px;
}

.game-over-subtitle {
  font-size: 32rpx;
  color: #FF00FF; /* 霓虹紫色 */
  margin-bottom: 30rpx;
  text-shadow: 0 0 8rpx rgba(255, 0, 255, 0.6);
  letter-spacing: 1px;
}

/* 游戏结束统计信息 */
.game-over-stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 20rpx 0;
  background-color: rgba(20, 20, 40, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12rpx;
  padding: 15rpx;
  box-shadow: inset 0 0 10rpx rgba(0, 255, 255, 0.2);
}

.game-over-stat-item {
  flex: 1 0 45%;
  margin: 5rpx;
  padding: 10rpx;
  text-align: center;
  border-right: 1px solid rgba(0, 255, 255, 0.1);
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
}

.game-over-stat-item:nth-child(2n) {
  border-right: none;
}

.game-over-stat-item:nth-last-child(-n+2) {
  border-bottom: none;
}

.game-over-stat-label {
  font-size: 24rpx;
  color: rgba(0, 255, 255, 0.7);
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.3);
  letter-spacing: 1px;
}

.game-over-stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 0 8rpx rgba(0, 255, 255, 0.5);
  letter-spacing: 1px;
}

.game-over-score {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF00FF; /* 霓虹紫色 */
  margin: 30rpx 0;
  padding: 10rpx 0;
  border-top: 1px solid rgba(255, 0, 255, 0.3);
  border-bottom: 1px solid rgba(255, 0, 255, 0.3);
  text-shadow: 0 0 10rpx rgba(255, 0, 255, 0.6);
  letter-spacing: 1px;
}

.game-over-button {
  background-color: rgba(10, 10, 30, 0.8);
  color: #00FFFF; /* 霓虹青色 */
  border: 1px solid #00FFFF;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.5);
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.7);
  letter-spacing: 1px;
  transition: all 0.2s ease;
}

.game-over-button:active {
  transform: scale(0.95);
  background-color: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15rpx rgba(0, 255, 255, 0.8);
}

/* 底部区域 */
.footer {
  margin-top: 20rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.disclaimer-card {
  width: var(--grid-width);
  max-width: 98vw; /* 确保不超过视口宽度的98% */
  background-color: rgba(10, 10, 30, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 auto 20rpx;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.disclaimer {
  font-size: 24rpx;
  color: rgba(0, 255, 255, 0.7);
  text-align: center;
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.3);
  letter-spacing: 1px;
}

.back-button {
  background-color: rgba(10, 10, 30, 0.8);
  color: #FF00FF; /* 霓虹紫色 */
  border: 1px solid #FF00FF;
  border-radius: 30rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  box-shadow: 0 0 10rpx rgba(255, 0, 255, 0.5);
  transition: all 0.2s ease;
  text-shadow: 0 0 5rpx rgba(255, 0, 255, 0.7);
  letter-spacing: 1px;
  margin: 0 auto;
  display: block;
  width: fit-content;
}

.back-button:active {
  transform: scale(0.95);
  background-color: rgba(255, 0, 255, 0.2);
  box-shadow: 0 0 15rpx rgba(255, 0, 255, 0.8);
}

/* 用户信息输入弹窗 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-container.show {
  opacity: 1;
  pointer-events: auto;
}

.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: rgba(10, 10, 30, 0.95);
  border: 2px solid rgba(0, 255, 255, 0.5);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 0 20rpx rgba(0, 255, 255, 0.5), 0 0 40rpx rgba(0, 255, 255, 0.3);
  z-index: 1000;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal-container.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  background-color: rgba(0, 255, 255, 0.1);
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #00FFFF; /* 霓虹青色 */
  text-shadow: 0 0 8rpx rgba(0, 255, 255, 0.6);
  letter-spacing: 1px;
}

.close-button {
  font-size: 40rpx;
  color: rgba(0, 255, 255, 0.8);
  line-height: 1;
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.5);
}

.modal-body {
  padding: 30rpx;
}

.input-group {
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: rgba(0, 255, 255, 0.8);
  margin-bottom: 10rpx;
  display: block;
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.4);
  letter-spacing: 1px;
}

.input-field {
  width: 100%;
  height: 80rpx;
  background-color: rgba(20, 20, 40, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.5);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #FFFFFF;
  box-shadow: inset 0 0 10rpx rgba(0, 255, 255, 0.2);
}

.modal-tip {
  font-size: 24rpx;
  color: rgba(255, 0, 255, 0.8);
  margin-top: 10rpx;
  text-shadow: 0 0 5rpx rgba(255, 0, 255, 0.4);
  letter-spacing: 1px;
}

.modal-footer {
  display: flex;
  border-top: 1px solid rgba(0, 255, 255, 0.3);
  background-color: rgba(0, 255, 255, 0.05);
}

.modal-button {
  flex: 1;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  transition: all 0.2s ease;
}

.modal-button.cancel {
  color: rgba(255, 255, 255, 0.7);
  border-right: 1px solid rgba(0, 255, 255, 0.3);
  text-shadow: 0 0 5rpx rgba(255, 255, 255, 0.3);
}

.modal-button.confirm {
  color: #00FFFF;
  font-weight: bold;
  text-shadow: 0 0 8rpx rgba(0, 255, 255, 0.6);
  letter-spacing: 1px;
}

.modal-button:active {
  background-color: rgba(0, 255, 255, 0.2);
}
