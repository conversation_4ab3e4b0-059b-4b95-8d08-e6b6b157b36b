Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    pageKey: {
      type: String,
      value: ''
    },
    simpleMode: {
      type: Boolean,
      value: false
    },
    isVip: {
      type: Boolean,
      value: false
    },
    vipRemainingDays: {
      type: Number,
      value: 0
    },
    freeCount: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    videoAdLoaded: false,
    selectedPlan: 'month' // 默认选择月卡
  },

  lifetimes: {
    attached() {
      this.initVideoAd();
    },
    detached() {
      if (this.videoAd) {
        this.videoAd.offLoad();
        this.videoAd.offError();
        this.videoAd.offClose();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    initVideoAd() {
      if (wx.createRewardedVideoAd) {
        this.videoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-919b8b04a2997a01'
        });

        this.videoAd.onLoad(() => {
          this.setData({ videoAdLoaded: true });
          console.log('激励视频广告加载成功');
        });

        this.videoAd.onError((err) => {
          console.error('激励视频广告加载失败', err);
          this.setData({ videoAdLoaded: false });
          wx.showToast({
            title: '广告加载失败，请稍后再试',
            icon: 'none'
          });
        });

        this.videoAd.onClose((res) => {
          // 用户点击了【关闭广告】按钮
          if (res && res.isEnded || res === undefined) {
            // 正常播放结束，可以获得奖励
            this.addFreeCountAfterAd();
          } else {
            // 播放中途退出，不给予奖励
            wx.showToast({
              title: '观看完整广告才能获得抽奖次数',
              icon: 'none'
            });
          }
        });
      } else {
        console.warn('当前基础库版本不支持激励视频广告');
      }
    },

    watchAd() {
      if (this.videoAd && this.data.videoAdLoaded) {
        this.videoAd.show().catch(() => {
          // 失败重试
          this.videoAd.load()
            .then(() => this.videoAd.show())
            .catch(err => {
              console.error('激励视频广告显示失败', err);
              wx.showToast({
                title: '广告显示失败，请稍后再试',
                icon: 'none'
              });
            });
        });
      } else {
        wx.showToast({
          title: '广告加载中，请稍后再试',
          icon: 'none'
        });
      }
    },

    prevent() {
      // 阻止冒泡
      return false;
    },

    closeDialog() {
      this.triggerEvent('close');
    },

    addFreeCount() {
      if (this.videoAd && this.data.videoAdLoaded) {
        wx.showLoading({
          title: '广告加载中...'
        });

        this.videoAd.show().catch(() => {
          // 失败重试
          this.videoAd.load()
            .then(() => {
              wx.hideLoading();
              return this.videoAd.show();
            })
            .catch(err => {
              wx.hideLoading();
              console.error('激励视频广告显示失败', err);
              wx.showToast({
                title: '广告显示失败，请稍后再试',
                icon: 'none'
              });
            });
        }).then(() => {
          wx.hideLoading();
        });
      } else {
        wx.showToast({
          title: '广告加载中，请稍后再试',
          icon: 'none'
        });

        // 尝试重新加载广告
        if (this.videoAd) {
          this.videoAd.load();
        }
      }
    },

    /**
     * 选择VIP套餐
     * @param {Object} e - 事件对象
     */
    selectPlan(e) {
      const plan = e.currentTarget.dataset.plan;
      this.setData({ selectedPlan: plan });
    },

    /**
     * 购买VIP会员
     */
    buyVip() {
      const plan = this.data.selectedPlan;
      let amount = 0;
      let days = 0;

      // 获取App实例，使用全局价格常量
      const app = getApp();

      // 根据选择的套餐设置金额和天数
      if (plan === 'month') {
        // 使用page-with-vip.js中定义的常量
        const VIP_CONSTANTS = app.globalData.VIP_CONSTANTS || {
          MONTH_PRICE: 1,
          MONTH_DAYS: 31
        };
        amount = VIP_CONSTANTS.MONTH_PRICE;
        days = VIP_CONSTANTS.MONTH_DAYS;
      } else if (plan === 'year') {
        // 使用page-with-vip.js中定义的常量
        const VIP_CONSTANTS = app.globalData.VIP_CONSTANTS || {
          YEAR_PRICE: 1,
          YEAR_DAYS: 365
        };
        amount = VIP_CONSTANTS.YEAR_PRICE;
        days = VIP_CONSTANTS.YEAR_DAYS;
      }

      console.log(`VIP购买 - 选择${plan}套餐，金额${amount/100}元，${days}天`);

      // 显示确认对话框
      wx.showModal({
        title: '确认购买',
        content: `即将支付${amount/100}元购买${days}天VIP会员，确认继续吗？`,
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定
            this.processPayment(plan, amount, days);
          }
        }
      });
    },

    /**
     * 处理支付流程
     * @param {string} plan - 套餐类型：month或year
     * @param {number} amount - 支付金额，单位分
     * @param {number} days - VIP天数
     */
    processPayment(plan, amount, days) {
      wx.showLoading({
        title: '正在处理...',
        mask: true
      });

      console.log(`开始处理VIP支付：${plan}套餐，${amount/100}元，${days}天`);

      // 获取全局App实例
      const app = getApp();

      // 在弹窗组件中直接调用通用VIP购买接口
      if (app && app.processVipPurchase) {
        // 如果全局有统一处理方法，使用全局方法
        app.processVipPurchase({
          plan,
          amount,
          days
        }).then(() => {
          this.closeDialog();
        }).catch(error => {
          console.error('VIP购买失败:', error);
          wx.showToast({
            title: '购买失败，请重试',
            icon: 'none'
          });
        }).finally(() => {
          wx.hideLoading();
        });
      } else {
        // 如果没有全局方法，显示错误提示
        wx.hideLoading();
        wx.showToast({
          title: '支付功能未初始化',
          icon: 'none'
        });
      }
    },

    /**
     * 广告观看完成后增加免费次数
     * @returns {void}
     */
    addFreeCountAfterAd() {
      // 获取当前页面标识
      const pageKey = this.properties.pageKey || 'default';

      // 根据页面类型设置不同的奖励次数
      let rewardCount = 100; // 默认为100次
      if (pageKey === 'prize-list' || pageKey === 'prize-query') {
        rewardCount = 3; // 奖品查询页面为3次
      }

      // 计算新的免费次数
      let newFreeCount;

      // 在奖品查询页面
      if (pageKey === 'prize-list' || pageKey === 'prize-query') {
        // 如果当前次数为0，重置为3次；否则累加3次
        if (this.data.freeCount === 0) {
          newFreeCount = rewardCount; // 重置为3次
          console.log(`VIP对话框 - 重置广告奖励次数: ${this.data.freeCount} -> ${newFreeCount} (重置为${rewardCount}次)`);
        } else {
          newFreeCount = this.data.freeCount + rewardCount; // 累加3次
          console.log(`VIP对话框 - 增加广告奖励次数: ${this.data.freeCount} -> ${newFreeCount} (奖励${rewardCount}次)`);
        }
      } else {
        // 其他页面始终累加
        newFreeCount = this.data.freeCount + rewardCount;
        console.log(`VIP对话框 - 增加广告奖励次数: ${this.data.freeCount} -> ${newFreeCount} (奖励${rewardCount}次)`);
      }

      // 1. 更新组件内部数据
      this.setData({
        freeCount: newFreeCount
      });

      // 2. 使用页面特定的键名保存免费次数
      try {
        // 生成特定于当前页面的存储键
        const storageKey = `freeCount_${pageKey}`;

        // 保存页面特定的免费次数
        wx.setStorageSync(storageKey, newFreeCount);
        console.log(`保存页面[${pageKey}]特定免费次数: ${newFreeCount}`);

        // 同时更新通用键，用于VIP徽章显示
        const currentCounts = wx.getStorageSync('allFreeCounts') || {};
        currentCounts[pageKey] = newFreeCount;
        wx.setStorageSync('allFreeCounts', currentCounts);
      } catch (e) {
        console.error('保存免费次数失败', e);
      }

      // 3. 通知父组件更新，传递页面标识
      this.triggerEvent('freeCountUpdated', {
        freeCount: newFreeCount,
        pageKey: pageKey,
        source: 'adReward'
      });

      // 4. 更新全局数据
      if (getApp() && getApp().globalData) {
        const app = getApp();

        // 如果全局数据中没有freeCounts对象，则创建
        if (!app.globalData.freeCounts) {
          app.globalData.freeCounts = {};
        }

        // 更新特定页面的免费次数
        app.globalData.freeCounts[pageKey] = newFreeCount;

        // 触发全局事件
        if (app.globalEventEmitter) {
          app.globalEventEmitter.emit('freeCountChanged', {
            pageKey: pageKey,
            count: newFreeCount
          });
        }
      }

      // 5. 直接更新当前页面
      const currentPage = getCurrentPages().slice(-1)[0];
      if (currentPage) {
        // 检查页面是否有更新方法
        if (typeof currentPage.updateLotteryCount === 'function') {
          currentPage.updateLotteryCount(newFreeCount, pageKey);
        } else {
          // 如果没有特定更新方法，尝试直接更新数据
          try {
            const dataKey = `${pageKey}FreeCount`;
            const updateData = {};
            updateData[dataKey] = newFreeCount;
            updateData.freeCount = newFreeCount; // 兼容通用键名
            currentPage.setData(updateData);
          } catch (e) {
            console.error('更新页面数据失败', e);
          }
        }
      }

      // 6. 提示用户
      wx.showToast({
        title: `已获得${rewardCount}次免费${pageKey === 'prize-list' || pageKey === 'prize-query' ? '查询' : '模拟'}次数`,
        icon: 'none',
        duration: 2500
      });

      // 7. 延迟关闭对话框
      setTimeout(() => {
        this.closeDialog();
      }, 1500);
    },

    /**
     * 处理购买/续费按钮点击
     */
    handleBuyTap() {
      this.triggerEvent('buy');
    },

    /**
     * 处理关闭按钮点击
     */
    handleClose() {
      this.triggerEvent('close');
    }
  }
})