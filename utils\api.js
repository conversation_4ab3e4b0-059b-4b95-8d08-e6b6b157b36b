/**
 * 微信小程序API接口封装
 */

// API基础URL，根据实际情况修改
const BASE_URL = 'https://api.example.com';

/**
 * 登录并获取用户信息
 * @returns {Promise} 返回登录结果
 */
const login = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (res) => {
        if (res.code) {
          wx.request({
            url: `${BASE_URL}/login/`,
            method: 'POST',
            data: {
              code: res.code
            },
            success: (response) => {
              const data = response.data;
              if (data.success) {
                // 保存用户信息到本地存储
                wx.setStorageSync('token', data.token);
                wx.setStorageSync('openid', data.openid);
                wx.setStorageSync('is_vip', data.is_vip);
                wx.setStorageSync('is_valid_vip', data.is_valid_vip);
                wx.setStorageSync('vip_expire_at', data.vip_expire_at);
                resolve(data);
              } else {
                reject(data.message || '登录失败');
              }
            },
            fail: (err) => {
              reject(err);
            }
          });
        } else {
          reject('登录失败: ' + res.errMsg);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

/**
 * 获取用户VIP信息
 * @returns {Promise} 返回用户VIP信息
 */
const getVipInfo = () => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    const openid = wx.getStorageSync('openid');

    if (!token || !openid) {
      reject('未登录，请先登录');
      return;
    }

    wx.request({
      url: `${BASE_URL}/user/vip/info/?openid=${openid}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        const data = res.data;
        if (data.success) {
          // 更新本地存储的VIP信息
          wx.setStorageSync('is_vip', data.data.is_vip);
          wx.setStorageSync('is_valid_vip', data.data.is_valid_vip);
          wx.setStorageSync('vip_expire_at', data.data.vip_expire_at);
          resolve(data.data);
        } else {
          reject(data.message || '获取VIP信息失败');
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

/**
 * 设置用户VIP状态
 * @param {boolean} isVip - 是否为VIP
 * @param {number} days - 天数（已更新为31天，给用户多1天）
 * @returns {Object} VIP状态和到期时间
 */
const setVip = (isVip, days) => {
  const now = new Date();
  const expireDate = new Date(now);

  // 使用传入的天数设置过期日期
  expireDate.setDate(expireDate.getDate() + days);

  // 格式化日期为YYYY-MM-DD
  const expireAt = `${expireDate.getFullYear()}-${(expireDate.getMonth() + 1).toString().padStart(2, '0')}-${expireDate.getDate().toString().padStart(2, '0')}`;

  // 保存VIP状态到本地
  wx.setStorageSync('is_vip', isVip);
  wx.setStorageSync('is_valid_vip', isVip);
  wx.setStorageSync('vip_expire_at', expireAt);

  return { isVip, expireAt };
};

/**
 * 初始化免费次数
 * @param {string} pageKey 页面标识
 * @param {number} customInitialCount 自定义初始免费次数（可选）
 */
const initFreeCount = (pageKey, customInitialCount = null) => {
  const key = `freeCount_${pageKey}`;
  const freeCount = wx.getStorageSync(key);

  if (freeCount === '' || freeCount === undefined) {
    // 如果提供了自定义初始值，则使用它
    if (customInitialCount !== null) {
      wx.setStorageSync(key, customInitialCount);
      return customInitialCount;
    }

    // 否则根据页面标识设置不同的初始值
    let initialFreeCount = 100; // 默认为100次

    if (pageKey === 'treasure-hunting') {
      initialFreeCount = 100; // 赛车夺宝页面100次
    } else if (pageKey === 'luckytree') {
      initialFreeCount = 100; // 幸运摇钱树页面100次
    } else if (pageKey === 'supertreasure') {
      initialFreeCount = 100; // 至尊夺宝页面100次
    } else if (pageKey === 'prize-list' || pageKey === 'prize-query') {
      initialFreeCount = 3; // 奖品查询页面3次
    } else if (pageKey === 'game-2048') {
      initialFreeCount = 1; // 2048游戏每天1次免费体验
    } else if (pageKey === 'car-calculation') {
      initialFreeCount = 3; // 赛车推进计算与绘图页面3次
    }

    console.log(`初始化页面 ${pageKey} 的免费次数为: ${initialFreeCount}`);
    wx.setStorageSync(key, initialFreeCount);
    return initialFreeCount;
  }

  return freeCount;
};

/**
 * 获取当前页面的免费次数
 * @param {string} pageKey 页面标识
 * @returns {number} 剩余免费次数
 */
const getFreeCount = (pageKey) => {
  // 确保每个页面都有独立的存储键
  const key = `freeCount_${pageKey}`;
  const freeCount = wx.getStorageSync(key);

  // 如果是第一次访问该页面，给予初始免费次数
  if (freeCount === '' || freeCount === undefined) {
    // 根据不同页面设置不同的初始免费次数
    let initialFreeCount = 100; // 默认为100次

    // 根据页面标识设置不同的初始值
    if (pageKey === 'treasure-hunting') {
      initialFreeCount = 100; // 赛车夺宝页面100次
    } else if (pageKey === 'luckytree') {
      initialFreeCount = 100; // 幸运摇钱树页面100次
    } else if (pageKey === 'supertreasure') {
      initialFreeCount = 100; // 至尊夺宝页面100次
    } else if (pageKey === 'prize-list' || pageKey === 'prize-query') {
      initialFreeCount = 3; // 奖品查询页面3次
    } else if (pageKey === 'game-2048') {
      initialFreeCount = 1; // 2048游戏每天1次免费体验
    } else if (pageKey === 'car-calculation') {
      initialFreeCount = 3; // 赛车推进计算与绘图页面3次
    }

    console.log(`初始化页面 ${pageKey} 的免费次数为: ${initialFreeCount}`);
    wx.setStorageSync(key, initialFreeCount);
    return initialFreeCount;
  }

  return freeCount;
};

/**
 * 更新免费次数
 * @param {string} pageKey 页面标识
 * @param {number} count 次数变化值
 * @returns {number} 更新后的免费次数
 */
const updateFreeCount = (pageKey, count) => {
  // 确保每个页面都有独立的存储键
  const key = `freeCount_${pageKey}`;
  let freeCount = wx.getStorageSync(key) || 0;

  freeCount += count;
  if (freeCount < 0) freeCount = 0;

  wx.setStorageSync(key, freeCount);
  return freeCount;
};

/**
 * 检查是否为有效的VIP用户
 * @returns {boolean} 是否为有效VIP
 */
const isValidVip = () => {
  const isVip = wx.getStorageSync('is_vip');
  const isValidVip = wx.getStorageSync('is_valid_vip');
  return isVip && isValidVip;
};

/**
 * 检查是否为VIP用户（简化版）
 * @returns {boolean} 是否为VIP
 */
const isVip = () => {
  return isValidVip();
};

/**
 * 获取VIP到期时间的格式化显示
 * @returns {string} 格式化的到期时间
 */
const getVipExpireFormatted = () => {
  const expireAt = wx.getStorageSync('vip_expire_at');
  if (!expireAt) return '';

  const expireDate = new Date(expireAt);
  return `${expireDate.getFullYear()}-${(expireDate.getMonth() + 1).toString().padStart(2, '0')}-${expireDate.getDate().toString().padStart(2, '0')}`;
};

/**
 * 获取VIP剩余天数
 * @returns {number} 剩余天数
 */
const getVipRemainingDays = () => {
  const expireAt = wx.getStorageSync('vip_expire_at');
  if (!expireAt) return 0;

  const expireDate = new Date(expireAt);
  const now = new Date();

  const diffTime = expireDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
};

/**
 * 更新用户信息
 * @param {Object} userInfo - 用户信息对象
 * @returns {Promise} 返回更新结果
 */
const updateUserInfo = (userInfo) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    const openid = wx.getStorageSync('openid');

    if (!token || !openid) {
      reject('未登录，请先登录');
      return;
    }

    wx.request({
      url: `${BASE_URL}/api/update_user_info/`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: {
        openid: openid,
        nickname: userInfo.nickName,
        avatar_url: userInfo.avatarUrl,
        gender: userInfo.gender,
        country: userInfo.country,
        province: userInfo.province,
        city: userInfo.city
      },
      success: (res) => {
        const data = res.data;
        if (data.success) {
          // 更新成功
          wx.setStorageSync('userInfo', JSON.stringify(userInfo));
          resolve(data);
        } else {
          reject(data.message || '更新用户信息失败');
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

/**
 * 通用请求方法
 * @param {string} url - 请求URL（完整URL或相对路径）
 * @param {Object} options - 请求选项
 * @param {string} options.method - 请求方法，默认GET
 * @param {Object} options.data - 请求数据
 * @param {Object} options.header - 请求头
 * @param {number} options.timeout - 超时时间，默认10秒
 * @returns {Promise} 返回请求结果
 */
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const {
      method = 'GET',
      data = {},
      header = {},
      timeout = 10000
    } = options;

    // 直接使用传入的URL，不做额外处理
    const fullUrl = url;

    // 设置默认请求头
    const defaultHeader = {
      'Content-Type': 'application/json'
    };

    // 如果有token，添加到请求头
    const token = wx.getStorageSync('token');
    if (token) {
      defaultHeader['Authorization'] = `Bearer ${token}`;
    }

    // 合并请求头
    const finalHeader = { ...defaultHeader, ...header };

    console.log(`API请求: ${method} ${fullUrl}`, data);

    // 创建超时Promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeout);
    });

    // 创建请求Promise
    const requestPromise = new Promise((resolve, reject) => {
      wx.request({
        url: fullUrl,
        method: method,
        data: data,
        header: finalHeader,
        success: (res) => {
          console.log(`API响应: ${method} ${fullUrl}`, res.data);

          // 检查HTTP状态码
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
          }
        },
        fail: (err) => {
          console.error(`API请求失败: ${method} ${fullUrl}`, err);
          reject(err);
        }
      });
    });

    // 使用Promise.race来处理超时
    Promise.race([requestPromise, timeoutPromise])
      .then(resolve)
      .catch(reject);
  });
};

module.exports = {
  login,
  getVipInfo,
  initFreeCount,
  getFreeCount,
  updateFreeCount,
  isValidVip,
  isVip,
  setVip,
  getVipExpireFormatted,
  getVipRemainingDays,
  updateUserInfo,
  request
};