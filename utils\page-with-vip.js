// 创建一个基类，包含通用的VIP处理逻辑
const app = getApp();

// 使用全局VIP常量
const VIP_CONSTANTS = app.globalData.VIP_CONSTANTS || {
  // 价格 (单位: 分)
  MONTH_PRICE: 1,   // 月卡价格: 0.01元
  YEAR_PRICE: 1,    // 年卡价格: 0.01元

  // 天数
  MONTH_DAYS: 31,     // 月卡天数
  YEAR_DAYS: 365,     // 年卡天数
};

const PageWithVIP = (options) => {
  // 保存原有的生命周期函数
  const { onLoad, onShow, onUnload, onHide } = options;

  return Page({
    ...options,

    onLoad(query) {
      // 初始化VIP状态
      this.initVipStatus();

      // 监听VIP状态变化
      if (app.globalEventEmitter) {
        app.globalEventEmitter.on('vipStatusChanged', this.handleVipStatusChanged.bind(this));
      }

      // 调用原有的onLoad
      if (onLoad) {
        onLoad.call(this, query);
      }
    },

    onShow() {
      // 每次显示页面时刷新VIP状态
      this.refreshVipStatus();

      // 调用原有的onShow
      if (onShow) {
        onShow.call(this);
      }
    },

    onHide() {
      if (onHide) {
        onHide.call(this);
      }
    },

    onUnload() {
      // 清理事件监听
      if (app.globalEventEmitter) {
        app.globalEventEmitter.off('vipStatusChanged', this.handleVipStatusChanged.bind(this));
      }

      // 调用原有的onUnload
      if (onUnload) {
        onUnload.call(this);
      }
    },

    // VIP相关方法
    async initVipStatus() {
      try {
        // 优先从全局变量获取VIP状态
        const app = getApp();
        if (app && app.globalData) {
          const isVip = app.globalData.isVip;
          const vipRemainingDays = app.globalData.vipRemainingDays;

          if (isVip !== undefined && vipRemainingDays !== undefined) {
            console.log('从全局变量获取VIP状态:', { isVip, vipRemainingDays });
            this.setData({
              isVip: isVip,
              vipRemainingDays: vipRemainingDays
            });
            return;
          }
        }

        // 其次从缓存获取
        const cachedVipInfo = wx.getStorageSync('vipInfo');
        if (cachedVipInfo) {
          console.log('从缓存获取VIP状态');
          this.updateVipStatus(cachedVipInfo);
          return;
        }

        // 最后才从服务器获取
        console.log('从服务器获取VIP状态');
        const vipInfo = await app.getVipInfo(false, true);
        if (vipInfo) {
          this.updateVipStatus(vipInfo);
        }
      } catch (error) {
        console.error('初始化VIP状态失败:', error);
      }
    },

    async refreshVipStatus() {
      try {
        // 强制从服务器刷新VIP状态
        console.log('强制刷新VIP状态');
        const vipInfo = await app.getVipInfo(true, false);
        if (vipInfo) {
          this.updateVipStatus(vipInfo);
        }
      } catch (error) {
        console.error('刷新VIP状态失败:', error);
        // 尝试从缓存获取
        const cachedVipInfo = wx.getStorageSync('vipInfo');
        if (cachedVipInfo) {
          this.updateVipStatus(cachedVipInfo);
        }
      }
    },

    updateVipStatus(vipInfo) {
      if (!vipInfo) return;

      // 重新计算剩余天数
      if (vipInfo.is_valid_vip && vipInfo.vip_expire_at) {
        const expireDate = new Date(vipInfo.vip_expire_at);
        const now = new Date();
        const diffTime = expireDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        vipInfo.remainingDays = Math.max(0, diffDays);

        console.log('VIP状态更新 - 过期时间:', vipInfo.vip_expire_at);
        console.log('VIP状态更新 - 当前时间:', now.toISOString());
        console.log('VIP状态更新 - 剩余天数:', vipInfo.remainingDays);
      }

      this.setData({
        isVip: vipInfo.is_valid_vip,
        vipRemainingDays: vipInfo.remainingDays || 0
      });

      // 更新VIP徽章组件
      const vipBadge = this.selectComponent('#vipBadge');
      if (vipBadge) {
        vipBadge.updateVipStatus(vipInfo);
      }
    },

    handleVipStatusChanged(vipInfo) {
      this.updateVipStatus(vipInfo);
    },

    /**
     * 处理VIP购买/续费
     * @param {Object} planData - 套餐数据，包含plan、amount和days
     */
    async onBuyVip(e) {
      try {
        // 获取传递的套餐数据
        const planData = e && e.detail || e || {};

        // 转换plan类型为对应的天数和金额
        if (planData.plan === 'year' && !planData.days) {
          planData.days = VIP_CONSTANTS.YEAR_DAYS;
          if (!planData.amount) {
            planData.amount = VIP_CONSTANTS.YEAR_PRICE;
          }
        } else if (planData.plan === 'month' && !planData.days) {
          planData.days = VIP_CONSTANTS.MONTH_DAYS;
          if (!planData.amount) {
            planData.amount = VIP_CONSTANTS.MONTH_PRICE;
          }
        } else if (!planData.days) {
          planData.days = VIP_CONSTANTS.MONTH_DAYS;
          if (!planData.amount) {
            planData.amount = VIP_CONSTANTS.MONTH_PRICE;
          }
        }

        console.log('处理VIP购买参数:', planData);

        // 调用PageWithVIP中的通用VIP购买方法
        if (this.onVipDialogBuy) {
          this.onVipDialogBuy({detail: planData});
        } else {
          // 兼容未继承PageWithVIP的页面
          this.mockBuyVipSuccess(planData);
        }
      } catch (error) {
        console.error('购买VIP失败:', error);
        wx.showToast({
          title: '购买失败，请重试',
          icon: 'none'
        });
      } finally {
        wx.hideLoading();
      }
    },

    /**
     * 模拟购买VIP成功
     * @param {Object} planData - 套餐数据，包含plan和days
     */
    mockBuyVipSuccess(planData) {
      // 根据套餐类型设置天数
      let days = VIP_CONSTANTS.MONTH_DAYS; // 默认月卡天数

      if (planData) {
        if (planData.days) {
          // 如果传入了具体天数，直接使用
          days = planData.days;
        } else if (planData.plan === 'year') {
          // 年卡设置为365天
          days = VIP_CONSTANTS.YEAR_DAYS;
        } else if (planData.plan === 'month') {
          // 月卡设置为31天
          days = VIP_CONSTANTS.MONTH_DAYS;
        }
      }

      console.log(`模拟购买VIP: ${days}天`);

      // 获取当前VIP信息，检查是否是续费
      const cachedVipInfo = wx.getStorageSync('vipInfo');
      const isRenewal = cachedVipInfo && cachedVipInfo.is_valid_vip;

      // 设置VIP状态
      let expireDate;

      if (isRenewal && cachedVipInfo.vip_expire_at) {
        // 续费：从当前过期时间开始计算
        const currentExpireDate = new Date(cachedVipInfo.vip_expire_at);
        console.log('续费前过期时间:', currentExpireDate.toISOString());

        // 使用毫秒计算以确保准确性
        const millisecondsPerDay = 24 * 60 * 60 * 1000;
        expireDate = new Date(currentExpireDate.getTime() + days * millisecondsPerDay);
      } else {
        // 新开通：从当前时间开始计算
        const now = new Date();
        const millisecondsPerDay = 24 * 60 * 60 * 1000;
        expireDate = new Date(now.getTime() + days * millisecondsPerDay);
      }

      const expireAt = expireDate.toISOString();
      console.log(`设置VIP过期时间 (${days}天):`, expireAt);

      // 更新本地VIP状态
      wx.setStorageSync('is_vip', true);
      wx.setStorageSync('is_valid_vip', true);
      wx.setStorageSync('vip_expire_at', expireAt);

      // 创建新的VIP信息
      const vipInfo = {
        is_vip: true,
        is_valid_vip: true,
        vip_expire_at: expireAt,
        remainingDays: this.calculateRemainingDays(expireAt)
      };

      // 更新缓存中的完整VIP信息
      wx.setStorageSync('vipInfo', vipInfo);

      // 如果页面有updateVipAndFreeCount方法则调用
      if (this.updateVipAndFreeCount) {
        this.updateVipAndFreeCount();
      } else if (this.refreshVipStatus) {
        this.refreshVipStatus();
      } else {
        // 直接更新VIP状态
        this.updateVipStatus(vipInfo);
      }

      // 关闭VIP对话框
      this.setData({
        showVipDialog: false
      });

      wx.showToast({
        title: isRenewal ? 'VIP续费成功' : 'VIP开通成功',
        icon: 'success'
      });

      return vipInfo;
    },

    /**
     * VIP对话框购买事件
     * @param {Object} e - 事件对象，包含套餐信息
     */
    async onVipDialogBuy(e) {
      try {
        // 确保参数正确传递
        const planData = e.detail || {};

        // 确保年卡正确设置天数
        if (planData.plan === 'year' && (!planData.days || planData.days !== VIP_CONSTANTS.YEAR_DAYS)) {
          planData.days = VIP_CONSTANTS.YEAR_DAYS;
          planData.amount = planData.amount || VIP_CONSTANTS.YEAR_PRICE;
          console.log('已修正年卡参数:', planData);
        }

        console.log('VIP对话框购买参数:', planData);

        // 判断是否为测试环境
        const isTestEnv = wx.getStorageSync('isTestEnv') || false;

        if (isTestEnv) {
          // 测试环境使用模拟购买
          console.log('测试环境，使用模拟购买');
          this.mockBuyVipSuccess(planData);
        } else {
          // 直接调用app.js中的处理方法，统一支付逻辑
          console.log('生产环境，使用app.js中的支付处理流程');
          const app = getApp();
          await app.processVipPurchase(planData);

          // 支付流程完成后关闭对话框
          this.setData({
            showVipDialog: false
          });
        }
      } catch (error) {
        console.error('VIP购买失败:', error);
        wx.showToast({
          title: '购买失败，请重试',
          icon: 'none'
        });
      }
    },

    /**
     * VIP对话框关闭事件
     */
    onVipDialogClose() {
      this.setData({
        showVipDialog: false
      });
    },

    /**
     * VIP徽章点击事件
     */
    onVipBadgeTap() {
      this.setData({
        showVipDialog: true
      });
    },

    /**
     * 计算剩余天数
     * @param {string} expireAtStr - 过期时间ISO字符串
     * @returns {number} 剩余天数
     */
    calculateRemainingDays(expireAtStr) {
      if (!expireAtStr) return 0;

      const expireDate = new Date(expireAtStr);
      const now = new Date();
      return Math.max(0, Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24)));
    },
  });
};

export default PageWithVIP;