<view class="container">
  <!-- 背景图片独立设置 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <view class="header">
    <view class="title">抽奖模拟器</view>
    <view class="subtitle">飞车抽奖模拟，带你体验真实概率</view>

    <!-- 使用统一的VIP徽章组件 -->
    <view class="vip-badge-container">
      <vip-badge id="vipBadge"
        pageType="common"
        isVip="{{isVip}}"
        remainingDays="{{vipRemainingDays}}"
        bindtap="onVipBadgeTap"
      />
    </view>
  </view>

  <view class="activity-list">
    <view class="activity-item {{loadingActivityId === item.id ? 'loading' : ''}}"
          wx:for="{{activities}}"
          wx:key="id"
          bindtap="onActivityTap"
          data-id="{{item.id}}">
      <view class="activity-header">
        <view class="activity-icon">
          <image src="{{item.icon}}" mode="aspectFit"></image>
          <view class="loading-indicator" wx:if="{{loadingActivityId === item.id}}"></view>
        </view>
        <view class="activity-title">{{item.name}}</view>
      </view>

      <view class="activity-desc">{{item.description}}</view>

      <view class="activity-items">
        <text class="item-label">可获得:</text>
        <view class="item-tags">
          <text class="item-tag" wx:for="{{item.items}}" wx:key="*this" wx:for-item="itemName">{{itemName}}</text>
        </view>
      </view>

      <view class="go-btn">{{loadingActivityId === item.id ? '加载中...' : '开始模拟'}}</view>
    </view>
  </view>

  <view class="footer">
    <!-- 免责声明卡片 -->
    <view class="disclaimer-card">
      <view class="disclaimer">* 抽奖概率基于游戏公示数据，仅供娱乐，非官方工具</view>
    </view>
    <view class="back-button" bindtap="goBack">
      返回工具箱
    </view>

    <!-- 广告组件 -->
    <!--<view class="ad-container">
      <ad-button text="支持我们" />
    </view>-->
  </view>

  <!-- 添加自定义底部导航栏 -->
  <custom-tabbar selected="2"></custom-tabbar>

  <!-- VIP对话框组件 -->
  <vip-dialog
    show="{{showVipDialog}}"
    pageKey="lottery-list"
    simpleMode="{{true}}"
    isVip="{{isVip}}"
    vipRemainingDays="{{vipRemainingDays}}"
    bind:close="onVipDialogClose"
    bind:buy="onBuyVip">
  </vip-dialog>
</view>