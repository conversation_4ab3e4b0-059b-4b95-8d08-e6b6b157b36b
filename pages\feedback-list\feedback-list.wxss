.container {
  padding: 20rpx;
  min-height: 100vh;
  position: relative;
  box-sizing: border-box;
  background: transparent;
  padding-bottom: 240rpx; /* 增加底部内边距 */
}

/* 背景图片样式 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.8;
  object-fit: cover;
}

.feedback-list {
  width: 100%;
  position: relative;
  z-index: 1;
}

.feedback-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.feedback-type {
  font-size: 28rpx;
  color: #666;
}

.feedback-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.feedback-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.feedback-status.processing {
  background: #e6f7ff;
  color: #1890ff;
}

.feedback-status.completed {
  background: #f6ffed;
  color: #52c41a;
}

.feedback-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin: 16rpx 0;
}

.feedback-footer {
  font-size: 24rpx;
  color: #999;
}

.feedback-reply {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.reply-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.reply-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.loading-more, .no-more {
  text-align: center;
  padding: 20rpx;
  color: #666;
  font-size: 26rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-state text {
  font-size: 28rpx;
  color: #999;
}

.pagination {
  position: fixed;
  bottom: 20rpx; /* 添加底部间距 */
  left: 20rpx; /* 与容器左边距对齐 */
  width: calc(100% - 40rpx); /* 减去左右边距 */
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx; /* 改用与卡片一致的圆角 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.page-info {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.separator {
  margin: 0 20rpx;
  color: #ddd;
}

.page-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.page-btn {
  padding: 10rpx 30rpx;
  font-size: 26rpx;
  color: #333;
  background: #f5f5f5;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.page-btn.disabled {
  color: #999;
  background: #f0f0f0;
  pointer-events: none;
}

.page-numbers {
  display: flex;
  gap: 10rpx;
}

.page-number {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 26rpx;
  color: #333;
  background: #f5f5f5;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.page-number.active {
  color: #fff;
  background: #007AFF;
} 