# QQ飞车赛车推进计算与绘图功能

## 功能概述

赛车推进计算与绘图功能是QQ飞车图鉴小程序中的核心工具，用户可以通过搜索选择赛车，系统自动从cars_car表获取原装赛车的推进档位和引擎档位数据，计算满改装赛车的推进40档位数据，并生成动力-速度曲线图和速度-时间曲线图。

## 🎨 设计理念

### 整体设计思维
- **数据可视化为核心**：将复杂的赛车数据通过视觉层次清晰呈现
- **渐进式交互**：从搜索→选择→计算→可视化的流畅体验
- **专业工具感**：体现计算工具的专业性和准确性
- **移动端优先**：垂直布局适应手机屏幕，拇指友好的操作区域

### 页面布局设计
```
┌─────────────────────────────┐
│  🏁 赛车推进计算与绘图    [VIP] │  ← 功能导航
│  根据原装数据计算满改装推进40   │
├─────────────────────────────┤
│ 🔍 [搜索赛车名称...    ] [搜索] │  ← 智能搜索
│ 📋 搜索历史/热门推荐          │
├─────────────────────────────┤
│ 🏎️ 收割者 [A/M3/L3]         │  ← 赛车信息卡片
│ 推进档位 | 引擎档位 | 燃料强度  │
├─────────────────────────────┤
│ [🧮 计算推进40]              │  ← 操作按钮
│ [📊 动力曲线] [📈 速度曲线]    │
├─────────────────────────────┤
│ 📊 推进40计算结果            │  ← 结果展示
│ 数据表格 + 图表可视化         │
└─────────────────────────────┘
```

### 交互设计原则
- **减少认知负担**：渐进式披露复杂信息
- **提升操作效率**：智能默认值和快捷操作
- **错误预防与恢复**：实时验证和友好的错误提示
- **流畅的动画反馈**：微交互增强用户体验

## 主要特性

### 1. 智能搜索选择功能
- 支持按赛车名称实时搜索
- 搜索历史和热门推荐
- 自动从cars_car表获取原装数据
- 显示赛车等级和详细档位信息
- 防抖处理优化性能

### 2. 推进40计算功能
- 根据推进档位计算表自动计算满改装推进40数据
- 支持所有赛车等级（C/M1, B/M2/L2/R, T1, A/M3/L3, T2, T2皮肤）
- 正确处理带+号的无上限档位
- 特殊处理T2皮肤等级（以"T2("开头的赛车）
- 实时计算进度显示

### 3. 数据可视化功能
- 动力-速度曲线图（基础/大喷/CWW三种状态）
- 速度-时间曲线图（加速性能分析）
- 支持两车对比功能
- 基于ECharts图表库实现
- 交互式图表（缩放、拖拽、点击查看详情）

### 4. VIP系统集成
- 免费用户每日3次查询限制
- VIP用户无限制使用
- 支持观看广告获得额外次数
- 权限状态实时显示

### 5. 响应式设计
- 适配不同屏幕尺寸（小屏/中屏/大屏）
- 美观的卡片式界面设计
- 流畅的动画效果和交互反馈
- 毛玻璃效果和多层阴影

## 🎯 视觉设计规范

### 色彩系统
- **主色调**：蓝色渐变 `#4a90e2 → #3670b2`（专业、可靠）
- **辅助色**：紫色渐变 `#9c27b0 → #7b1fa2`（图表、高级功能）
- **状态色**：
  - 成功：`#4CAF50`（计算完成）
  - 警告：`#FF9800`（权限限制）
  - 错误：`#F44336`（计算失败）

### 字体层次
- **标题**：32rpx，粗体，深色
- **副标题**：26rpx，常规，中等色
- **正文**：24rpx，常规，深色
- **说明**：20rpx，常规，浅色

### 卡片设计
- **圆角**：16rpx（现代感）
- **阴影**：多层阴影营造层次感
- **透明度**：`rgba(255,255,255,0.98)`（毛玻璃效果）
- **边框**：细微的蓝色边框增强品质感

### 动画与反馈
- **微交互**：按钮点击缩放、卡片展开动画
- **状态反馈**：加载动画、成功/错误提示
- **数据动画**：数字递增、图表渐进绘制

## 📱 响应式适配

### 屏幕尺寸适配
```css
/* 小屏幕 (<320px) */
--content-width: 98%;
--font-size-title: 28rpx;

/* 中等屏幕 (320px-414px) */
--content-width: 96%;
--font-size-title: 32rpx;

/* 大屏幕 (>414px) */
--content-width: 92%;
--font-size-title: 36rpx;
```

## 技术实现

### 页面结构
```
pages/car-calculation/
├── car-calculation.js    # 页面逻辑
├── car-calculation.json  # 页面配置
├── car-calculation.wxml  # 页面模板
├── car-calculation.wxss  # 页面样式
└── README.md            # 功能说明
```

### 核心算法

#### 推进40计算算法
```javascript
function calculatePropulsion40(carLevel, originalPropulsionLevels) {
  // 处理T2皮肤等级（以"T2("开头的赛车使用T2皮肤数据）
  if (carLevel.startsWith('T2(')) {
    lookupLevel = 'T2皮肤';
  } else {
    lookupLevel = carLevel;
  }

  // 获取对应等级的计算表数据
  const tableData = getPropulsionLevelTable(lookupLevel);

  const propulsion40Levels = [];
  for (let i = 0; i < originalPropulsionLevels.length; i++) {
    const levelNum = i + 1;
    const diffValue = tableData[`level_${levelNum}_diff`];
    const maxValue = tableData[`level_${levelNum}_max`];

    // 计算推进40档位
    const calculated40 = originalPropulsionLevels[i] + diffValue;

    // 如果上限为null（带+号的无上限），直接使用计算值
    const final40 = maxValue === null ? calculated40 : Math.min(calculated40, maxValue);

    propulsion40Levels.push(final40);
  }

  return propulsion40Levels;
}
```

#### 动力计算算法
```javascript
function calculatePowerValues(propulsionLevels, engineLevels, fuelIntensity = 6290) {
  // 速度锚点
  const speedAnchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5];

  // 基础动力计算
  const basePowers = calculateBasePowers(propulsionLevels, engineLevels, speedAnchors);

  // 大喷动力计算
  const bigJetPower = fuelIntensity / 1.2;
  const fuelPowers = basePowers.map(power => power + bigJetPower);

  // cww动力计算
  const smallJetPower = fuelIntensity / 1.2;
  const boostPowers = basePowers.map(power => power + bigJetPower + smallJetPower);

  return {
    speedAnchors,
    basePowers,
    fuelPowers,
    boostPowers
  };
}
```

### API接口设计

#### 赛车搜索接口
```
GET /api/cars/search/?q=收割者
Response: {
  "success": true,
  "data": [
    {
      "car_id": "car_001",
      "name": "收割者",
      "level": "A/M3/L3",
      "engine_levels": [40, 40, 40, 40, 40, 40],
      "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
      "fuel_intensity": 6290
    }
  ]
}
```

#### 推进计算接口
```
POST /api/cars/calculate-propulsion/
Request: {
  "car_id": "car_001"
}
Response: {
  "success": true,
  "data": {
    "car_info": {...},
    "original_data": {...},
    "propulsion_40_levels": [5036, 5184, 5800, 6416, 6900, 7115, 7282],
    "power_data": {...}
  }
}
```

#### 图表生成接口
```
POST /api/cars/generate-chart/
Request: {
  "chart_type": "power_speed",
  "cars": [...]
}
Response: {
  "success": true,
  "data": {
    "chart_config": {...}
  }
}
```

## 使用方法

1. 在工具箱页面点击"赛车推进计算与绘图"
2. 在搜索框输入赛车名称并点击搜索
3. 从搜索结果中选择目标赛车
4. 查看赛车的原装数据（推进档位、引擎档位、燃料强度）
5. 点击"计算推进40"按钮进行计算
6. 查看推进40计算结果
7. 点击"生成动力曲线"或"生成速度曲线"生成图表

## 数据来源

### 推进档位计算表
基于设计文档中的推进档位计算表数据：
- C/M1级：推进1-7档的差值和上限
- B/M2/L2/R级：推进1-7档的差值和上限
- T1级：推进1-7档的差值和上限
- A/M3/L3级：推进1-7档的差值和上限
- T2级：推进1-7档的差值和上限
- T2皮肤级：推进1-7档的差值和上限

### 赛车原装数据
从现有cars_car表获取：
- 赛车名称和等级
- 引擎1-6档数据
- 推进1-7档原装数据
- 燃料强度数据

## 特殊处理

### T2皮肤赛车
- 以"T2("开头的赛车等级使用T2皮肤计算数据
- 例如："T2(雷诺传奇皮肤)"使用T2皮肤等级的计算表

### 无上限档位
- 推进档位计算表中null值表示无上限（带+号的数据）
- 计算时直接使用原装数据+差值，不受上限限制

### 特殊赛车
- 雷诺传奇皮肤和飞碟等特殊赛车可能不适用推进档位计算表
- 这些赛车需要特殊标注和处理

## 注意事项

1. **数据准确性**：计算结果仅供参考，实际游戏数据可能有差异
2. **免责声明**：页面中包含免责声明，建议用户以游戏内实际数据为准
3. **性能考虑**：图表渲染可能消耗较多资源，需要优化
4. **用户体验**：输入验证和错误提示要友好，加载状态要明确显示

## 后续优化方向

1. 集成ECharts图表库，实现更丰富的图表功能
2. 添加两车对比功能
3. 支持更多赛车类型和特殊赛车
4. 添加历史记录和收藏功能
5. 支持导出图表为图片
6. 添加更多图表类型（如加速度曲线等）
7. 优化图表渲染性能
8. 支持批量计算和对比
