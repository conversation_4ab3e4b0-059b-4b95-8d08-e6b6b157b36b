# 抽奖记录前端对接指南

本文档提供了抽奖记录功能的前端对接指南，特别关注上传抽奖记录接口的正确使用方式。

## 上传抽奖记录接口

### 接口说明

上传抽奖记录接口用于将用户的抽奖结果上传到服务器，计算稀有度得分并更新排行榜。

### 接口地址

```
POST /api/lottery/records
```

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| activityType | string | 是 | 活动类型，可选值：`treasure-hunting`(赛车夺宝)、`supertreasure`(至尊夺宝)、`luckytree`(幸运摇钱树) |
| openid | string | 是 | 用户的微信openid |
| nickname | string | 否 | 用户昵称，可选，用于更新用户信息 |
| statistics | array | 是 | 抽奖物品统计数组，每个元素包含item对象和count数量 |
| totalDraws | integer | 是 | 总抽奖次数 |
| hasLegendaryItems | boolean | 否 | 是否有传说级物品，默认false |
| hasTotalGoldItems | boolean | 否 | 是否有金色物品，默认false |

### 重要提示

1. **字段名称必须正确**：
   - 使用 `statistics` 而非 `items` 作为物品数组字段名
   - 使用 `totalDraws` 而非 `drawCount` 作为总抽奖次数字段名

2. **物品数据结构**：
   - 每个物品必须包含 `item` 对象和 `count` 数量
   - `item` 对象必须包含 `id`, `name`, `background`, `probability` 字段

3. **稀有度计算**：
   - 只有 `background` 为 `legendary` 或 `gold` 的物品才会计入稀有度得分
   - 物品得分 = 1000 / 物品抽取概率
   - 总物品得分 = 所有传说级和金色物品的 (物品得分 × 获得数量) 之和
   - 稀有率系数 = 1 / 总抽奖次数
   - 稀有度得分 = 总物品得分 × 稀有率系数

### 请求示例

```javascript
wx.request({
  url: 'https://pikario.site/api/lottery/records',
  method: 'POST',
  data: {
    activityType: 'treasure-hunting',
    openid: wx.getStorageSync('openid'),
    nickname: wx.getStorageSync('userInfo').nickName,
    statistics: [  // 注意：此字段名为statistics而非items
      {
        item: {
          id: 1,
          name: '金色方程式',
          background: 'legendary',
          probability: 0.00002
        },
        count: 1
      },
      {
        item: {
          id: 2,
          name: '金色烈焰',
          background: 'gold',
          probability: 0.0003
        },
        count: 2
      }
    ],
    totalDraws: 500,  // 注意：此字段名为totalDraws而非drawCount
    hasLegendaryItems: true,
    hasTotalGoldItems: true
  },
  success(res) {
    console.log('上传成功:', res.data);
    // 处理成功响应
    if (res.data.code === 0) {
      const recordData = res.data.data;
      console.log('记录ID:', recordData.recordId);
      console.log('稀有度得分:', recordData.rarityScore);
      console.log('稀有度排名:', recordData.rank.rarityRank);
      console.log('抽奖次数排名:', recordData.rank.drawsRank);
    } else {
      console.error('上传失败:', res.data.message);
    }
  },
  fail(err) {
    console.error('请求失败:', err);
  }
});
```

### 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "activityType": "treasure-hunting",
    "recordId": "123",
    "rarityScore": 1000.5,
    "timestamp": "2023-08-01T12:00:00Z",
    "rank": {
      "rarityRank": 5,
      "drawsRank": 10
    }
  }
}
```

## 常见问题

### 1. 上传后物品记录不显示

如果上传后在record表中能查到记录，但在item表中没有物品记录，可能是以下原因：

- 请求中使用了错误的字段名（使用了`items`而非`statistics`）
- 物品数据结构不正确
- 物品数组为空

### 2. 稀有度得分为0

如果上传后稀有度得分为0，可能是以下原因：

- 没有传说级或金色物品（只有这两种物品才计入得分）
- 物品的`background`字段值不正确（必须是`legendary`或`gold`）
- 物品的`probability`值过大，导致得分过低
- `totalDraws`值过大，导致稀有率系数过低

### 3. 排名不显示

如果上传成功但排名不显示，可能是以下原因：

- 系统中没有其他用户记录
- 稀有度得分或抽奖次数为0

## 调试建议

1. 检查请求数据结构，确保字段名称正确
2. 打印完整的请求数据和响应数据
3. 确认物品数据中包含正确的背景色和概率值
4. 使用较小的`totalDraws`值进行测试，以获得更高的稀有度得分

## 相关接口

- [获取排行榜数据](/api/lottery/rankings)
- [获取用户抽奖记录详情](/api/lottery/record-detail)
- [删除抽奖记录](/api/lottery/records) (DELETE方法)
