// 抽奖模拟器页面逻辑
import PageWithVIP from '../../../utils/page-with-vip';

const app = getApp();
const api = require('../../../utils/api');

/**
 * 概率公示数据
 */
const probsData = {
  treasurehunting: {
    title: '赛车夺宝概率公示',
    ruleText: '赛车夺宝抽奖概率公示信息',
    items: [
      { name: '至曜·玄武(永久)', probability: '0.002%' },
      { name: '至曜·玄武(15天)', probability: '0.004%' },
      { name: 'S级赛车(永久)', probability: '0.016%' },
      { name: 'S级赛车(15天)', probability: '0.04%' },
      { name: 'T1机甲(永久)', probability: '0.09%' },
      { name: 'T1机甲(30天)', probability: '1.53%' },
      { name: 'A级赛车(永久)', probability: '0.18%' },
      { name: 'A级赛车(30天)', probability: '4.86%' },
      { name: '其他道具(7天)', probability: '93.28%' }
    ]
  }
};

/**
 * 自选赛车和机甲选项
 */
const selectionOptions = {
  // S级赛车选项列表（7选2）
  sCars: [
    { id: 's1', name: '天机线', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/天机线.png' },
    { id: 's2', name: '猛兽', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/猛兽.png' },
    { id: 's3', name: '海神之刺', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/海神之刺.png' },
    { id: 's4', name: '梦幻旋律', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/梦幻旋律.png' },
    { id: 's5', name: '血月猛禽', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/血月猛禽.png' },
    { id: 's6', name: '代达罗斯', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/代达罗斯.png' },
    { id: 's7', name: '终极麦凯伦', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/终极麦凯伦.png' }
  ],

  // T1机甲选项列表（5选2）
  t1Mechs: [
    { id: 't1', name: '双子守护者', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/双子守护者.png' },
    { id: 't2', name: '萌猪骑士', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/萌猪骑士.png' },
    { id: 't3', name: '战地先锋', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/战地先锋.png' },
    { id: 't4', name: '圣枪', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/圣枪.png' }
  ],

  // A级赛车选项列表（9选6）
  aCars: [
    { id: 'a1', name: '星河之恋', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/星河之恋.png' },
    { id: 'a2', name: '七彩仙葫', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/七彩仙葫.png' },
    { id: 'a3', name: '夜染天国', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/夜染天国.png' },
    { id: 'a4', name: '魔法之翼', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/魔法之翼.png' },
    { id: 'a5', name: 'SPD·星梦', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/SPD·星梦.png' },
    { id: 'a6', name: '冥霄', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/冥霄.png' },
    { id: 'a7', name: '神圣天使兽', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/神圣天使兽.png' },
    { id: 'a8', name: '地狱神影', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/地狱神影.png' },
    { id: 'a9', name: '绝地王灵', image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/绝地王灵.png' }
  ]
};

// 定义赛车夺宝抽奖活动的概率和奖品
const lotteryConfig = {
  // 赛车夺宝
  treasurehunting: {
    name: '模拟抽奖-赛车夺宝',
    items: [
      { id: 1, name: '至曜·玄武(永久)', probability: 0.00002, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/至曜·玄武.png', background: 'legendary', type: 'fixed', fragmentValue: 1500 },
      { id: 2, name: '至曜·玄武(15天)', probability: 0.00004, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/至曜·玄武.png', background: 'purple', type: 'fixed', fragmentValue: 20 },
      { id: 3, name: '自选S级赛车1(永久)', probability: 0.00008, image: '', background: 'gold', type: 'select', optionType: 'sCars', index: 0, fragmentValue: 1500 },
      { id: 4, name: '自选S级赛车2(永久)', probability: 0.00008, image: '', background: 'gold', type: 'select', optionType: 'sCars', index: 1, fragmentValue: 1500 },
      { id: 5, name: '自选S级赛车1(15天)', probability: 0.0002, image: '', background: 'purple', type: 'select', optionType: 'sCars', index: 0, fragmentValue: 20 },
      { id: 6, name: '自选S级赛车2(15天)', probability: 0.0002, image: '', background: 'purple', type: 'select', optionType: 'sCars', index: 1, fragmentValue: 20 },
      { id: 7, name: '自选T1机甲1(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 't1Mechs', index: 0, fragmentValue: 180 },
      { id: 8, name: '自选T1机甲2(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 't1Mechs', index: 1, fragmentValue: 180 },
      { id: 9, name: 'HelloKitty雷诺(永久)', probability: 0.0003, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/HelloKitty雷诺.png', background: 'gold', type: 'fixed', fragmentValue: 50 },
      { id: 10, name: '自选T1机甲1(30天)', probability: 0.0051, image: '', background: 'purple', type: 'select', optionType: 't1Mechs', index: 0, fragmentValue: 5 },
      { id: 11, name: '自选T1机甲2(30天)', probability: 0.0051, image: '', background: 'purple', type: 'select', optionType: 't1Mechs', index: 1, fragmentValue: 5 },
      { id: 12, name: 'HelloKitty雷诺(30天)', probability: 0.0051, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/HelloKitty雷诺.png', background: 'purple', type: 'fixed', fragmentValue: 5 },
      { id: 13, name: '自选A级赛车1(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 'aCars', index: 0, fragmentValue: 30 },
      { id: 14, name: '自选A级赛车2(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 'aCars', index: 1, fragmentValue: 30 },
      { id: 15, name: '自选A级赛车3(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 'aCars', index: 2, fragmentValue: 30 },
      { id: 16, name: '自选A级赛车4(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 'aCars', index: 3, fragmentValue: 30 },
      { id: 17, name: '自选A级赛车5(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 'aCars', index: 4, fragmentValue: 30 },
      { id: 18, name: '自选A级赛车6(永久)', probability: 0.0003, image: '', background: 'gold', type: 'select', optionType: 'aCars', index: 5, fragmentValue: 30 },
      { id: 19, name: '自选A级赛车1(30天)', probability: 0.0081, image: '', background: 'purple', type: 'select', optionType: 'aCars', index: 0, fragmentValue: 3 },
      { id: 20, name: '自选A级赛车2(30天)', probability: 0.0081, image: '', background: 'purple', type: 'select', optionType: 'aCars', index: 1, fragmentValue: 3 },
      { id: 21, name: '自选A级赛车3(30天)', probability: 0.0081, image: '', background: 'purple', type: 'select', optionType: 'aCars', index: 2, fragmentValue: 3 },
      { id: 22, name: '自选A级赛车4(30天)', probability: 0.0081, image: '', background: 'purple', type: 'select', optionType: 'aCars', index: 3, fragmentValue: 3 },
      { id: 23, name: '自选A级赛车5(30天)', probability: 0.0081, image: '', background: 'purple', type: 'select', optionType: 'aCars', index: 4, fragmentValue: 3 },
      { id: 24, name: '自选A级赛车6(30天)', probability: 0.0081, image: '', background: 'purple', type: 'select', optionType: 'aCars', index: 5, fragmentValue: 3 },
      { id: 25, name: '海神之戟(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/海神之戟.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 26, name: '幻粼猛禽(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/幻粼猛禽.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 27, name: '时之沧影(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/时之沧影.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 28, name: '赤焰天启(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/赤焰天启.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 29, name: '流风Arai(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/流风Arai.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 30, name: '冥霄(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/冥霄.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 31, name: '缤纷神驹(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/缤纷神驹.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 32, name: '卡帕(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/卡帕.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 33, name: '米迦勒(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/米迦勒.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 34, name: '梦魇(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/梦魇.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 35, name: '摄魂(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/摄魂.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 36, name: '剔魄(7天)', probability: 0.03773, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/剔魄.png', background: 'normal', type: 'fixed', fragmentValue: 1 },
      { id: 37, name: '效率宝珠LV1', probability: 0.08, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/效率宝珠LV1.png', background: 'normal', type: 'fixed', fragmentValue: 0 },
      { id: 38, name: '点火装置+1', probability: 0.08, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/点火装置+1.png', background: 'normal', type: 'fixed', fragmentValue: 0 },
      { id: 39, name: '进气系统+1', probability: 0.08, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/进气系统+1.png', background: 'normal', type: 'fixed', fragmentValue: 0 },
      { id: 40, name: '燃料系统+1', probability: 0.08, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/燃料系统+1.png', background: 'normal', type: 'fixed', fragmentValue: 0 },
      { id: 41, name: '引擎装置+1', probability: 0.08, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/引擎装置+1.png', background: 'normal', type: 'fixed', fragmentValue: 0 },
      { id: 42, name: '防护装置+1', probability: 0.08, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/treasure-hunting/防护装置+1.png', background: 'normal', type: 'fixed', fragmentValue: 0 },
    ],
    cost: 300, // 单次抽奖消耗点券，3元=300点券
    multiCost: 3000 // 10连抽消耗点券
  }
};

// 页面标识，用于区分不同抽奖页面的次数
const PAGE_KEY = 'treasure-hunting';

PageWithVIP({
  data: {
    activityId: '',
    activity: null,
    backgroundImage: '/images/bg.jpg',  // 确保使用绝对路径
    results: [], // 抽奖结果
    statistics: {}, // 抽奖统计
    totalDraws: 0, // 总抽奖次数
    totalFragments: 0, // 分解碎片总数
    running: false, // 是否正在抽奖动画中
    showResult: false, // 是否显示结果
    isMultiDraw: false, // 是否是十连抽
    showSelectPanel: false, // 是否显示自选面板
    selectionOptions: selectionOptions, // 自选选项
    selectedOptions: {
      sCars: [], // 已选择的S级赛车，存储索引
      t1Mechs: [], // 已选择的T1机甲，存储索引
      aCars: [] // 已选择的A级赛车，存储索引
    },
    selectionCompleted: false, // 是否已完成自选设置
    currentStep: 1, // 当前自选步骤，1:S级赛车，2:T1机甲，3:A级赛车，4:预览
    showProbabilityPanel: false, // 是否显示概率公示面板
    selectedSCarsItems: [],
    selectedT1MechsItems: [],
    selectedACarsItems: [],
    originalItems: null,
    hasTotalGoldItems: false, // 初始化金色物品标志
    hasTotalPurpleItems: false, // 初始化紫色物品标志

    // 添加新的属性
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 导航状态控制
    isNavigating: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('treasure-hunting onLoad', options);
    try {
      const activityId = options.id || 'treasurehunting';
      const activity = lotteryConfig[activityId];

      // 确保活动存在
      if (!activity) {
        console.error('活动不存在:', activityId);
        wx.showToast({
          title: '活动不存在',
          icon: 'none'
        });
        return;
      }

      console.log('加载活动:', activity.name);

      // 加载背景图片，确保使用正确路径
      const bgImage = '/images/bg.jpg';

      // 初始化空的选择项
      const emptySelectedOptions = {
        sCars: [],
        t1Mechs: [],
        aCars: []
      };

      // 尝试从缓存加载已保存的选择
      try {
        const savedSelectionStr = wx.getStorageSync('treasureHuntingSelection');
        if (savedSelectionStr) {
          const savedSelection = JSON.parse(savedSelectionStr);
          if (savedSelection.completed) {
            // 如果有完整的已保存选择，则使用它
            this.setData({
              selectedOptions: savedSelection.selectedOptions,
              selectedSCarsItems: savedSelection.selectedSCarsItems || [],
              selectedT1MechsItems: savedSelection.selectedT1MechsItems || [],
              selectedACarsItems: savedSelection.selectedACarsItems || [],
              selectionCompleted: true
            });

            // 更新活动数据中的自选项
            this.updateActivityItemsWithSelection(this.data.selectedOptions);

            console.log('已恢复保存的自选设置');
          }
        }
      } catch (e) {
        console.error('加载保存的选择失败:', e);
      }

      // 设置基本数据（只设置页面渲染必需的数据）
      this.setData({
        activityId: activityId,
        activity: activity,
        backgroundImage: bgImage,
        showSelectPanel: false
      });

      // 初始化页面数据（基础数据）
      this.initPageData();

      // 延迟执行复杂初始化，避免阻塞页面渲染
      setTimeout(() => {
        // 设置复杂数据
        this.setData({
          probsData: probsData.treasurehunting,
          selectionOptions: JSON.parse(JSON.stringify(selectionOptions))
        });

        // 初始化VIP和免费次数
        this.initVipAndFreeCount();

        // 尝试从缓存加载抽奖状态
        const stateLoaded = this.loadDrawState();

        // 如果没有加载到保存的状态，则初始化新的统计数据
        if (!stateLoaded) {
          this.initStatistics();
        } else {
          // 已加载缓存状态，检查是否有稀有物品
          this.checkRareItems();
        }
      }, 100);

    } catch (error) {
      console.error('加载页面出错:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 初始化页面数据
    this.setData({
      backgroundImage: '/images/bg.jpg',
      pageKey: PAGE_KEY,
      isVip: false,
      freeCount: 0,
      vipRemainingDays: 0,
      showVipDialog: false,
    });
  },

  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount() {
    try {
      // 获取VIP状态
      const isVip = api.isVip();
      // 获取免费次数
      const freeCount = api.getFreeCount(PAGE_KEY);
      // 获取VIP剩余天数
      const vipRemainingDays = api.getVipRemainingDays();

      this.setData({
        isVip: isVip,
        freeCount: freeCount,
        vipRemainingDays: vipRemainingDays
      });

      console.log('更新VIP和免费次数 - VIP:', isVip, '免费次数:', freeCount, '剩余天数:', vipRemainingDays);
    } catch (error) {
      console.error('更新VIP和免费次数失败:', error);
    }
  },

  /**
   * 更新VIP和免费次数
   */
  updateVipAndFreeCount() {
    try {
      // 获取VIP状态
      const isVip = api.isVip();
      // 获取免费次数
      const freeCount = api.getFreeCount(PAGE_KEY);
      // 获取VIP剩余天数
      const vipRemainingDays = api.getVipRemainingDays();

      this.setData({
        isVip: isVip,
        freeCount: freeCount,
        vipRemainingDays: vipRemainingDays
      });

      console.log('更新VIP和免费次数 - VIP:', isVip, '免费次数:', freeCount, '剩余天数:', vipRemainingDays);
    } catch (error) {
      console.error('更新VIP和免费次数失败:', error);
    }
  },

  /**
   * 加载自选设置
   * @param {boolean} forceLoad - 是否强制从缓存加载
   */
  loadSelectionFromStorage: function(forceLoad = false) {

    try {
      // 初始化空选择
      let selectedOptions = {
        sCars: [], // 默认不选择任何S级赛车
        t1Mechs: [], // 默认不选择任何T1机甲
        aCars: [] // 默认不选择任何A级赛车
      };
      let selectionCompleted = false;


      // 仅当forceLoad为true时才从缓存加载
      if (forceLoad) {
        try {
          const savedSelection = wx.getStorageSync('treasureHuntingSelection');

          if (savedSelection) {

            try {
              const parsedSelection = JSON.parse(savedSelection);

              if (parsedSelection && parsedSelection.selectedOptions) {
                selectedOptions = parsedSelection.selectedOptions;

                // 检查自选设置是否完整
                const sComplete = selectedOptions.sCars && selectedOptions.sCars.length === 2;
                const tComplete = selectedOptions.t1Mechs && selectedOptions.t1Mechs.length === 2;
                const aComplete = selectedOptions.aCars && selectedOptions.aCars.length === 6;

                // 只有当所有选择都完成时，才设置selectionCompleted为true
                selectionCompleted = sComplete && tComplete && aComplete;

                if (selectionCompleted) {
                  // 如果选择完整，更新活动数据中的自选项
                  this.updateActivityItemsWithSelection(selectedOptions);
                } else {
                  // 如果选择不完整，显示自选面板
                  this.setData({
                    showSelectPanel: true
                  });
                }
              } else {
                console.warn('【警告】缓存数据格式不正确');
                this.setData({
                  showSelectPanel: true
                });
              }
            } catch (parseError) {
              console.error('【错误】解析缓存数据失败:', parseError);
              this.setData({
                showSelectPanel: true
              });
            }
          } else {
            this.setData({
              showSelectPanel: true
            });
          }
        } catch (storageError) {
          console.error('【错误】读取本地存储失败:', storageError);
          this.setData({
            showSelectPanel: true
          });
        }
      } else {
      }

      // 更新自选项目的名称和图片
      const activity = lotteryConfig.treasurehunting;
      this.updateSelectedItems(activity.items, selectedOptions);
      this.setData({
        selectedOptions: selectedOptions,
        selectionCompleted: selectionCompleted
      });

      return selectedOptions;
    } catch (error) {
      console.error('【错误】loadSelectionFromStorage出错:', error);

      // 确保即使出错也返回空选择
      const emptySelection = {
        sCars: [],
        t1Mechs: [],
        aCars: []
      };

      this.setData({
        selectedOptions: emptySelection,
        selectionCompleted: false,
        showSelectPanel: true
      });
      return emptySelection;
    }
  },

  /**
   * 根据自选设置更新活动奖品信息
   * @param {Object} selectedOptions - 选择的选项
   */
  updateActivityItemsWithSelection: function(selectedOptions) {

    try {
      if (!this.data.activity || !this.data.activity.items) {
        console.error('【错误】活动数据不存在，无法更新自选项');
        return;
      }

      // 深拷贝活动数据以防止直接修改引用
      const updatedActivityItems = JSON.parse(JSON.stringify(this.data.activity.items));

      // 分配已选项到对应的奖励中
      for (let i = 0; i < updatedActivityItems.length; i++) {
        const item = updatedActivityItems[i];

        // 跳过非自选类型的项目
        if (item.type !== 'select' || !item.optionType || item.index === undefined) {
          continue;
        }

        // 根据自选类型和索引分配对应的选择结果
        if (item.optionType === 'sCars') {
          const selectedIndex = Number(item.index);
          if (selectedIndex >= 0 && selectedIndex < selectedOptions.sCars.length) {
            const carIndex = selectedOptions.sCars[selectedIndex];
            const selectedCar = this.data.selectionOptions.sCars[carIndex];

            if (selectedCar) {
              item.displayName = selectedCar.name + (item.name.includes('(') ? item.name.substring(item.name.indexOf('(')) : '');
              item.image = selectedCar.image;
              item.selectedItemIndex = carIndex;
            }
          }
        } else if (item.optionType === 't1Mechs') {
          const selectedIndex = Number(item.index);
          if (selectedIndex >= 0 && selectedIndex < selectedOptions.t1Mechs.length) {
            const mechIndex = selectedOptions.t1Mechs[selectedIndex];
            const selectedMech = this.data.selectionOptions.t1Mechs[mechIndex];

            if (selectedMech) {
              item.displayName = selectedMech.name + (item.name.includes('(') ? item.name.substring(item.name.indexOf('(')) : '');
              item.image = selectedMech.image;
              item.selectedItemIndex = mechIndex;
            }
          }
        } else if (item.optionType === 'aCars') {
          const selectedIndex = Number(item.index);
          if (selectedIndex >= 0 && selectedIndex < selectedOptions.aCars.length) {
            const carIndex = selectedOptions.aCars[selectedIndex];
            const selectedCar = this.data.selectionOptions.aCars[carIndex];

            if (selectedCar) {
              item.displayName = selectedCar.name + (item.name.includes('(') ? item.name.substring(item.name.indexOf('(')) : '');
              item.image = selectedCar.image;
              item.selectedItemIndex = carIndex;
            }
          }
        }
      }

      // 使用完全新的对象更新活动数据，确保视图刷新
      const updatedActivity = JSON.parse(JSON.stringify(this.data.activity));
      updatedActivity.items = updatedActivityItems;

      this.setData({
        activity: updatedActivity
      });
    } catch (error) {
      console.error('【错误】updateActivityItemsWithSelection出错:', error);
    }
  },

  /**
   * 保存自选设置到本地存储
   */
  saveSelectionToStorage: function() {
    try {
      // 确保所有索引都是数字类型
      const selectedOptions = {
        sCars: this.data.selectedOptions.sCars.map(Number),
        t1Mechs: this.data.selectedOptions.t1Mechs.map(Number),
        aCars: this.data.selectedOptions.aCars.map(Number)
      };

      const selectionData = {
        selectedOptions: selectedOptions,
        completed: this.data.selectionCompleted
      };
      wx.setStorageSync('treasureHuntingSelection', JSON.stringify(selectionData));
    } catch (e) {
      console.error('保存自选设置失败', e);
    }
  },

  /**
   * 更新自选项目的名称和图片
   */
  updateSelectedItems: function(activityItems, selectedOpts) {

    try {
      // 确保selectionOptions已初始化
      if (!this.data.selectionOptions || !this.data.selectionOptions.sCars || !this.data.selectionOptions.sCars.length) {
        this.setData({
          selectionOptions: selectionOptions // 使用全局定义的selectionOptions
        });
      }

      // 使用传入的selectedOpts，避免重新声明
      const localSelectedOpts = selectedOpts || {
        sCars: [],
        t1Mechs: [],
        aCars: []
      };

      // 确保所有索引都是数字类型
      if (localSelectedOpts.sCars && Array.isArray(localSelectedOpts.sCars)) {
        localSelectedOpts.sCars = localSelectedOpts.sCars.map(Number);
      } else {
        localSelectedOpts.sCars = [];
      }

      if (localSelectedOpts.t1Mechs && Array.isArray(localSelectedOpts.t1Mechs)) {
        localSelectedOpts.t1Mechs = localSelectedOpts.t1Mechs.map(Number);
        } else {
        localSelectedOpts.t1Mechs = [];
      }

      if (localSelectedOpts.aCars && Array.isArray(localSelectedOpts.aCars)) {
        localSelectedOpts.aCars = localSelectedOpts.aCars.map(Number);
          } else {
        localSelectedOpts.aCars = [];
      }

      console.log('【11.3.1】当前selectionOptions数据:',
        `S级赛车: ${this.data.selectionOptions.sCars ? this.data.selectionOptions.sCars.length : 0}个, ` +
        `T1机甲: ${this.data.selectionOptions.t1Mechs ? this.data.selectionOptions.t1Mechs.length : 0}个, ` +
        `A级赛车: ${this.data.selectionOptions.aCars ? this.data.selectionOptions.aCars.length : 0}个`
      );

      // 初始化数组
      const selectedSCarsItems = [];
      const selectedT1MechsItems = [];
      const selectedACarsItems = [];

      // 处理S级赛车
      if (localSelectedOpts.sCars && localSelectedOpts.sCars.length > 0 &&
          this.data.selectionOptions && this.data.selectionOptions.sCars) {
        localSelectedOpts.sCars.forEach(index => {
          const numIndex = Number(index);
          if (numIndex >= 0 && numIndex < this.data.selectionOptions.sCars.length) {
            const item = this.data.selectionOptions.sCars[numIndex];
            selectedSCarsItems.push(item);
          } else {
            console.warn(`【警告】S级赛车索引越界: ${index}, numIndex=${numIndex}`);
          }
        });
      } else {
      }

      // 处理T1机甲
      if (localSelectedOpts.t1Mechs && localSelectedOpts.t1Mechs.length > 0 &&
          this.data.selectionOptions && this.data.selectionOptions.t1Mechs) {
        localSelectedOpts.t1Mechs.forEach(index => {
          const numIndex = Number(index);
          if (numIndex >= 0 && numIndex < this.data.selectionOptions.t1Mechs.length) {
            const item = this.data.selectionOptions.t1Mechs[numIndex];
            selectedT1MechsItems.push(item);
          } else {
            console.warn(`【警告】T1机甲索引越界: ${index}, numIndex=${numIndex}`);
          }
        });
      } else {
        console.log('【11.5.2】没有选择T1机甲或数据不完整');
      }

      // 处理A级赛车
      if (localSelectedOpts.aCars && localSelectedOpts.aCars.length > 0 &&
          this.data.selectionOptions && this.data.selectionOptions.aCars) {
        console.log('【11.6】处理A级赛车选择');
        localSelectedOpts.aCars.forEach(index => {
          const numIndex = Number(index);
          if (numIndex >= 0 && numIndex < this.data.selectionOptions.aCars.length) {
            const item = this.data.selectionOptions.aCars[numIndex];
            console.log(`【11.6.1】添加A级赛车: 索引=${numIndex}, 名称=${item ? item.name : '未知'}`);
            selectedACarsItems.push(item);
          } else {
            console.warn(`【警告】A级赛车索引越界: ${index}, numIndex=${numIndex}`);
          }
        });
      } else {
        console.log('【11.6.2】没有选择A级赛车或数据不完整');
      }


      // 更新选中项目
      this.setData({
        selectedSCarsItems,
        selectedT1MechsItems,
        selectedACarsItems
      });

    } catch (error) {
      console.error('【错误】updateSelectedItems出错:', error);
    }

  },

  // 添加物品到统计数据
  addToStatistics: function(item, updateDrawCount = false) {
    const statistics = this.data.statistics;
    let found = false;

    // 克隆物品，确保不会修改原始物品
    const itemForStats = { ...item };

    // 处理自选物品显示名称
    if (item.type === 'select' && item.displayName) {
      itemForStats.specificName = item.displayName;
    }

    for (let i = 0; i < statistics.length; i++) {
      // 对于自选物品，需要同时比对ID和具体名称
      let isMatch = false;
      if (item.type === 'select' && item.displayName) {
        // 检查统计项是否已有specificName属性
        if (statistics[i].item.specificName === item.displayName && statistics[i].item.id === item.id) {
          isMatch = true;
        }
      } else {
        // 非自选物品仅比对ID
        isMatch = statistics[i].item.id === item.id;
      }

      if (isMatch) {
        statistics[i].count++;
        statistics[i].probabilityText = (item.probability * 100).toFixed(3) + '%';
        found = true;
        break;
      }
    }

    if (!found) {
      statistics.push({
        item: itemForStats,
        count: 1,
        percentage: '0.00',
        probabilityText: (item.probability * 100).toFixed(3) + '%'
      });
    }

    // 统计总抽奖次数（只有在updateDrawCount为true时更新）
    const totalDraws = updateDrawCount ? this.data.totalDraws + 1 : this.data.totalDraws;

    // 重新计算所有项目的百分比
    for (let i = 0; i < statistics.length; i++) {
      statistics[i].percentage = (statistics[i].count / totalDraws * 100).toFixed(2);
    }

    // 按照抽取次数排序
    statistics.sort((a, b) => b.count - a.count);

    // 更新统计数据
    const updateData = {
      statistics: statistics
    };

    // 只有在需要更新抽数时才更新totalDraws
    if (updateDrawCount) {
      updateData.totalDraws = totalDraws;
    }

    this.setData(updateData);
  },

  // 计算物品的碎片价值
  calculateFragmentValue: function(item) {
    console.log('计算碎片价值', item.name, item.fragmentValue);

    // 直接返回配置好的碎片值
    if (item && item.fragmentValue !== undefined) {
      return item.fragmentValue;
    }

    // 如果没有配置碎片值，则返回0
    return 0;
  },

  // 初始化统计数据，确保每个物品都在统计中显示
  initStatistics: function() {
    try {
      console.log('初始化统计数据');
      const statistics = [];

      // 确保activity和items存在
      if (!this.data.activity || !this.data.activity.items) {
        console.warn('活动数据不存在，无法初始化统计');
        this.setData({
          statistics: statistics,
          totalDraws: 0,
          totalFragments: 0,
          running: false,
          showResult: false,
          results: [],
          hasTotalGoldItems: false,
          hasTotalPurpleItems: false
        });
        return;
      }

      const items = this.data.activity.items;

      for (let i = 0; i < items.length; i++) {
        statistics.push({
          item: items[i],
          count: 0,
          percentage: '0.00',
          probabilityText: (items[i].probability * 100).toFixed(3) + '%'
        });
      }

      // 按照抽取概率排序（从高到低）
      statistics.sort((a, b) => b.item.probability - a.item.probability);

      console.log(`初始化了${statistics.length}条统计数据`);

      this.setData({
        statistics: statistics,
        totalDraws: 0,
        totalFragments: 0,
        running: false,
        showResult: false,
        results: [],
        hasTotalGoldItems: false,
        hasTotalPurpleItems: false
      });
    } catch (error) {
      console.error('初始化统计数据出错:', error);
      // 确保统计数据至少是一个空数组
      this.setData({
        statistics: [],
        totalDraws: 0,
        totalFragments: 0
      });
    }
  },

  // 单抽
  singleDraw: function() {
    if (this.data.running) return;

    // 检查是否已完成自选设置
    if (!this.data.selectionCompleted) {
      wx.showToast({
        title: '请先完成自选设置',
        icon: 'none'
      });
      this.setData({
        showSelectPanel: true
      });
      return;
    }

    console.log('开始单抽');
    this.setData({
      running: true,
      showResult: false,
      isMultiDraw: false
    });

    // 模拟抽奖过程
    setTimeout(() => {
      const result = this.drawItem(false);
      const fragmentValue = this.calculateFragmentValue(result);
      console.log(`抽取: ${result.displayName || result.name}, 碎片值: ${fragmentValue}`);

      this.setData({
        results: [result],
        running: false,
        showResult: true
      });

      // 添加到统计数据
      this.addToStatistics(result);
    }, 800);
  },

  // 十连抽
  multiDraw: function() {
    if (this.data.running) return;

    // 检查是否已完成自选设置
    if (!this.data.selectionCompleted) {
      wx.showToast({
        title: '请先完成自选设置',
        icon: 'none'
      });
      this.setData({
        showSelectPanel: true
      });
      return;
    }

    console.log('开始十连抽');
    this.setData({
      running: true,
      showResult: false,
      isMultiDraw: true
    });

    // 模拟抽奖过程
    setTimeout(() => {
      const results = [];
      let hasGuaranteed = false;
      let totalFragmentsThisDraw = 0;

      // 先进行9次普通抽奖
      for (let i = 0; i < 9; i++) {
        const result = this.drawItem(false);
        results.push(result);

        // 检查是否已经抽到了自选A级赛车(30天)
        if (result.id >= 19 && result.id <= 24) {
          hasGuaranteed = true;
        }

        // 计算碎片并记录日志
        const fragmentValue = this.calculateFragmentValue(result);
        totalFragmentsThisDraw += fragmentValue;
        console.log(`第${i+1}次抽取: ${result.displayName || result.name}, 碎片值: ${fragmentValue}`);

        // 添加到统计数据
        this.addToStatistics(result);
      }

      // 第10次抽奖，如果前9次没有抽到保底物品，则强制给予保底
      const lastResult = this.drawItem(!hasGuaranteed);

      // 计算最后一个物品的碎片值
      const lastItemFragmentValue = this.calculateFragmentValue(lastResult);
      totalFragmentsThisDraw += lastItemFragmentValue;
      console.log(`第10次抽取: ${lastResult.displayName || lastResult.name}, 碎片值: ${lastItemFragmentValue}`);
      console.log(`本次十连抽总计碎片: ${totalFragmentsThisDraw}`);

      results.push(lastResult);
      // 添加到统计数据
      this.addToStatistics(lastResult);

      this.setData({
        results: results,
        running: false,
        showResult: true
      });
    }, 800);
  },

  // 抽取一个物品
  drawItem: function(forceGuaranteed) {
    const activity = this.data.activity;
    const rand = Math.random();

    // 如果强制获得保底物品，则挑选一个随机的保底A级赛车
    if (forceGuaranteed) {
      // 从已选的A级赛车中随机选择一个30天版本
      const selectedACars = this.data.selectedOptions.aCars;
      const randomIndex = Math.floor(Math.random() * selectedACars.length);
      const selectedCarIndex = selectedACars[randomIndex];
      const selectedCar = this.data.selectionOptions.aCars[selectedCarIndex];

      // 这里要获取对应配置的ID，从id=19到id=24(六个30天A级赛车)
      const guaranteedItemId = 19 + randomIndex;
      let guaranteedItem = activity.items.find(item => item.id === guaranteedItemId);

      if (guaranteedItem) {
        // 复制一个新对象，避免修改原始配置
        guaranteedItem = { ...guaranteedItem };

        // 设置正确的图片和显示名称
        guaranteedItem.image = selectedCar.image;
        guaranteedItem.displayName = selectedCar.name + '(30天)';

        console.log(`保底抽取: ${guaranteedItem.displayName}`);
        return guaranteedItem;
      }
    }

    // 普通抽取逻辑
    let cumulativeProbability = 0;
    for (let i = 0; i < activity.items.length; i++) {
      const item = activity.items[i];
      cumulativeProbability += item.probability;

      if (rand < cumulativeProbability) {
        // 找到抽取的物品，复制一个新对象，避免修改原始配置
        const result = { ...item };

        // 如果是自选物品，需要设置正确的显示信息
        if (result.type === 'select') {
          const selectedIndex = this.data.selectedOptions[result.optionType][result.index];
          const selectedOption = this.data.selectionOptions[result.optionType][selectedIndex];

          // 设置正确的图片和显示名称
          result.image = selectedOption.image;
          result.displayName = selectedOption.name + (result.name.includes('天') ? result.name.substring(result.name.indexOf('(')) : '(永久)');
        }

        console.log(`抽取: ${result.displayName || result.name}`);
        return result;
      }
    }

    // 如果概率加起来不是100%，默认返回最后一个物品
    console.warn('概率计算有误，返回最后一个物品');
    return { ...activity.items[activity.items.length - 1] };
  },

  // 关闭结果面板
  closeResult: function() {
    this.setData({
      showResult: false
    });
  },

  // 重置统计
  resetStats: function() {
    // 调用新的resetDrawState方法
    this.resetDrawState();
  },

  // 返回活动列表（优化版本）
  goBack: function() {
    // 防止频繁点击
    if (this.data.isNavigating) {
      return;
    }

    this.setData({
      isNavigating: true
    });

    wx.navigateBack({
      delta: 1,
      animationType: 'none',
      success: () => {
        console.log('返回活动列表成功');
      },
      fail: (err) => {
        console.error('返回活动列表失败', err);
        // 重置状态
        this.setData({
          isNavigating: false
        });
      }
    });
  },

  /**
   * 加载并重置自选设置
   */
  resetAndClearSelection: function() {
    console.log('【7】resetAndClearSelection开始');
    console.log('【7.1】重置前选择状态:', JSON.stringify(this.data.selectedOptions));
    console.log('【7.1.1】当前selectionOptions状态:',
      `S级赛车: ${this.data.selectionOptions && this.data.selectionOptions.sCars ? this.data.selectionOptions.sCars.length : 0}个, ` +
      `T1机甲: ${this.data.selectionOptions && this.data.selectionOptions.t1Mechs ? this.data.selectionOptions.t1Mechs.length : 0}个, ` +
      `A级赛车: ${this.data.selectionOptions && this.data.selectionOptions.aCars ? this.data.selectionOptions.aCars.length : 0}个`
    );

    try {
      // 清空所有选择(只清空selectedOptions，不清空selectionOptions)
      const selectedOptions = {
        sCars: [],   // 确保是空数组
        t1Mechs: [], // 确保是空数组
        aCars: []    // 确保是空数组
      };

      console.log('【7.2】创建了空选择对象:', JSON.stringify(selectedOptions));
      console.log('【7.2.0】类型检查:',
        `sCars是数组: ${Array.isArray(selectedOptions.sCars)}, ` +
        `t1Mechs是数组: ${Array.isArray(selectedOptions.t1Mechs)}, ` +
        `aCars是数组: ${Array.isArray(selectedOptions.aCars)}`
      );

      // 确保selectionOptions没有被清空
      if (!this.data.selectionOptions || !this.data.selectionOptions.sCars || !this.data.selectionOptions.sCars.length) {
        console.log('【7.2.1】selectionOptions不存在或为空，重新设置');
        this.setData({
          selectionOptions: selectionOptions // 使用全局定义的selectionOptions
        });
        console.log('【7.2.2】已重新设置selectionOptions');
      }

      // 先完全清空已有的选择相关数据
      this.setData({
        selectedOptions: selectedOptions,
        selectedSCarsItems: [],
        selectedT1MechsItems: [],
        selectedACarsItems: [],
        selectionCompleted: false
      });

      // 更新自选项目的名称和图片
      if (this.data.activity && this.data.activity.items) {
        console.log('【7.3】调用updateSelectedItems前');
        this.updateSelectedItems(this.data.activity.items, selectedOptions);
        console.log('【7.4】调用updateSelectedItems后');
      } else {
        console.error('【错误】activity或items不存在，无法更新自选项目');
      }

      console.log('【7.5】设置data前');
      this.setData({
        selectedOptions: selectedOptions,
        selectionCompleted: false
      });
      console.log('【7.6】设置data后，当前选择:', JSON.stringify(this.data.selectedOptions));
      console.log('【7.6.1】设置后类型检查:',
        `sCars是数组: ${Array.isArray(this.data.selectedOptions.sCars)}, ` +
        `t1Mechs是数组: ${Array.isArray(this.data.selectedOptions.t1Mechs)}, ` +
        `aCars是数组: ${Array.isArray(this.data.selectedOptions.aCars)}`
      );

      // 清除本地存储
      try {
        wx.removeStorageSync('treasureHuntingSelection');
        console.log('【7.7】已清除本地存储');
      } catch(e) {
        console.error('【错误】清除本地存储失败:', e);
      }

      console.log('【7.8】已清空所有自选设置');
    } catch(error) {
      console.error('【错误】resetAndClearSelection出错:', error);
    }

    console.log('【7.9】resetAndClearSelection结束');
    console.log('【7.9.1】reset后selectionOptions状态:',
      `S级赛车: ${this.data.selectionOptions && this.data.selectionOptions.sCars ? this.data.selectionOptions.sCars.length : 0}个, ` +
      `T1机甲: ${this.data.selectionOptions && this.data.selectionOptions.t1Mechs ? this.data.selectionOptions.t1Mechs.length : 0}个, ` +
      `A级赛车: ${this.data.selectionOptions && this.data.selectionOptions.aCars ? this.data.selectionOptions.aCars.length : 0}个`
    );
    console.log('【7.9.2】reset后selectedOptions状态:',
      `sCars: ${JSON.stringify(this.data.selectedOptions.sCars)}, ` +
      `t1Mechs: ${JSON.stringify(this.data.selectedOptions.t1Mechs)}, ` +
      `aCars: ${JSON.stringify(this.data.selectedOptions.aCars)}`
    );
  },

  /**
   * 初始化页面时的选择状态检查
   */
  checkSelectionStatus: function() {
    console.log('【8】checkSelectionStatus开始');
    console.log('【8.1】当前选择状态:', JSON.stringify(this.data.selectedOptions));

    try {
      // 防御性检查，确保selectedOptions存在
      if (!this.data.selectedOptions) {
        console.warn('【警告】selectedOptions为null，无法检查选择状态');
        return false;
      }

      const { sCars, t1Mechs, aCars } = this.data.selectedOptions;

      // 防御性检查，确保所有数组属性存在
      if (!Array.isArray(sCars) || !Array.isArray(t1Mechs) || !Array.isArray(aCars)) {
        console.warn('【警告】selectedOptions中存在非数组属性');
        return false;
      }

      // 只有当所有三类选择都满足要求时，才认为自选已完成
      const isComplete = sCars.length === 2 && t1Mechs.length === 2 && aCars.length === 6;

      console.log('【8.2】sCars长度:', sCars.length, 't1Mechs长度:', t1Mechs.length, 'aCars长度:', aCars.length);
      console.log('【8.3】isComplete=', isComplete);

      this.setData({
        selectionCompleted: isComplete
      });

      console.log('【8.4】设置selectionCompleted=', isComplete);
      return isComplete;
    } catch(error) {
      console.error('【错误】checkSelectionStatus出错:', error);
      return false;
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    console.log('【9】onReady - 页面初次渲染完成');
    console.log('【9.1】当前选择状态:', JSON.stringify(this.data.selectedOptions));
    console.log('【9.2】自选面板显示状态:', this.data.showSelectPanel);
    console.log('【9.3】已选择的项目:', {
      'S级赛车': JSON.stringify(this.data.selectedSCarsItems),
      'T1机甲': JSON.stringify(this.data.selectedT1MechsItems),
      'A级赛车': JSON.stringify(this.data.selectedACarsItems)
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: `QQ飞车 ${this.data.activity.name} 抽奖模拟器`,
      path: '/pages/lottery/treasure-hunting/treasure-hunting'
    };
  },

  /**
   * 判断某个项目是否被选中
   * @param {string} type - 类型，如'sCars'
   * @param {number} index - 项目索引
   * @return {boolean} - 是否选中
   */
  isItemSelected: function(type, index) {
    const currentSelection = this.data.selectedOptions[type];
    const numIndex = Number(index);
    return currentSelection.some(item => Number(item) === numIndex);
  },

  /**
   * 获取项目在选择列表中的序号（从1开始）
   * @param {string} type - 类型，如'sCars'
   * @param {number} index - 项目索引
   * @return {number} - 序号，如果未选中返回-1
   */
  getItemSelectNumber: function(type, index) {
    const currentSelection = this.data.selectedOptions[type];
    const numIndex = Number(index);
    const foundIndex = currentSelection.findIndex(item => Number(item) === numIndex);
    return foundIndex !== -1 ? foundIndex + 1 : -1;
  },

  /**
   * 切换概率公示面板的显示状态
   */
  toggleProbabilityPanel: function() {
    this.setData({
      showProbabilityPanel: !this.data.showProbabilityPanel
    });
  },

  /**
   * 打开自选面板
   */
  openSelectPanel: function() {
    console.log('【3】openSelectPanel开始');

    console.log('【3.1】打开面板前状态:');
    console.log('【3.1.1】showSelectPanel:', this.data.showSelectPanel);
    console.log('【3.1.2】currentStep:', this.data.currentStep);
    console.log('【3.1.3】selectedOptions:', JSON.stringify(this.data.selectedOptions));

    try {
      // 检查selectionOptions是否已初始化
      if (!this.data.selectionOptions || !this.data.selectionOptions.sCars || !this.data.selectionOptions.sCars.length) {
        console.log('【3.1.4】selectionOptions未初始化或为空，使用全局selectionOptions');

        // 确保先完成这个数据
        this.setData({
          selectionOptions: JSON.parse(JSON.stringify(selectionOptions)) // 使用全局定义的selectionOptions，深拷贝防止修改原始对象
        });

        console.log('【3.1.5】已重新设置selectionOptions');
      }

      console.log('【3.1.6】selectionOptions已存在:',
        `S级赛车: ${this.data.selectionOptions.sCars ? this.data.selectionOptions.sCars.length : 0}个, ` +
        `T1机甲: ${this.data.selectionOptions.t1Mechs ? this.data.selectionOptions.t1Mechs.length : 0}个, ` +
        `A级赛车: ${this.data.selectionOptions.aCars ? this.data.selectionOptions.aCars.length : 0}个`
      );

      // 默认显示S级赛车选择页
      let initialStep = 1;

      // 如果已经有完整的S级赛车选择，且点击修改自选，则自动进入到下一步
      if (this.data.selectionCompleted && this.data.selectedOptions.sCars.length === 2) {
        if (this.data.selectedOptions.t1Mechs.length === 2) {
          // 如果S级和T1都已选择完成，显示A级选择页
          initialStep = 3;
        } else {
          // 如果只有S级选择完成，显示T1选择页
          initialStep = 2;
        }
      }

      // 打开自选面板但保留现有选择
      this.setData({
        showSelectPanel: true,
        currentStep: initialStep,
        _forceUpdate: Date.now() // 强制刷新UI
      });

      console.log('【3.3】保留现有选择，面板已打开');
      console.log('【3.3.1】selectedOptions:', JSON.stringify(this.data.selectedOptions));

      // 存储所有道具的备份
      if (!this.data.backupItems && this.data.activity && this.data.activity.items) {
        console.log('【3.4】备份原始道具列表');
        this.setData({
          backupItems: this.data.activity.items
        });
      }

      // 确保已选择项目的显示正确
      this.updateSelectedItems(this.data.activity.items, this.data.selectedOptions);

      console.log('【3.5】自选面板已打开，初始步骤为', initialStep);
    } catch(error) {
      console.error('【错误】openSelectPanel出错:', error);

      // 出错时的恢复措施
      wx.showToast({
        title: '打开自选面板失败',
        icon: 'none'
      });
    }

    console.log('【3.6】openSelectPanel结束');
  },

  // 下一步
  nextStep: function() {
    const { sCars, t1Mechs } = this.data.selectedOptions;
    const currentStep = this.data.currentStep;

    // 检查当前步骤的选择是否完成
    if (currentStep === 1 && sCars.length !== 2) {
      wx.showToast({
        title: '请选择2个S级赛车',
        icon: 'none'
      });
      return;
    }

    if (currentStep === 2 && t1Mechs.length !== 2) {
      wx.showToast({
        title: '请选择2个T1机甲',
        icon: 'none'
      });
      return;
    }

    // 前进到下一步
    this.setData({
      currentStep: currentStep + 1
    });
  },

  // 上一步
  prevStep: function() {
    const currentStep = this.data.currentStep;
    if (currentStep > 1) {
      this.setData({
        currentStep: currentStep - 1
      });
    }
  },

  // 关闭自选面板
  closeSelectPanel: function() {
    const { sCars, t1Mechs, aCars } = this.data.selectedOptions;

    // 检查是否已完成选择
    if (sCars.length !== 2 || t1Mechs.length !== 2 || aCars.length !== 6) {
      wx.showModal({
        title: '提示',
        content: '你尚未完成自选设置，退出后设置将不会保存，确定要退出吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              showSelectPanel: false,
              currentStep: 1 // 重置步骤，避免出现4/3的问题
            });
          }
        }
      });
      return;
    }

    // 如果已完成选择，则直接关闭面板并更新活动数据
    this.updateActivityItemsWithSelection(this.data.selectedOptions);

    this.setData({
      showSelectPanel: false,
      selectionCompleted: true
    });

    // 保存当前选择到本地存储
    this.saveSelectionToStorage();

    // 显示成功提示
    wx.showToast({
      title: '自选设置已完成',
      icon: 'success'
    });
  },

  /**
   * 处理S级赛车选择变化
   * @param {Object} e - 事件对象
   */
  onSCarsChange: function(e) {
    // 检查是否为初次渲染自动触发
    if (!e.detail || !e.detail.value) {
      console.log('忽略S级赛车自动触发事件');
      return;
    }

    const values = e.detail.value.map(Number);
    console.log('S级赛车选择变化 - 原始值:', e.detail.value);
    console.log('S级赛车选择变化 - 转换为数字后:', values);

    // 选择数量限制为2个
    let selectedValues = values;
    if (values.length > 2) {
      // 只保留最后选择的2个
      selectedValues = values.slice(-2);
      console.log('S级赛车选择超出限制，截取为:', selectedValues);
      wx.showToast({
        title: '最多只能选择2个S级赛车',
        icon: 'none'
      });
    }

    // 更新选择数据
    const selectedOptions = this.data.selectedOptions;
    selectedOptions.sCars = selectedValues;

    // 更新自选项目的名称和图片
    this.updateSelectedItems(this.data.activity.items, selectedOptions);

    console.log('S级赛车最终选择结果:', selectedOptions.sCars);

    this.setData({
      selectedOptions: selectedOptions
    });
  },

  /**
   * 处理T1机甲选择变化
   * @param {Object} e - 事件对象
   */
  onT1MechsChange: function(e) {
    // 检查是否为初次渲染自动触发
    if (!e.detail || !e.detail.value) {
      console.log('忽略T1机甲自动触发事件');
      return;
    }

    const values = e.detail.value.map(Number);
    console.log('T1机甲选择变化 - 原始值:', e.detail.value);
    console.log('T1机甲选择变化 - 转换为数字后:', values);

    // 选择数量限制为2个
    let selectedValues = values;
    if (values.length > 2) {
      // 只保留最后选择的2个
      selectedValues = values.slice(-2);
      console.log('T1机甲选择超出限制，截取为:', selectedValues);
        wx.showToast({
        title: '最多只能选择2个T1机甲',
          icon: 'none'
        });
    }

    // 更新选择数据
    const selectedOptions = this.data.selectedOptions;
    selectedOptions.t1Mechs = selectedValues;

    // 更新自选项目的名称和图片
    this.updateSelectedItems(this.data.activity.items, selectedOptions);

    console.log('T1机甲最终选择结果:', selectedOptions.t1Mechs);

    this.setData({
      selectedOptions: selectedOptions
    });
  },

  /**
   * 处理A级赛车选择变化
   * @param {Object} e - 事件对象
   */
  onACarsChange: function(e) {
    // 检查是否为初次渲染自动触发
    if (!e.detail || !e.detail.value) {
      console.log('忽略A级赛车自动触发事件');
        return;
      }

    const values = e.detail.value.map(Number);
    console.log('A级赛车选择变化 - 原始值:', e.detail.value);
    console.log('A级赛车选择变化 - 转换为数字后:', values);

    // 选择数量限制为6个
    let selectedValues = values;
    if (values.length > 6) {
      // 只保留最后选择的6个
      selectedValues = values.slice(-6);
      console.log('A级赛车选择超出限制，截取为:', selectedValues);
      wx.showToast({
        title: '最多只能选择6个A级赛车',
        icon: 'none'
      });
    }

    // 更新选择数据
    const selectedOptions = this.data.selectedOptions;
    selectedOptions.aCars = selectedValues;

    // 更新自选项目的名称和图片
    this.updateSelectedItems(this.data.activity.items, selectedOptions);

    console.log('A级赛车最终选择结果:', selectedOptions.aCars);

    this.setData({
      selectedOptions: selectedOptions
    });
  },

  /**
   * 切换选择状态
   */
  toggleSelection: function(e) {
    console.log('【10】toggleSelection开始');
    console.log('【10.1】事件数据:', JSON.stringify(e.currentTarget.dataset));

    try {
      // 确保index是数字类型
      const type = e.currentTarget.dataset.type;
      const indexStr = e.currentTarget.dataset.index;
      const index = Number(indexStr);

      console.log(`【10.2】切换选择: 类型=${type}, 原始索引=${indexStr}, 转换后索引=${index}, 类型=${typeof index}`);

      // 检查selectedOptions是否为null
      if (!this.data.selectedOptions) {
        console.error('【错误】selectedOptions为null，初始化为空对象');
        this.setData({
          selectedOptions: {
            sCars: [],
            t1Mechs: [],
            aCars: []
          }
        });
      }

      // 创建当前选中状态的深拷贝，而不是直接修改引用
      const selectedOptions = JSON.parse(JSON.stringify(this.data.selectedOptions || {sCars: [], t1Mechs: [], aCars: []}));
      console.log('【10.3】当前选择状态:', JSON.stringify(selectedOptions));
      console.log(`【10.3.1】${type}数组:`, JSON.stringify(selectedOptions[type]));

      // 确保selectedOptions的数组全部是数字类型
      if (selectedOptions[type] && Array.isArray(selectedOptions[type])) {
        selectedOptions[type] = selectedOptions[type].map(Number);
      } else {
        // 如果不是数组或不存在，初始化为空数组
        selectedOptions[type] = [];
      }

      // 检查是否已经达到最大选择数量且当前项未被选中
      const isSelected = selectedOptions[type].indexOf(index) !== -1;
      console.log(`【10.4】当前项选中状态: ${isSelected ? '已选中' : '未选中'}, indexOf结果:`, selectedOptions[type].indexOf(index));

      let maxAllowed = 0;
      switch(type) {
        case 'sCars':
        case 't1Mechs':
          maxAllowed = 2;
          break;
        case 'aCars':
          maxAllowed = 6;
          break;
      }

      console.log(`【10.5】最大允许选择: ${maxAllowed}, 当前已选: ${selectedOptions[type].length}`);

      if (isSelected) {
        // 如果已选中，则移除选中状态
        const newSelection = selectedOptions[type].filter(item => Number(item) !== index);
        console.log('【10.6】移除选中状态后新的选择:', JSON.stringify(newSelection));
        selectedOptions[type] = newSelection;
      } else if (selectedOptions[type].length < maxAllowed) {
        // 如果未选中且未达到最大选择数量，则添加选中状态
        selectedOptions[type].push(index);
        console.log('【10.7】添加选中状态后新的选择:', JSON.stringify(selectedOptions[type]));
      } else {
        // 已达到最大选择数量
        console.log(`【10.8】已达到最大选择数量 ${maxAllowed}，无法再选择`);
        wx.showToast({
          title: `最多只能选择${maxAllowed}个`,
          icon: 'none'
        });
        return;
      }

      // 更新自选项目的名称和图片
      console.log('【10.14】更新选中项目的详细信息');
      const activity = lotteryConfig.treasurehunting;
      this.updateSelectedItems(activity.items, selectedOptions);

      // 更新选中状态 - 直接设置完整数据，不采用延时操作
      console.log('【10.9】更新选中状态前:', JSON.stringify(this.data.selectedOptions));

      // 设置完整的selectedOptions对象，同时强制刷新UI
      this.setData({
        selectedOptions: selectedOptions,
        _forceUpdate: Date.now()
      });

      console.log('【10.10】更新选中状态后:', JSON.stringify(this.data.selectedOptions));

      // 每次切换选择后保存当前选择状态到缓存
      this.saveSelectionToStorage();

      // 检查自选是否已完成
      console.log('【10.15】检查自选完成状态');
      this.checkSelectionStatus();

      // 强制刷新界面
      this.setData({
        _forceRefresh: Date.now() // 使用不同的key强制刷新UI
      });

      console.log('【10.16】toggleSelection结束');
    } catch(error) {
      console.error('【错误】toggleSelection出错:', error);
    }
  },

  /**
   * 从已选项中移除选中项目
   */
  removeSelectedItem: function(e) {
    console.log('【12】removeSelectedItem开始');
    try {
      const type = e.currentTarget.dataset.type;
      const index = Number(e.currentTarget.dataset.index);

      console.log(`【12.1】移除项目: 类型=${type}, 索引=${index}`);

      // 创建当前选中状态的深拷贝
      const selectedOptions = JSON.parse(JSON.stringify(this.data.selectedOptions || {sCars: [], t1Mechs: [], aCars: []}));

      if (selectedOptions[type] && Array.isArray(selectedOptions[type])) {
        // 移除指定索引的项目
        const newSelection = selectedOptions[type].filter(item => Number(item) !== index);
        selectedOptions[type] = newSelection;

        console.log(`【12.2】移除后的选择: ${JSON.stringify(selectedOptions[type])}`);

        // 更新自选项目的名称和图片
        const activity = lotteryConfig.treasurehunting;
        this.updateSelectedItems(activity.items, selectedOptions);

        // 更新数据
        this.setData({
          selectedOptions: selectedOptions,
          _forceUpdate: Date.now()
        });

        // 保存当前选择状态到缓存
        this.saveSelectionToStorage();

        // 检查自选是否已完成
        this.checkSelectionStatus();
      }

      console.log('【12.3】removeSelectedItem结束');
    } catch(error) {
      console.error('【错误】removeSelectedItem出错:', error);
    }
  },

  // 确认自选
  confirmSelection: function() {
    const { sCars, t1Mechs, aCars } = this.data.selectedOptions;
    const currentStep = this.data.currentStep;

    // 如果在步骤3（A级赛车选择）且未完成选择
    if (currentStep === 3 && aCars.length !== 6) {
      wx.showToast({
        title: `请选择6个A级赛车，当前已选${aCars.length}个`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查是否已完成所有需要的选择
    const sComplete = sCars.length === 2;
    const tComplete = t1Mechs.length === 2;
    const aComplete = aCars.length === 6;

    if (!sComplete || !tComplete || !aComplete) {
      let message = '请完成以下选择：';
      if (!sComplete) message += '\n- 选择2个S级赛车';
      if (!tComplete) message += '\n- 选择2个T1机甲';
      if (!aComplete) message += '\n- 选择6个A级赛车';

      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 存储选中的自选项到本地缓存
    try {
      const selectionData = {
        selectedOptions: this.data.selectedOptions,
        selectedSCarsItems: this.data.selectedSCarsItems,
        selectedT1MechsItems: this.data.selectedT1MechsItems,
        selectedACarsItems: this.data.selectedACarsItems,
        completed: true
      };

      wx.setStorageSync('treasureHuntingSelection', JSON.stringify(selectionData));
    } catch(e) {
      console.error('保存自选内容到本地存储失败:', e);
    }

    // 修改活动数据中的自选项
    this.updateActivityItemsWithSelection(this.data.selectedOptions);

    // 设置已完成自选状态，并关闭选择面板
    this.setData({
      showSelectPanel: false,
      selectionCompleted: true,
      currentStep: 1, // 重置步骤，以便下次打开时从第一步开始
      _forceUpdate: Date.now() // 强制刷新UI
    });

    // 显示成功提示
    wx.showToast({
      title: '自选设置已完成',
      icon: 'success'
    });
  },

  /**
   * 重置自选设置
   */
  resetSelection: function() {
    // 重置为空选择
    const selectedOptions = {
      sCars: [],
      t1Mechs: [],
      aCars: []
    };

    // 更新自选项目的名称和图片
    this.updateSelectedItems(this.data.activity.items, selectedOptions);

    this.setData({
      selectedOptions,
      selectionCompleted: false,
      showSelectPanel: true,
      currentStep: 1
    });

    // 清除本地存储
    wx.removeStorageSync('treasureHuntingSelection');

    // 重置统计数据
    this.initStatistics();

    wx.showToast({
      title: '已重置自选设置',
      icon: 'success'
    });
  },

  /**
   * 加载上次保存的设置
   */
  loadPreviousSelection: function() {
    // 尝试从本地存储加载自选设置
    try {
      const storedSelection = wx.getStorageSync('treasureHuntingSelection');

      if (storedSelection) {
        // 解析保存的设置
        const parsedSelection = JSON.parse(storedSelection);
        let selectedOptions = {
          sCars: [],
          t1Mechs: [],
          aCars: []
        };

        // 确保所有索引都是数字类型
        if (parsedSelection.selectedOptions) {
          for (const key in parsedSelection.selectedOptions) {
            if (Array.isArray(parsedSelection.selectedOptions[key])) {
              selectedOptions[key] = parsedSelection.selectedOptions[key].map(Number);
            }
          }
        }

        // 更新自选项目的名称和图片
        this.updateSelectedItems(this.data.activity.items, selectedOptions);

        this.setData({
          selectedOptions: selectedOptions,
          selectionCompleted: parsedSelection.completed || false
        });

        console.log('已加载上次保存的设置:', selectedOptions);

        wx.showToast({
          title: '已加载上次保存的设置',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '没有找到保存的设置',
          icon: 'none'
        });
      }
    } catch (e) {
      console.error('加载上次保存的设置失败', e);
      wx.showToast({
        title: '加载设置失败',
        icon: 'none'
      });
    }
  },

  /**
   * 完成自选设置
   */
  completeSelection: function() {
    console.log('【9】completeSelection开始');
    console.log('【9.1】当前选择状态:', JSON.stringify(this.data.selectedOptions));

    try {
      // 检查是否已完成所有需要的选择
      const sComplete = this.data.selectedOptions.sCars && this.data.selectedOptions.sCars.length === 2;
      const tComplete = this.data.selectedOptions.t1Mechs && this.data.selectedOptions.t1Mechs.length === 2;
      const aComplete = this.data.selectedOptions.aCars && this.data.selectedOptions.aCars.length === 6;

      console.log(`【9.2】选择完成状态：S级=${sComplete}, T1=${tComplete}, A级=${aComplete}`);

      if (!sComplete || !tComplete || !aComplete) {
        console.log('【9.3】选择不完整，无法完成设置');
        wx.showToast({
          title: '请完成所有必选项',
          icon: 'none'
        });
        return;
      }

      // 存储选中的自选项到本地缓存
      console.log('【9.4】开始保存自选内容');
      try {
        const selectionData = {
          selectedOptions: this.data.selectedOptions,
          selectedSCarsItems: this.data.selectedSCarsItems,
          selectedT1MechsItems: this.data.selectedT1MechsItems,
          selectedACarsItems: this.data.selectedACarsItems,
          completed: true
        };

        wx.setStorageSync('treasureHuntingSelection', JSON.stringify(selectionData));
        console.log('【9.5】已保存自选内容到本地存储');
      } catch(e) {
        console.error('【错误】保存自选内容到本地存储失败:', e);
      }

      // 修改活动数据中的自选项
      console.log('【9.6】开始更新活动数据中的自选项');
      this.updateActivityItemsWithSelection(this.data.selectedOptions);

      // 设置已完成自选状态，并更新活动数据
      console.log('【9.9】更新自选完成状态');
      this.setData({
        showSelectPanel: false,
        selectionCompleted: true,
        _forceUpdate: Date.now(), // 强制刷新UI
        _forceRefresh: Date.now() // 使用不同的key再次强制刷新
      });

      // 显示成功提示
      wx.showToast({
        title: '自选设置已完成',
        icon: 'success'
      });

    } catch(error) {
      console.error('【错误】completeSelection出错:', error);

      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      });
    }

    console.log('【9.12】completeSelection结束');
  },

  /**
   * 恢复原始项目列表
   */
  restoreOriginalItems: function() {
    if (this.data.originalItems) {
      // 获取活动数据
      const activity = this.data.activity;

      // 恢复原始项目列表
      activity.items = this.data.originalItems;

      // 更新页面数据
      this.setData({
        activity: activity,
        selectionCompleted: false
      });

      console.log('已恢复原始项目列表');
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('【2】onShow - 页面显示');

    try {
      console.log('【2.1】当前状态:');
      console.log('【2.1.1】selectionOptions:',
        `S级赛车: ${this.data.selectionOptions && this.data.selectionOptions.sCars ? this.data.selectionOptions.sCars.length : 0}个, ` +
        `T1机甲: ${this.data.selectionOptions && this.data.selectionOptions.t1Mechs ? this.data.selectionOptions.t1Mechs.length : 0}个, ` +
        `A级赛车: ${this.data.selectionOptions && this.data.selectionOptions.aCars ? this.data.selectionOptions.aCars.length : 0}个`
      );
      console.log('【2.1.2】selectedOptions:', JSON.stringify(this.data.selectedOptions));
      console.log('【2.1.3】selectionCompleted:', this.data.selectionCompleted);

      // 如果selectionOptions未初始化，重新设置
      if (!this.data.selectionOptions || !this.data.selectionOptions.sCars || !this.data.selectionOptions.sCars.length) {
        console.log('【2.2】selectionOptions未初始化，重新设置');
        this.setData({
          selectionOptions: JSON.parse(JSON.stringify(selectionOptions))
        });

        // 强制刷新
        setTimeout(() => {
          this.setData({
            _forceUpdate: Date.now()
          });
        }, 100);
      }

      // 检查自选是否已完成
      this.checkSelectionStatus();

      console.log('【2.3】onShow结束');
    } catch (error) {
      console.error('【错误】onShow异常:', error);
    }

    // 在页面显示时更新VIP信息
    this.updateVipAndFreeCount();
  },

  // drawOne函数，为兼容旧代码，重定向到drawItem
  drawOne: function() {
    return this.drawItem(false);
  },

  /**
   * 单抽按钮点击事件处理
   */
  singleDraw() {
    if (!this.canDraw(1)) return;

    // 消费免费次数（非VIP用户）- 立即扣除次数
    if (!this.data.isVip) {
      // 直接调用updateFreeCountCache处理次数扣除和UI更新
      this.updateFreeCountCache(-1);
    }

    // 执行抽奖
    this.performSingleDraw();
  },

  /**
   * 十连抽按钮点击事件处理
   */
  multiDraw() {
    if (!this.canDraw(10)) return;

    // 消费免费次数（非VIP用户）- 立即扣除次数
    if (!this.data.isVip) {
      // 直接调用updateFreeCountCache处理次数扣除和UI更新
      this.updateFreeCountCache(-10);
    }

    // 执行十连抽
    this.performMultiDraw();
  },

  /**
   * 检查是否可以抽奖
   * @param {number} count 要抽奖的次数
   * @returns {boolean} 是否可以抽奖
   */
  canDraw: function(count) {
    // 先检查是否完成了自选设置
    if (!this.data.selectionCompleted) {
      wx.showToast({
        title: '请先完成自选设置',
        icon: 'none'
      });
      // 显示设置面板
      this.openSelectPanel();
      return false;
    }

    // VIP用户不受限制
    if (this.data.isVip) return true;

    // 非VIP用户检查是否有足够的免费次数
    if (this.data.freeCount < count) {
      // 显示VIP对话框
      this.setData({
        showVipDialog: true
      });
      return false;
    }

    return true;
  },

  /**
   * 执行单抽逻辑
   */
  performSingleDraw() {
    // 开始抽奖动画
    this.setData({
      running: true,
      showResult: false,
      isMultiDraw: false
    });

    setTimeout(() => {
      // 调用抽奖函数并保存结果
      const result = this.drawItem(false);

      // 计算碎片值
      const fragmentValue = this.calculateFragmentValue(result);
      console.log(`单抽: ${result.displayName || result.name}, 碎片值: ${fragmentValue}`);

      // 添加到统计数据 - 不在这里更新总抽数
      this.addToStatistics(result, false);

      // 更新总碎片数
      const newTotalFragments = this.data.totalFragments + fragmentValue;

      // 检查是否有金色或紫色稀有物品
      let hasTotalGoldItems = this.data.hasTotalGoldItems;
      let hasTotalPurpleItems = this.data.hasTotalPurpleItems;

      // 检查当前抽到的物品是否是金色或紫色
      if (result.background === 'gold') {
        hasTotalGoldItems = true;
      } else if (result.background === 'purple') {
        hasTotalPurpleItems = true;
      }

      // 更新UI显示
      this.setData({
        results: [result], // 保存单抽结果为数组
        running: false,
        showResult: true,
        totalDraws: this.data.totalDraws + 1, // 只在这里更新总抽数
        totalFragments: newTotalFragments,
        hasTotalGoldItems: hasTotalGoldItems,
        hasTotalPurpleItems: hasTotalPurpleItems
      });
    }, 1000);
  },

  /**
   * 执行十连抽逻辑
   */
  performMultiDraw: function() {
    // 开始抽奖动画
    this.setData({
      running: true,
      showResult: false,
      isMultiDraw: true
    });

    setTimeout(() => {
      const results = [];
      let hasGuaranteed = false;
      let totalFragmentsThisDraw = 0;
      let hasTotalGoldItems = this.data.hasTotalGoldItems;
      let hasTotalPurpleItems = this.data.hasTotalPurpleItems;

      // 先进行9次普通抽奖
      for (let i = 0; i < 9; i++) {
        const result = this.drawItem(false);
        results.push(result);

        // 检查是否已经抽到了自选A级赛车(30天)
        if (result.id >= 19 && result.id <= 24) {
          hasGuaranteed = true;
        }

        // 计算碎片并记录日志
        const fragmentValue = this.calculateFragmentValue(result);
        totalFragmentsThisDraw += fragmentValue;

        // 添加到统计数据 - 不更新总抽数
        this.addToStatistics(result, false);

        // 检查是否是金色或紫色稀有物品
        if (result.background === 'gold') {
          hasTotalGoldItems = true;
        } else if (result.background === 'purple') {
          hasTotalPurpleItems = true;
        }
      }

      // 第10次抽奖，如果前9次没有抽到保底物品，则强制给予保底
      const lastResult = this.drawItem(!hasGuaranteed);
      results.push(lastResult);

      // 计算最后一个物品的碎片值
      const lastItemFragmentValue = this.calculateFragmentValue(lastResult);
      totalFragmentsThisDraw += lastItemFragmentValue;

      // 添加到统计数据 - 不更新总抽数
      this.addToStatistics(lastResult, false);

      // 检查最后一个物品是否是金色或紫色稀有物品
      if (lastResult.background === 'gold') {
        hasTotalGoldItems = true;
      } else if (lastResult.background === 'purple') {
        hasTotalPurpleItems = true;
      }

      // 更新UI显示
      this.setData({
        results: results,
        running: false,
        showResult: true,
        totalDraws: this.data.totalDraws + 10, // 只在这里更新总抽数
        totalFragments: this.data.totalFragments + totalFragmentsThisDraw,
        hasTotalGoldItems: hasTotalGoldItems,
        hasTotalPurpleItems: hasTotalPurpleItems
      });
    }, 1000);
  },

  /**
   * VIP对话框关闭事件处理
   */
  handleVipDialogClose() {
    this.setData({ showVipDialog: false });
  },

  /**
   * 添加免费次数事件处理
   */
  handleAddFreeCount(e) {
    const count = e.detail.count || 100;
    const newFreeCount = api.updateFreeCount(PAGE_KEY, count);

    this.setData({ freeCount: newFreeCount });

    wx.showToast({
      title: `获得${count}次免费机会`,
      icon: 'success'
    });
  },

  /**
   * VIP徽章点击事件处理
   */
  handleVipBadgeClick() {
    if (!this.data.isVip) {
      this.setData({ showVipDialog: true });
    }
  },

  /**
   * 更新免费次数缓存
   * @param {number} change 次数变化值
   */
  updateFreeCountCache: function(change = 0) {
    try {
      // 更新存储中的免费次数
      const newFreeCount = api.updateFreeCount(PAGE_KEY, change);

      // 直接更新UI显示
      this.setData({
        freeCount: newFreeCount
      });

      console.log('更新免费次数缓存 - 变化:', change, '新值:', newFreeCount);
      return newFreeCount;
    } catch (error) {
      console.error('更新免费次数缓存失败:', error);
      return this.data.freeCount;
    }
  },

  /**
   * VIP徽章点击事件
   */
  onVipBadgeTap: function() {
    this.setData({
      showVipDialog: true
    });
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({
      showVipDialog: false
    });
  },

  /**
   * 添加免费次数事件
   */
  onAddFreeAttempts: function() {
    // 添加免费次数（从20次改为100次）
    const newFreeCount = api.updateFreeCount(PAGE_KEY, 100);

    this.setData({
      freeCount: newFreeCount,
      showVipDialog: false
    });

    // 显示添加成功提示
    wx.showToast({
      title: '已添加100次免费机会',
      icon: 'success'
    });
  },

  /**
   * 抽取多个奖励
   * @param {number} count 抽取数量
   * @returns {Array} 抽取结果
   */
  drawItems: function(count) {
    // 特殊处理十连抽保底逻辑
    if (count === 10) {
      const results = [];
      let hasGuaranteed = false;
      let fragmentTotal = 0;

      // 先进行9次普通抽奖
      for (let i = 0; i < 9; i++) {
        const result = this.drawItem(false);
        results.push(result);

        // 检查是否已经抽到了自选A级赛车(30天)
        if (result.id >= 19 && result.id <= 24) {
          hasGuaranteed = true;
        }

        // 累加碎片值
        fragmentTotal += result.fragmentValue || 0;
      }

      // 第10次抽奖，如果前9次没有抽到保底物品，则强制给予保底
      const lastResult = this.drawItem(!hasGuaranteed);
      results.push(lastResult);

      // 累加最后一个物品的碎片值
      fragmentTotal += lastResult.fragmentValue || 0;

      // 更新统计数据和UI
      this.setData({
        results: results,
        totalDraws: this.data.totalDraws + count,
        totalCost: this.data.totalCost + this.data.activity.multiCost,
        totalFragments: this.data.totalFragments + fragmentTotal
      });

      return results;
    }

    // 处理普通抽奖（非十连抽）
    const results = [];
    for (let i = 0; i < count; i++) {
      const result = this.drawItem(false);
      results.push(result);
    }

    // 更新统计数据和UI
    this.setData({
      results: results,
      totalDraws: this.data.totalDraws + count,
      totalCost: this.data.totalCost + this.data.activity.multiCost,
    });

    // 计算碎片总数
    let fragmentTotal = 0;
    for (let i = 0; i < results.length; i++) {
      fragmentTotal += results[i].fragmentValue || 0;
    }

    // 更新总碎片数
    this.setData({
      totalFragments: this.data.totalFragments + fragmentTotal
    });

    return results;
  },

  /**
   * 批量更新统计数据
   * @param {Array} results 抽取结果
   */
  updateStatisticsBatch: function(results) {
    // 临时统计对象
    const tempStats = {};

    // 统计每个物品的抽取次数
    for (let i = 0; i < results.length; i++) {
      const id = results[i].id;
      if (!tempStats[id]) {
        tempStats[id] = {
          item: results[i],
          count: 1
        };
      } else {
        tempStats[id].count++;
      }
    }

    // 更新统计数据
    for (const id in tempStats) {
      const found = this.data.statistics.find(item => item.item.id === id);
      if (found) {
        found.count += tempStats[id].count;
      } else {
        this.data.statistics.push(tempStats[id]);
      }
    }

    // 更新金色道具和紫色道具的计数
    let hasTotalGoldItems = false;
    let hasTotalPurpleItems = false;

    for (let i = 0; i < this.data.statistics.length; i++) {
      if (this.data.statistics[i].item.background === 'gold' && this.data.statistics[i].count > 0) {
        hasTotalGoldItems = true;
      }
      if (this.data.statistics[i].item.background === 'purple' && this.data.statistics[i].count > 0) {
        hasTotalPurpleItems = true;
      }
    }

    // 更新UI
    this.setData({
      statistics: this.data.statistics,
      hasTotalGoldItems: hasTotalGoldItems,
      hasTotalPurpleItems: hasTotalPurpleItems
    });
  },

  /**
   * 阻止触摸滑动事件传递，防止弹窗背景滚动
   */
  preventTouchMove: function() {
    // 阻止事件冒泡，不执行任何操作
    return false;
  },

  /**
   * 检查稀有物品
   */
  checkRareItems() {
    // 遍历统计数据，查找是否有永久奖励（传说级、金色）和稀有奖励（紫色）
    let hasLegendaryItems = false;
    let hasTotalGoldItems = false;
    let hasTotalPurpleItems = false;

    for (const stat of this.data.statistics) {
      if (stat.count > 0) {
        if (stat.item.background === 'legendary') {
          hasLegendaryItems = true;
        } else if (stat.item.background === 'gold') {
          hasTotalGoldItems = true;
        } else if (stat.item.background === 'purple') {
          hasTotalPurpleItems = true;
        }
      }
    }

    this.setData({
      hasLegendaryItems: hasLegendaryItems,
      hasTotalGoldItems: hasTotalGoldItems,
      hasTotalPurpleItems: hasTotalPurpleItems
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 保存当前抽奖状态到本地缓存
    this.saveDrawState();
  },

  /**
   * 保存抽奖状态到本地缓存
   */
  saveDrawState: function() {
    try {
      const stateKey = `${PAGE_KEY}_state`;
      const state = {
        totalDraws: this.data.totalDraws,
        totalFragments: this.data.totalFragments,
        statistics: this.data.statistics,
        hasLegendaryItems: this.data.hasLegendaryItems,
        hasTotalGoldItems: this.data.hasTotalGoldItems,
        hasTotalPurpleItems: this.data.hasTotalPurpleItems,
        savedTime: new Date().getTime()
      };

      wx.setStorageSync(stateKey, JSON.stringify(state));
      console.log('已保存抽奖状态:', state.totalDraws, '次抽奖');
    } catch (error) {
      console.error('保存抽奖状态失败:', error);
    }
  },

  /**
   * 从本地缓存加载抽奖状态
   */
  loadDrawState: function() {
    try {
      const stateKey = `${PAGE_KEY}_state`;
      const stateStr = wx.getStorageSync(stateKey);

      if (stateStr) {
        const state = JSON.parse(stateStr);

        // 如果有抽奖记录，更新页面状态
        if (state.totalDraws > 0) {
          this.setData({
            totalDraws: state.totalDraws,
            totalFragments: state.totalFragments,
            statistics: state.statistics,
            hasLegendaryItems: state.hasLegendaryItems || false,
            hasTotalGoldItems: state.hasTotalGoldItems,
            hasTotalPurpleItems: state.hasTotalPurpleItems
          });

          console.log('已恢复抽奖状态:', state.totalDraws, '次抽奖');
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('加载抽奖状态失败:', error);
      return false;
    }
  },

  /**
   * 重置抽奖状态
   */
  resetDrawState: function() {
    try {
      const stateKey = `${PAGE_KEY}_state`;
      wx.removeStorageSync(stateKey);

      // 重新初始化统计数据
      this.initStatistics();

      wx.showToast({
        title: '抽奖记录已重置',
        icon: 'success'
      });

      console.log('抽奖状态已重置');
    } catch (error) {
      console.error('重置抽奖状态失败:', error);
    }
  },

  /**
   * 打开排行榜页面
   */
  openRanking: function() {
    // 保存当前抽奖状态
    this.saveDrawState();

    // 导航到排行榜页面
    wx.navigateTo({
      url: './ranking?activityType=treasure-hunting',
      fail: function(err) {
        console.error('打开排行榜失败:', err);
        wx.showToast({
          title: '打开排行榜失败',
          icon: 'none'
        });
      }
    });
  },
});