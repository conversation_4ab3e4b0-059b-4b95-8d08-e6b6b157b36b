// 抽奖模拟器页面逻辑
const app = getApp();
const api = require('../../../utils/api');
import PageWithVIP from '../../../utils/page-with-vip';

// 定义至尊夺宝抽奖活动的概率和奖品
const lotteryConfig = {
  // 至尊夺宝
  supertreasure: {
    name: '至尊夺宝',
    items: [
      { id: 1, name: '至曜·影虎(永久)', probability: 0.00001, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至曜·影虎.png', background: 'gold' },
      { id: 2, name: '至曜·玄武(永久)', probability: 0.00001, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至曜·玄武.png', background: 'gold' },
      { id: 3, name: '至尊·涅槃(永久)', probability: 0.00001, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至尊·涅槃.png', background: 'gold' },
      { id: 4, name: '至尊·火麒麟(永久)', probability: 0.00001, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至尊·火麒麟.png', background: 'gold' },
      { id: 5, name: '至曜·影虎(7天)', probability: 0.0039, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至曜·影虎.png', background: 'purple' },
      { id: 6, name: '至曜·玄武(7天)', probability: 0.0039, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至曜·玄武.png', background: 'purple' },
      { id: 7, name: '至尊·涅槃(7天)', probability: 0.0039, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至尊·涅槃.png', background: 'purple' },
      { id: 8, name: '至尊·火麒麟(7天)', probability: 0.0039, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至尊·火麒麟.png', background: 'normal' },
      { id: 9, name: '圣天使(永久)', probability: 0.00004, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/圣天使.png', background: 'gold' },
      { id: 10, name: '圣天使(7天)', probability: 0.0155, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/圣天使.png', background: 'normal' },
      { id: 11, name: '晶石(1个)', probability: 0.00004, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/晶石.png', background: 'purple' },
      { id: 12, name: '侍武之魂(永久)', probability: 0.0002, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/侍武之魂.png', background: 'gold' },
      { id: 13, name: '烈魂者(永久)', probability: 0.0002, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/烈魂者.png', background: 'gold' },
      { id: 14, name: '蓝隐神使(永久)', probability: 0.0002, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/蓝隐神使.png', background: 'gold' },
      { id: 15, name: '侍武之魂(7天)', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/侍武之魂.png', background: 'normal' },
      { id: 16, name: '烈魂者(7天)', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/烈魂者.png', background: 'normal' },
      { id: 17, name: '蓝隐神使(7天)', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/蓝隐神使.png', background: 'normal' },
      { id: 18, name: '虹刺(永久)', probability: 0.0023, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/虹刺.png', background: 'purple' },
      { id: 19, name: '雾夜之星(永久)', probability: 0.0023, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/雾夜之星.png', background: 'purple' },
      { id: 20, name: '虹刺(7天)', probability: 0.0619, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/虹刺.png', background: 'normal' },
      { id: 21, name: '雾夜之星(7天)', probability: 0.0619, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/雾夜之星.png', background: 'normal' },
      { id: 22, name: '至尊能量源(1个)', probability: 0.0039, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/至尊能量源.png', background: 'normal' },
      { id: 23, name: '高阶能量源(1个)', probability: 0.0077, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/高阶能量源.png', background: 'normal' },
      { id: 24, name: '普通能量源(1个)', probability: 0.0155, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/普通能量源.png', background: 'normal' },
      { id: 25, name: '效率宝珠LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/效率宝珠LV1.png', background: 'normal' },
      { id: 26, name: '重生宝珠LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/重生宝珠LV1.png', background: 'normal' },
      { id: 27, name: '坚韧宝珠LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/坚韧宝珠LV1.png', background: 'normal' },
      { id: 28, name: '后发宝珠LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/后发宝珠LV1.png', background: 'normal' },
      { id: 29, name: '复仇之钻LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/复仇之钻LV1.png', background: 'normal' },
      { id: 30, name: '闪电之钻LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/闪电之钻LV1.png', background: 'normal' },
      { id: 31, name: '攻击之钻LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/攻击之钻LV1.png', background: 'normal' },
      { id: 32, name: '复制之钻LV1', probability: 0.0387, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/复制之钻LV1.png', background: 'normal' },
      { id: 33, name: '点火装置+1', probability: 0.0774, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/点火装置+1.png', background: 'normal' },
      { id: 34, name: '进气系统+1', probability: 0.0774, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/进气系统+1.png', background: 'normal' },
      { id: 35, name: '燃料系统+1', probability: 0.0774, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/燃料系统+1.png', background: 'normal' },
      { id: 36, name: '引擎装置+1', probability: 0.0774, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/引擎装置+1.png', background: 'normal' },
      { id: 37, name: '悬挂系统+1', probability: 0.0774, image: 'https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/lottery/supertreasure/悬挂系统+1.png', background: 'normal' }
    ],
    cost: 3, // 单次抽奖消耗3元
    multiCost: 30 // 10连抽消耗30元
  }
};

// 页面KEY标识，确保唯一性
const PAGE_KEY = 'supertreasure';

PageWithVIP({
  data: {
    activityId: '',
    activity: null,
    backgroundImage: '/images/bg.jpg', // 确保使用绝对路径
    results: [], // 抽奖结果
    statistics: [], // 抽奖统计
    totalDraws: 0, // 总抽奖次数
    totalCost: 0, // 总消耗
    realCostYuan: '0.00', // 人民币花费
    totalTickets: 0, // 添加累计点券数量
    running: false, // 是否正在抽奖动画中
    showResult: false, // 是否显示结果
    isMultiDraw: false, // 是否是十连抽
    pageKey: PAGE_KEY,
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,
    showProbabilityPanel: false,
    showMoreProbability: false,
    showDrawDetails: false,
    hasGoldItems: false,
    hasPurpleItems: false,
    exchangeableCrystals: 0
  },

  onLoad: function(options) {
    console.log('至尊夺宝抽奖模拟器页面加载');

    // 直接使用supertreasure活动数据
    const activity = lotteryConfig.supertreasure;

    if (activity) {
      this.setData({
        activityId: 'supertreasure',
        activity,
        backgroundImage: '/images/bg.jpg'  // 使用绝对路径
      });

      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: activity.name
      });

      // 初始化页面数据
      this.initPageData();

      // 初始化VIP和免费次数
      this.initVipAndFreeCount();

      // 尝试加载保存的抽奖状态
      this.loadDrawState();
    } else {
      wx.showToast({
        title: '加载活动数据失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
          animationType: 'none'
        });
      }, 1500);
    }
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 初始化页面数据
    this.setData({
      backgroundImage: '/images/bg.jpg',
      pageKey: PAGE_KEY,
      isVip: false,
      freeCount: 0,
      vipRemainingDays: 0,
      showVipDialog: false,
    });
  },

  /**
   * 初始化VIP和免费次数
   */
  initVipAndFreeCount() {
    // 获取VIP状态
    const isVip = api.isVip();
    // 获取当前活动的免费次数
    const freeCount = api.getFreeCount(PAGE_KEY);
    // 获取VIP剩余天数
    const vipRemainingDays = api.getVipRemainingDays();

    this.setData({
      isVip: isVip,
      freeCount: freeCount,
      vipRemainingDays: vipRemainingDays
    });
  },

  /**
   * 更新VIP和免费次数状态
   */
  updateVipAndFreeCount() {
    const isVip = api.isValidVip();
    const freeCount = api.getFreeCount(PAGE_KEY);
    const vipRemainingDays = isVip ? api.getVipRemainingDays() : 0;

    this.setData({
      isVip,
      freeCount,
      vipRemainingDays
    });

    // 更新VIP徽章组件
    const vipBadge = this.selectComponent('#vipBadge');
    if (vipBadge) {
      vipBadge.updateVipStatus();
    }
  },

  /**
   * 添加物品到统计数据
   * @param {Object} item - 抽到的物品对象
   * @param {Number} increment - 表示增加的抽奖次数，默认为1
   */
  addToStatistics: function(item, increment = 1) {
    const statistics = this.data.statistics;
    let found = false;
    let addedTickets = 0;

    // 检查是否为点券类型奖励并计算点券数量
    if (item.name.includes('点券×')) {
      const ticketMatch = item.name.match(/点券×(\d+)/);
      if (ticketMatch && ticketMatch[1]) {
        addedTickets = parseInt(ticketMatch[1], 10);
      }
    }

    for (let i = 0; i < statistics.length; i++) {
      if (statistics[i].item.id === item.id) {
        statistics[i].count++;
        statistics[i].probabilityText = (item.probability * 100).toFixed(3) + '%';
        found = true;
        break;
      }
    }

    if (!found) {
      statistics.push({
        item: item,
        count: 1,
        percentage: '0.00',
        probabilityText: (item.probability * 100).toFixed(3) + '%'
      });
    }

    // 统计总抽奖次数（但不更新界面）
    const totalDraws = this.data.totalDraws + increment;

    // 重新计算所有项目的百分比
    for (let i = 0; i < statistics.length; i++) {
      statistics[i].percentage = (statistics[i].count / totalDraws * 100).toFixed(2);
    }

    // 按照抽取次数排序
    statistics.sort((a, b) => b.count - a.count);

    // 更新统计数据和点券数量
    this.setData({
      statistics: statistics,
      totalTickets: this.data.totalTickets + addedTickets
    });
  },

  /**
   * 批量更新统计数据
   * @param {Array} items - 抽到的物品数组
   */
  updateStatisticsBatch: function(items) {
    const statistics = this.data.statistics;
    const totalNewDraws = items.length;
    let totalAddedTickets = 0;

    // 先更新每个物品的计数并计算获得的点券
    items.forEach(item => {
      // 计算点券
      if (item.name.includes('点券×')) {
        const ticketMatch = item.name.match(/点券×(\d+)/);
        if (ticketMatch && ticketMatch[1]) {
          totalAddedTickets += parseInt(ticketMatch[1], 10);
        }
      }

      let found = false;
      for (let i = 0; i < statistics.length; i++) {
        if (statistics[i].item.id === item.id) {
          statistics[i].count++;
          statistics[i].probabilityText = (item.probability * 100).toFixed(3) + '%';
          found = true;
          break;
        }
      }

      if (!found) {
        statistics.push({
          item: item,
          count: 1,
          percentage: '0.00',
          probabilityText: (item.probability * 100).toFixed(3) + '%'
        });
      }
    });

    // 统计总抽奖次数（但不更新界面）
    const totalDraws = this.data.totalDraws + totalNewDraws;

    // 重新计算所有项目的百分比
    for (let i = 0; i < statistics.length; i++) {
      statistics[i].percentage = (statistics[i].count / totalDraws * 100).toFixed(2);
    }

    // 按照抽取次数排序
    statistics.sort((a, b) => b.count - a.count);

    this.setData({
      statistics: statistics,
      totalTickets: this.data.totalTickets + totalAddedTickets
    });
  },

  // 初始化统计数据，确保每个物品都在统计中显示
  initStatistics: function() {
    const statistics = [];
    const items = this.data.activity.items;

    for (let i = 0; i < items.length; i++) {
      statistics.push({
        item: items[i],
        count: 0,
        percentage: '0.00',
        probabilityText: (items[i].probability * 100).toFixed(3) + '%'
      });
    }

    // 按照抽取概率排序（从高到低）
    statistics.sort((a, b) => b.item.probability - a.item.probability);

    this.setData({
      statistics: statistics,
      totalDraws: 0,
      totalCost: 0,
      realCostYuan: '0.00',
      showResult: false,
      results: [],
      totalTickets: 0,
      exchangeableCrystals: 0
    });
  },

  /**
   * 计算可兑换晶石数量
   * @returns {number} 可兑换的晶石数量
   */
  calculateExchangeableCrystals: function() {
    return Math.floor(this.data.totalDraws / 150);
  },

  /**
   * 单抽按钮点击事件处理
   */
  singleDraw: function() {
    if (this.data.running) return;

    if (!this.canDraw(1)) return;

    this.setData({
      running: true,
      isMultiDraw: false,
      showResult: false
    });

    setTimeout(() => {
      // 调用抽奖函数获取单个结果
      const result = this.drawItem(true);

      // 使用单个结果更新统计
      this.addToStatistics(result);

      // 新增总抽数
      const newTotalDraws = this.data.totalDraws + 1;

      this.setData({
        results: [result], // 保存为数组，但只包含一个元素
        running: false,
        showResult: true,
        totalDraws: newTotalDraws,
        totalCost: this.data.totalCost + this.data.activity.cost,
        realCostYuan: ((this.data.totalCost + this.data.activity.cost) / 100).toFixed(2),
        exchangeableCrystals: Math.floor(newTotalDraws / 150)
      });

      this.checkRareItems();

      // 消费免费次数（非VIP用户）
      if(!this.data.isVip) {
        const newFreeCount = Math.max(0, this.data.freeCount - 1);
        this.setData({
          freeCount: newFreeCount
        });
        // 更新缓存，消耗1次
        this.updateFreeCountCache(-1);
      }
    }, 1000);
  },

  /**
   * 十连抽按钮点击事件处理
   */
  multiDraw: function() {
    if (this.data.running) return;

    if (!this.canDraw(10)) return;

    this.setData({
      running: true,
      isMultiDraw: true,
      showResult: false
    });

    setTimeout(() => {
      // 抽取10次
      const results = [];
      for (let i = 0; i < 10; i++) {
        results.push(this.drawItem(true));
      }

      // 批量更新统计
      this.updateStatisticsBatch(results);

      // 新增总抽数
      const newTotalDraws = this.data.totalDraws + 10;

      this.setData({
        results: results,
        running: false,
        showResult: true,
        totalDraws: newTotalDraws,
        totalCost: this.data.totalCost + this.data.activity.multiCost,
        realCostYuan: ((this.data.totalCost + this.data.activity.multiCost) / 100).toFixed(2),
        exchangeableCrystals: Math.floor(newTotalDraws / 150)
      });

      this.checkRareItems();

      // 消费免费次数（非VIP用户）
      if(!this.data.isVip) {
        const newFreeCount = Math.max(0, this.data.freeCount - 10);
        this.setData({
          freeCount: newFreeCount
        });
        // 更新缓存，消耗10次
        this.updateFreeCountCache(-10);
      }
    }, 1000);
  },

  /**
   * 检查是否可以抽奖
   * @param {number} count 需要的次数
   * @returns {boolean} 是否可以抽奖
   */
  canDraw(count = 1) {
    // 如果是VIP用户，可以无限抽奖
    if (this.data.isVip) {
      return true;
    }

    // 非VIP用户，检查免费次数是否足够
    if (this.data.freeCount < count) {
      // 显示VIP对话框
      this.setData({ showVipDialog: true });
      return false;
    }

    return true;
  },

  /**
   * VIP对话框关闭事件
   */
  onVipDialogClose: function() {
    this.setData({
      showVipDialog: false
    });
  },

  /**
   * 添加免费次数事件
   */
  onAddFreeAttempts: function() {
    // 添加免费次数（增加到100次）
    const newFreeCount = api.updateFreeCount(PAGE_KEY, 100);

    this.setData({
      freeCount: newFreeCount,
      showVipDialog: false
    });

    // 显示添加成功提示
    wx.showToast({
      title: '已添加100次免费机会',
      icon: 'success'
    });
  },

  /**
   * VIP徽章点击事件处理
   */
  handleVipBadgeClick() {
    if (!this.data.isVip) {
      this.setData({ showVipDialog: true });
    }
  },

  // 根据概率抽取一个物品
  drawItem: function(isSingleDraw) {
    // 随机数 0-1
    const random = Math.random();
    let cumulativeProbability = 0;

    for (let i = 0; i < this.data.activity.items.length; i++) {
      cumulativeProbability += this.data.activity.items[i].probability;
      if (random < cumulativeProbability) {
        const result = this.data.activity.items[i];
        return result;
      }
    }

    // 以防万一概率总和不为1
    const result = this.data.activity.items[this.data.activity.items.length - 1];
    return result;
  },

  // 关闭结果面板
  closeResult: function() {
    this.setData({
      showResult: false
    });
  },

  // 重置统计
  resetStats: function() {
    // 调用新的resetDrawState方法
    this.resetDrawState();
  },

  /**
   * 保存抽奖状态到本地存储
   */
  saveDrawState() {
    // 保存当前抽奖统计和重要状态数据
    const stateData = {
      statistics: this.data.statistics,
      totalDraws: this.data.totalDraws,
      totalCost: this.data.totalCost,
      realCostYuan: this.data.realCostYuan,
      totalTickets: this.data.totalTickets,
      exchangeableCrystals: this.data.exchangeableCrystals,
      hasGoldItems: this.data.hasGoldItems,
      hasPurpleItems: this.data.hasPurpleItems
    };

    try {
      wx.setStorageSync(`${PAGE_KEY}_state`, stateData);
      console.log('抽奖状态已保存');
    } catch (e) {
      console.error('保存抽奖状态失败:', e);
    }
  },

  /**
   * 从本地存储加载抽奖状态
   */
  loadDrawState() {
    try {
      const stateData = wx.getStorageSync(`${PAGE_KEY}_state`);
      if (stateData) {
        // 恢复保存的状态
        this.setData({
          statistics: stateData.statistics || [],
          totalDraws: stateData.totalDraws || 0,
          totalCost: stateData.totalCost || 0,
          realCostYuan: stateData.realCostYuan || '0.00',
          totalTickets: stateData.totalTickets || 0,
          exchangeableCrystals: stateData.exchangeableCrystals || 0,
          hasGoldItems: stateData.hasGoldItems || false,
          hasPurpleItems: stateData.hasPurpleItems || false
        });
        console.log('已加载保存的抽奖状态');
      } else {
        // 如果没有状态数据，则初始化统计
        this.initStatistics();
        console.log('没有找到保存的状态，已初始化统计');
      }
    } catch (e) {
      console.error('加载抽奖状态失败:', e);
      // 初始化统计
      this.initStatistics();
    }
  },

  /**
   * 重置抽奖状态并清除本地存储
   */
  resetDrawState() {
    // 初始化统计
    this.initStatistics();

    // 清除本地存储的状态
    try {
      wx.removeStorageSync(`${PAGE_KEY}_state`);
      console.log('已重置抽奖状态并清除缓存');

      wx.showToast({
        title: '已重置数据',
        icon: 'success'
      });
    } catch (e) {
      console.error('清除抽奖状态缓存失败:', e);
    }
  },

  /**
   * 页面关闭时保存状态
   */
  onUnload() {
    // 自动保存当前抽奖状态
    this.saveDrawState();
  },

  // 返回活动列表
  goBack: function() {
    wx.navigateBack({
      delta: 1,
      animationType: 'none'
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: `QQ飞车 ${this.data.activity.name} 抽奖模拟器`,
      path: '/pages/lottery/supertreasure/supertreasure'
    };
  },

  // 在页面显示时更新VIP信息
  onShow: function() {
    this.updateVipAndFreeCount();
  },

  /**
   * 抽取多个奖励
   * @param {number} count 抽取数量
   * @returns {Array} 抽取结果
   */
  drawItems: function(count) {
    const results = [];
    for (let i = 0; i < count; i++) {
      const result = this.drawItem(false);
      results.push(result);
    }

    // 批量更新统计数据
    this.updateStatisticsBatch(results);

    this.setData({
      results: results,
      totalDraws: this.data.totalDraws + count,
      totalCost: this.data.totalCost + this.data.activity.multiCost,
      realCostYuan: ((this.data.totalCost + this.data.activity.multiCost) / 100).toFixed(2)
    });

    return results;
  },

  /**
   * 更新免费次数缓存
   * @param {number} change 次数变化值
   */
  updateFreeCountCache: function(change = 0) {
    api.updateFreeCount(PAGE_KEY, change);
  },

  toggleProbabilityPanel() {
    this.setData({
      showProbabilityPanel: !this.data.showProbabilityPanel
    });
  },

  toggleMoreProbability() {
    this.setData({
      showMoreProbability: !this.data.showMoreProbability
    });
  },

  // 计算是否有金色和紫色奖励
  checkRareItems() {
    let hasGoldItems = false;
    let hasPurpleItems = false;

    for (const stat of this.data.statistics) {
      if (stat.count > 0) {
        if (stat.item.background === 'gold') {
          hasGoldItems = true;
        } else if (stat.item.background === 'purple') {
          hasPurpleItems = true;
        }
      }
    }

    this.setData({
      hasGoldItems: hasGoldItems,
      hasPurpleItems: hasPurpleItems
    });
  },

  /**
   * 打开排行榜页面
   */
  openRanking: function() {
    // 保存当前抽奖状态
    this.saveDrawState();

    // 导航到排行榜页面
    wx.navigateTo({
      url: './ranking?activityType=supertreasure',
      fail: function(err) {
        console.error('打开排行榜失败:', err);
        wx.showToast({
          title: '打开排行榜失败',
          icon: 'none'
        });
      }
    });
  },
});